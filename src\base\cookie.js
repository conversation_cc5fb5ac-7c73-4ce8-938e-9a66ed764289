const TokenKey = 'accessToken'
const RefreshTokenKey = 'refreshToken'
const localStorage = window.localStorage

export function getToken() {
  return localStorage.getItem(TokenKey)
}

export function getRefreshToken() {
  return localStorage.getItem(RefreshTokenKey)
}

export function setToken(data) {
  localStorage.setItem(TokenKey, data.accessToken)
  localStorage.setItem(RefreshTokenKey, data.refreshToken)
}

export function removeToken() {
  localStorage.removeItem(TokenKey)
  localStorage.removeItem(RefreshTokenKey)
}

export function setCookie(key, token) {
  return localStorage.setItem(key, token)
}
export function getCookie(key) {
  return localStorage.getItem(key)
}