import request from "@/base/service";

const requestP = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_PRODUCT_API,
    },
  });
};

const requestB = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_BASE_API,
    },
  });
};

// 创建评价
export function createComment(data) {
  return requestP({
    url: "/comment/create",
    method: "post",
    data,
  });
}

// 获取商品评价
export function getProductCommentPage(params) {
  return requestP({
    url: "/comment/getProductCommentPage",
    method: "get",
    params,
  });
}

// 上传图片
export function uploadFile(params) {
  return requestB({
    url: "/infra/file/upload",
    method: "get",
    params,
  });
}

// 点赞
export function like(params) {
  return requestP({
    url: "/comment/like",
    method: "get",
    params,
  });
}
