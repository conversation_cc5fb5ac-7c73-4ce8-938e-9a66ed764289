/*
 * @Author: zzw <EMAIL>
 * @Date: 2024-04-15 15:48:13
 * @LastEditors: zzw <EMAIL>
 * @LastEditTime: 2024-04-19 13:38:17
 * @FilePath: \mall-pc-dev-fl-v1.0-81567db8bdfadf81b1f43356e990c3bf2fe3c9ed\src\main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/* eslint-disable vue/multi-word-component-names */
import Vue from "vue";
import TDesign from "tdesign-vue";
// 引入组件库的少量全局样式变量
// import "tdesign-vue/es/style/index.css";
import 'tdesign-vue/dist/reset.css';
import "tdesign-icons-vue";
import "@/style/tenant-variables.less"; // 引入租户自定义变量，不在从本地获取
import "@/style/token.less";
import "@/style/common.less";
// import ElementUI from 'element-ui';
//样式文件需要单独引入
// import 'element-ui/lib/theme-chalk/index.css';

import router from "@/router";
import store from "@/store";
import * as API from "@/api";
import VueLazyload from "vue-lazyload";
import lazy from "@/assets/lazy.gif";
import "@/style/base.less";
import "@/style/reset.less";
import "@/directives"
import '@/components/index.js'
import bus from '@/utils/bus.js'
import filters from '@/utils/filters';
import { enableSupName, enableMarketPrice } from '@/utils/util'

Vue.prototype.enableSupName = enableSupName
Vue.prototype.enableMarketPrice = enableMarketPrice
Vue.prototype.$bus = bus
// Vue.use(ElementUI);
import './mock/mockServer';
import App from "./App.vue";

Vue.use(TDesign);

Vue.use(VueLazyload, {
  loading: lazy,
});

Vue.config.productionTip = false;

// 全局导入过滤器
Object.keys(filters ).forEach(key => {
  Vue.filter(key, filters [key])
});

new Vue({
  render: (h) => h(App),
  beforeCreate() {
    // Vue.prototype.$bus = this;
    Vue.prototype.$API = API;
  },
  router,
  store,
}).$mount("#app");
