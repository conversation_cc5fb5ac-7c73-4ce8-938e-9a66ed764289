import { reqGetC<PERSON>, reqUserRegister, reqUserLogOut } from '@/api'
import { getUserInfo } from '@/components/Header/api'
import { loginUser, loginSmsCode } from '@/views/Login/api'
import { setToken, removeToken } from '@/base/cookie'
import { getCount } from '@/views/ShopCart/api'
import {  getUnreadCount } from '@/components/Message/api'
import store from "@/store"

const state = {
  cartCount: 0,
  unreadCount: 0,
  code: '',
  userInfo: ''
}
const mutations = {
  GetCode(state, code) {
    state.code = code
  },
  UserInfo(state, userInfo) {
    state.userInfo = userInfo
  },
  Clear(state) {
    state.token = ''
    state.userInfo = {}
    removeToken()
  },
  SetCartCount(state, cartCount) {
    state.cartCount = cartCount
  },
  SetUnreadCount(state, count) {
    state.unreadCount = count
  }
}
const actions = {
  userLogin({ commit }, data) {
    return new Promise((resolve, reject) => {
      loginUser(data).then(res => {
        if (res.code === 0) {
          setToken(res.data)
          // 对比栏需要清掉
          localStorage.setItem('showCompare', false)
          resolve()
        } else {
          reject(res.msg)
        }
      }).catch(error => {
        reject(error)
      })
    })
  },
  smsLogin({ commit }, data) {
    return new Promise((resolve, reject) => {
      loginSmsCode(data).then(res => {
        if (res.code === 0) {
          setToken(res.data)
          // 对比栏需要清掉
          localStorage.setItem('showCompare', false)
          resolve()
        } else {
          reject(res.msg)
        }
      }).catch(error => {
        reject(error)
      })
    })
  },
  getUserInfo({ commit }) {
    return new Promise((resolve, reject) => {
      getUserInfo().then(res => {
        if(res.code === 0) {
          commit('UserInfo', res.data)
          sessionStorage.setItem('session-userinfo', JSON.stringify(res.data))
          store.dispatch('loadDefaultRegions')
          store.dispatch('loadUnreadCount')
        } else {
          reject(res.msg)
        }
      }).catch(error => {
        reject(error)
      })
    })
  },
  async loadUnreadCount({ commit }) {
    const res = await getUnreadCount()
    if(res.code === 0) {
      commit('SetUnreadCount', res.data)
      return res.data
    } else {
      return false
    }
  },
  async getCode({ commit }, phone) {
    const { data: res } = await reqGetCode(phone)
    // console.log(res);
    if(res.code == 200) {
      commit('GetCode', res.data)
    } else {
      return Promise.reject(new Error('faile'))
    }
  },
  async userRegister({ commit }, data) {
    const { data: res } = await reqUserRegister(data)
    if(res.code == 200) {
      return 'ok'
    } else {
      return Promise.reject(new Error('faile'))
    }
  },
  async userLogOut({ commit }) {
    const { data: res } = await reqUserLogOut()
    if(res.code == 200) {
      commit('Clear')
      commit('SetCartCount', 0)
      return 'ok'
    } else {
      return Promise.reject(new Error('faile'))
    }
  },
  async loadCartCount({ commit }) {
    const res = await getCount()
    if(res.code === 0) {
      commit('SetCartCount', res.data)
      return res.data
    } else {
      console.log('购物车数量查询失败...')
      return false
    }
  }
}
const getters = {}

export default {state, mutations, actions, getters}