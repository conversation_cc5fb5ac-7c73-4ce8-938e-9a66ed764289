<template>
  <div class="best-selling">
    <div class="title">
      热销商品
    </div>
    <div class="selling-wrap">
      <div class="selling-content" v-for="item in productList" :key="item.skuId" @click="jumpDetail(item)">
        <div>
          <t-image :src="item.imageUrl" style="width: 150px;height: 150px;"></t-image>
        </div>
        <div class="content">
          <div class="item-title" :title="item.skuName">{{ item.skuName }}</div>
          <div class="price">
            <span v-if="item.salePrice != -1" class="price">￥ {{ item.salePrice | formatMoney }}
              <span class="market-price" v-if="enableMarketPrice() && item.marketPrice">￥{{ (item.marketPrice || 0) |
                formatMoney }}</span>
            </span>
            <span v-else class="need-login" style="margin-top: 20px;; margin-bottom: 5px">登录后显示价格</span>
          </div>
        </div>
      </div>
      <div v-if="productList.length === 0 && !loading" class="nothing" style="height: 580px;">
        <t-image fit="cover" class="image" :src="emptySrc"></t-image>
      </div>
    </div>
  </div>
</template>

<script>
import { goodsSearchSimpleList } from '@/views/Search/api'
import { mapState } from 'vuex'

export default {
  name: 'BestSelling',
  data() {
    return {
      loading: false,
      emptySrc: require('@/assets/nothing.png'),
      productList: []
    }
  },
  computed: {
    ...mapState({
      defaultRegions: state => state.defaultAddress.defaultRegions
    })
  },
  methods: {
    // 保留两位小数
    saveTwoDecimal(val = 0) {
      return (Math.round(val * 100) / 100).toFixed(2)
    },
    async getData() {
      this.loading = true
      const res = await goodsSearchSimpleList({
        pageIndex: 1,
        pageSize: 10,
        sortType: 1,
        areaIds: this.defaultRegions.join(',')
      })
      this.loading = false
      if (res.code === 0) {
        this.productList = res.data || []
      }
    },
    jumpDetail(item) {
      this.$router.push(`/sku/${item.skuId}`)
    }
  },
  created() {
    this.getData()
  }
}
</script>

<style lang="less" scoped>
.best-selling {
  width: 1200px;
  margin: 35px auto 0;
  height: 640px;

  .title {
    color: #333;
    font-size: 20px;
    font-weight: 700;
  }

  .selling-wrap {
    margin-top: 20px;
    background-color: #f5f6f8;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .selling-content {
      width: 225px;
      padding: 30px 15px 20px;
      background-color: #fff;
      white-space: normal;
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      margin-bottom: 10px;
      .content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        .item-title {
          width: 100%;
          margin: 30px 0 10px 0;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          color: #333;
          &:hover {
            color: #e11b10;
          }
        }

        .price {
          font-size: 18px;
          color: #e11b10;
        }
      }

    }
  }
}
</style>