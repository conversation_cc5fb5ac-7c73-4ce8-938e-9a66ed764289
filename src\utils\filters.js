import * as orderUtil from '@/utils/orderUtil'
import * as utilMain from '@/utils/util'
const orderStatusInfo = (val) => {
    return orderUtil.getOrderLabel(val)
}

const orderAuditStatusInfo = (val) => {
    return orderUtil.getOrderAuditLabel(val)
}

const paymentMethodInfo = (val) => {
  return orderUtil.getPaymentMethodLabel(val)
}

const afterSaleWayInfo = (val) => {
  return orderUtil.getAfterSaleWayLabel(val)
}

const afterSaleReasonInfo = (val) => {
  return orderUtil.getAfterSaleReasonLabel(val)
}

const formatDate = (mills, format = 'yyyy/MM/dd') => {
  if(!mills) {
    return ''
  }
  let date = new Date(mills)
  return utilMain.formatDateTime(date, format)
}

const formatDateTime = (mills, format = 'yyyy/MM/dd HH:mm:ss') => {
  if(!mills) {
    return ''
  }
  let date = new Date(mills)
  return utilMain.formatDateTime(date, format)
}
 
const formatMoney = (val, digits = 2) => {
  if(!val && val !== 0) {
    val = 0
  }
  let t1 = Math.pow(10, digits)
  return ((val * t1)/t1).toFixed(digits)
}

const stockStateText = (val) => {
  const dicts = {
    33: '有货',
    36: '预订',
    39: '有货',
    40: '有货'
  }
  return dicts[val] || '无货'
}

const ycrhAuditStatusInfo = (val) => {
  return orderUtil.getYcrhAuditStatusLabel(val)
}

const ycrhAuditStatusStyle = (val) => {
  return orderUtil.getYcrhAuditStatusStyle(val)
}


export default { orderStatusInfo, orderAuditStatusInfo, paymentMethodInfo, formatDate, formatDateTime, afterSaleWayInfo, afterSaleReasonInfo, formatMoney, stockStateText,
  ycrhAuditStatusInfo, ycrhAuditStatusStyle
 }

