export const ORDER_STATUS_LIST = [
    { label: '待确认', value: 1 },
    { label: '待发货', value: 2 },
    { label: '已发货', value: 3 },
    // { label: '已送达', value: 4 },
    // { label: '已签收', value: 5 },
    { label: '待收货', value: 4 },
    { label: '待收货', value: 5 },
    { label: '待收货', value: 45 },    
    { label: '售后中', value: 6 },
    { label: '售后完成', value: 7 },
    { label: '已完成', value: 8 },
    { label: '已取消', value: 9 },
]
export const ORDER_AUDIT_STATUS_LIST = [
    { label: '未审批', value: 1 },
    { label: '审批中', value: 2 },
    { label: '审批驳回', value: 3 },
    { label: '审批通过', value: 4 }
]

export const ORDER_PAYMENT_METHOD_LIST = [
    { label: '账期支付', value: 1 },
    { label: 'VOP余额支付', value: 2 },
    { label: '线下支付', value: 3 },
    { label: '货到付款', value: 4 },
    { label: '线上支付', value: 5 },
    { label: '积分支付', value: 6 },
    { label: '混合支付', value: 7 }
]

export const AFTER_SALE_WAY_LIST = [
    { label: '仅退款', value: 10 },
    { label: '退货退款', value: 20 }
]

export const AFTER_SALE_REASON_LIST = [
    { label: '与商家协商一致退款', value: '100', way: [10,20] },
    { label: '拍错/多拍/不喜欢', value: '110', way: [10,20] },
    { label: '未按约定时间发货', value: '120', way: [10,20] },
    { label: '快递/物流一直未送到', value: '130', way: [10] },
    { label: '货物破损已拒签', value: '140', way: [10] },
    { label: '少件/漏发', value: '150', way: [10,20] },
    { label: '材质/面料与商品描述不符', value: '160', way: [20] },
    { label: '颜色/图案/款式等不符', value: '170', way: [20] },
    { label: '大小/尺寸/重量/厚度不符', value: '180', way: [20] },
    { label: '质量问题', value: '190', way: [20] },
    { label: '包装/商品破损/污渍', value: '200', way: [20] },
    { label: '假冒品牌', value: '210', way: [20] },
    { label: '卖家发错货', value: '220', way: [20] },
    { label: '发票问题', value: '230', way: [20] }
]

export const ORDER_ASSET_STATUS_LIST = [
    { label: '建档待处理', value: 0 },
    { label: '建档已推送', value: 1 },
    { label: '无须建档', value: 2 },
    { label: '建档中', value: 3 },
    { label: '建档完成', value: 4 },
    { label: '建档失败', value: 5 }
]

export const YCRH_AUDIT_STATUS_LIST = [
    { label: '审批中', value: '0', style: 'primary' },
    { label: '审批通过', value: '1', style: 'success' },
    { label: '审批驳回', value: '2', style: 'danger' }
]

export const getOrderLabel = (order) => {
    let obj = ORDER_STATUS_LIST.find(item => item.value === order.status)
    return obj ? obj.label : '--'
}

export const getOrderAuditLabel = (status) => {
    let obj = ORDER_AUDIT_STATUS_LIST.find(item => item.value === status)
    return obj ? obj.label : '--'
}

export const getPaymentMethodLabel = (status = 1) => {
    let obj = ORDER_PAYMENT_METHOD_LIST.find(item => item.value === status)
    return obj ? obj.label : '--'
}

export const getAfterSaleWayLabel = (status) => {
    let obj = AFTER_SALE_WAY_LIST.find(item => item.value === status)
    return obj ? obj.label : '--'
}

export const getAfterSaleReasonLabel = (status) => {
    let obj = AFTER_SALE_REASON_LIST.find(item => item.value === status)
    return obj ? obj.label : '--'
}

export const getYcrhAuditStatusLabel = (status) => {
    let obj = YCRH_AUDIT_STATUS_LIST.find(item => item.value === status)
    return obj ? obj.label : '--'
}

export const getYcrhAuditStatusStyle = (status) => {
    let obj = YCRH_AUDIT_STATUS_LIST.find(item => item.value === status)
    return obj ? obj.style : 'primary'
}