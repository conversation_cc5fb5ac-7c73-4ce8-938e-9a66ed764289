import request from '@/base/service'

const requestM = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_MEMBER_API
    }
  })
}

// 获得基本信息
export function getUserInfo(params) {
  return requestM({
    url: '/user/get',
    method: 'get',
    params
  })
}

// 修改密码
export function updatePassword(data) {
  return requestM({
    url: '/user/update-password',
    method: 'post',
    data
  })
}

// 获取退出登录信息
export function getSsoLogoutInfo(params) {
  return requestM({
    url: '/auth/sso-logout',
    method: 'get',
    params
  })
}

// 获取退出登录信息
export function getSsoLogoutInfoV2(params) {
  return requestM({
    url: '/auth/sso-logout-v2',
    method: 'get',
    params
  })
}
