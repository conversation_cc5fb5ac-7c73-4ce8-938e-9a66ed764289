<template>
  <section class="product-detail">
    <div class="category-list">
      <div class="category-title">{{ title }}</div>
      <div @click="toDetail(item)" v-for="(item, index) in suggestGoodsList" :key="index" class="category-item">
        <i class="number-icon" :class="'number-' + (index + 1)"></i>
        <t-image class="category-image" fit="cover" :src="item.imageUrl"></t-image>
        <div class="category-content">{{ item.skuName }}</div>
        <!-- <div class="category-price">￥{{ saveTwoDecimal(item.salePrice) }}</div> -->
        <div v-if="item.salePrice != -1" class="category-price">￥ {{ item.salePrice | formatMoney }}
          <span class="market-price" v-if="enableMarketPrice() && item.marketPrice">￥{{ (item.marketPrice || 0) |
            formatMoney }}</span>
        </div>
        <div v-else class="need-login">登录后显示价格</div>
      </div>
    </div>
    <div class="detail">
      <div class="list-name">
        <div class="tab-name" v-for="(item, index) in nameList" :key="item" @click="clickName(index)"
          :class="{ 'active-name': activeName === index }">
          {{ item }}
        </div>
      </div>
      <div class="product-content" v-if="activeName === 0">
        <book-content v-if="isContentBookExt" :detailInfo="detailData" />
        <div class="rich-content" v-if="!isContentBookExt && detailData.introducePc"
          v-html="detailData.introducePc.replace(/https:http:\/\//g, 'https://')"></div>
      </div>
      <div class="product-content" v-if="activeName === 1">
        <div class="description">
          <div class="label">供应商商品编码</div>
          <div class="content">{{ detailData.skuInnerId && detailData.skuInnerId.indexOf('mall-') >= 0 ?
            detailData.skuId : detailData.skuInnerId }}</div>
        </div>
        <!-- <template v-if="detailData.paramGroupAttrList && detailData.paramGroupAttrList[0] && detailData.paramGroupAttrList[0].paramAttributeList">
          <div class="description" v-for="item in detailData.paramGroupAttrList[0].paramAttributeList">
            <div class="label">{{ item.paramAttrName }}</div>
            <div class="content">{{ item.paramAttrValList[0][0] }}</div>
          </div>
        </template> -->
        <template v-if="detailData.paramGroupAttrList && detailData.paramGroupAttrList.length > 0">
          <template v-for="itemData in detailData.paramGroupAttrList">
            <template v-if="itemData.paramAttributeList && itemData.paramAttributeList.length > 0">
              <div class="description" v-for="(item, index) in itemData.paramAttributeList" :key="index">
                <div class="label">{{ item.paramAttrName }}</div>
                <div class="content">{{ parseValList(item.paramAttrValList) }}</div>
              </div>
            </template>
          </template>
        </template>
      </div>
      <div class="product-content" v-if="activeName === 2">
        <div class="description wareInfo" v-if="detailData.wareInfo">
          <div class="content wareInfo-content" v-for="(item, index) in detailData.wareInfo.split(',')" :key="index"
            style="border-left: 1px solid #e6e6e6;">
            {{ item }}
          </div>
        </div>
      </div>
      <!-- <div class="product-content" v-if="activeName === 3">
        <div class="after-sales">
          <div class="returned after-sales-item">
            <t-image class="image" alt="退货" :src="imageSrc1"></t-image>
            <div class="text">
              确认收货后<span class="prompt">7</span>日内出现质量问题可申请退货
            </div>
          </div>
          <div class="change after-sales-item">
            <t-image class="image" alt="换货" :src="imageSrc2"></t-image>
            <div class="text">
              确认收货后<span class="prompt">15</span>日内出现质量问题可申请更换
            </div>
          </div>
          <div class="protect after-sales-item">
            <t-image class="image" alt="质保" :src="imageSrc3"></t-image>
            <div class="text">质保期限<span class="prompt">1年</span></div>
          </div>
        </div>
      </div> -->
      <div class="product-content" v-if="activeName === 3">
        <div class="review-list">
          <template>
            <div class="nothing" v-if="commentList.length === 0 && !loading">
              <t-image fit="cover" class="image" :src="emptySrc"></t-image>
              <div class="text">暂无数据</div>
            </div>
            <div class="review-item" v-for="review in commentList" :key="review.id">
              <div class="user-info-rating">
                <div class="user-info">
                  <img v-show="review.avatar != null && review.avatar.length > 0" :src="review.avatar"
                    class="user-image" alt="User" />
                  <span class="user-name">{{ formatName(review.nickName) }}</span>
                </div>
                <div class="rating">
                  <t-rate :key="review.id" class="star" v-model="review.score" size="16px" />
                </div>
              </div>

              <div class="review-content">
                <p>{{ review.content }}</p>

                <div class="image-gallery">
                  <img v-for="(image, index) in review.pics ? review.pics.split(',') : [] " :key="index" :src="image"
                    class="product-thumbnail" :class="{ selected: selectedImage === image }"
                    @click="selectImage(review, index)" alt="Product Thumbnail" />
                </div>

                <div v-if="review.selectedImage" class="selected-image-container">
                  <img :src="review.selectedImage" class="selected-image" alt="Selected Product Image" />
                </div>
              </div>

              <div class="review-footer">
                <span class="date">{{ review.createTime | formatDate('yyyy-MM') }}</span>
                <div class="review-actions">
                  <div class="like-container">
                    <thumb-up-1-icon class="like-icon" @click="clickLike(review)" size="large" />
                    <span>{{ review.likeCount }}</span>
                  </div>
                  <div class="reply-container">
                    <chat-message-icon class="reply-icon" @click="clickReply(review)" size="large" />
                    <span>{{ review.replyCount }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div v-show="total > 0">
              <t-pagination class="pagination" :total="total" :showPageNumber="true" :showPageSize="false"
                showPreviousAndNextBtn :totalContent="false" @current-change="changePage" />
            </div>
          </template>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import {
  ThumbUp1Icon,
  ChatMessageIcon
} from 'tdesign-icons-vue';
import { getProductCommentPage, like } from '@/views/Center/comment/api'
import BookContent from '@/views/Detail/components/BookContent'
export default {
  name: "Detail",
  components: { BookContent, ThumbUp1Icon, ChatMessageIcon },
  data() {
    var nameList = ["商品介绍", "规格参数", "包装清单"]
    const configData = this.$store.state.Home.configData || {}
    if (configData.productCommentSwitch) {
      nameList.push("商品评价")
    }

    return {
      emptySrc: require('@/assets/nothing.png'),
      title: '品目销量排行',
      imageSrc1: require("assets/detail/returned.png"),
      imageSrc2: require("assets/detail/change.png"),
      imageSrc3: require("assets/detail/protect.png"),
      // nameList: ["商品介绍", "规格参数", "包装清单", "售后参数", "商品评价"],
      nameList: nameList,
      activeName: 0,
      commentList: [],
      total: 0,
      page: 1,
      columns: [
        { colKey: 'score', width: 100, title: '商品评分' },
        { colKey: 'content', title: '内容' },
        { colKey: 'pics', title: '图片' },
        { colKey: 'anonymousFlag', title: '是否匿名' },
        { colKey: 'createTime', title: '创建时间' }
      ],
      totalPages: 6,
      currentPage: 1,
      selectedImage: null,
    };
  },
  props: {
    detailData: {
      type: Object,
      default: () => {
        return {
          suggestGoodsList: []
        }
      }
    },
    suggestGoodsList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  computed: {
    isContentBookExt() {
      return this.detailData.bookExtInfo && this.detailData.bookExtInfo.isbn && true
    }
  },
  created() {
    // 如果是店铺商详页
    // this.title = '店铺热销'
    this.getProductCommentPage()
  },
  methods: {
    formatName(nickName) {
      if (nickName != null && nickName.length > 2) {
        const startChar = nickName[0]; // 获取第一个字符
        const endChar = nickName[nickName.length - 1]; // 获取最后一个字符
        const middleStars = '*'.repeat(nickName.length - 2); // 中间部分用星号替换
        return `${startChar}${middleStars}${endChar}`;
      } else if (nickName != null && nickName.length === 2) {
        return nickName.substring(0, 1) + '*';
      } else {
        return "匿名";
      }
    },
    // 保留两位小数
    saveTwoDecimal(val = 0) {
      return (Math.round(val * 100) / 100).toFixed(2)
    },
    clickName(index) {
      this.activeName = index;
      if (index === 3) {
        // 评价
        this.getProductCommentPage()
      }
    },
    // 获取评价详细信息
    async getProductCommentPage() {
      const res = await getProductCommentPage({
        skuId: this.detailData.skuId || this.detailData.skuInnerId,
        pageNo: this.page,
        pageSize: 10
      })
      if (res.code === 0 && res.data.list) {
        this.commentList = res.data.list
        this.total = Number(res.data.total)
      }
    },
    changePage(page) {
      this.page = page
      this.getProductCommentPage()
    },
    toDetail(item) {
      this.$router.push(`/sku/${item.skuId}`)
    },
    parseValList(arr) {
      const result = arr.map(item => item[0])
      return result.join('，')
    },
    selectImage(review, imageIndex) {
      if (review.pics != null && review.pics.length > 0) {
        const images = review.pics.split(',');
        if (imageIndex < images.length) {
          // 使用 Vue.set 确保是响应式更新
          this.$set(review, 'selectedImage', images[imageIndex]);
        }
      }
    },
    async clickReply(review) {
      this.$message.info('稍后开放')
    },
    async clickLike(review) {
      if (!review.likeClicked) {
        const res = await like({
          id: review.id
        })
        if (res.code === 0) {
          review.likeCount = review.likeCount + 1;
          this.$set(review, 'likeClicked', true);
        }
      }
      else {
        this.$message.info('您已点赞')
      }
    }
  },
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.product-detail {
  width: 1200px;
  margin: 0 auto 0;
  overflow: hidden;
  display: flex;

  .category-list {
    width: 230px;
    height: 1500px;
    padding-bottom: 10px;
    background-color: #fff;
    border: 1px solid #E9EBF2;

    .category-title {
      height: 46px;
      line-height: 46px;
      font-weight: bold;
      font-size: 16px;
      border-bottom: 1px solid #E9EBF2;
      background-color: #f5f6f8;
      padding: 0 20px;
    }

    .category-item {
      margin-top: 16px;
      padding: 20px;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;

      .number-icon {
        background: url(assets/detail/number.png) no-repeat;
        position: absolute;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        z-index: 10;
        left: 20px;
        top: 0;
      }

      .number-1 {
        background-position: 0 0;
      }

      .number-2 {
        background-position: 0 -42px;
      }

      .number-3 {
        background-position: 0 -84px;
      }

      .number-4 {
        background-position: 0 -126px;
      }

      .number-5 {
        background-position: 0 -168px;
      }

      .category-image {
        width: 150px;
        height: 150px;
      }

      .category-content {
        margin: 10px;
        height: 40px;
        line-height: 20px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        width: 200px;

        &:hover {
          color: #e11b10;
        }
      }

      .category-price {
        font-size: 18px;
        height: 24px;
        color: #e11b10;
      }
    }
  }

  .detail {
    width: calc(100% - 253px);
    margin-left: 24px;
    border: 1px solid #E9EBF2;
    align-self: flex-start;

    .list-name {
      display: flex;
      align-items: center;
      height: 46px;
      background-color: #f5f6f8;
      font-weight: bold;
      font-size: 16px;
      padding: 0 20px;

      .tab-name {
        padding: 0 60px 0 0;
        cursor: pointer;

        &:hover {
          color: #e11b10;
        }
      }

      .active-name {
        position: relative;

        &:after {
          position: absolute;
          left: 0;
          top: 0;
          content: "";
          height: 32px;
          right: 0;
          margin-right: 60px;
          border-bottom: 2px solid #e11b10;
        }
      }
    }

    .product-content {
      padding: 20px;
      background-color: #fff;
      min-height: 160px;

      .rich-content {
        img {
          max-width: 100% !important;
        }
      }

      .description {
        display: flex;

        &:last-child {
          border-bottom: 1px solid #e6e6e6;
        }

        .label {
          width: 260px;
          padding: 15px 0;
          text-align: center;
          background-color: #f8f8f8;
          border-top: 1px solid #e6e6e6;
          border-left: 1px solid #e6e6e6;
          border-right: 1px solid #e6e6e6;
        }

        .content {
          flex: 1;
          padding: 15px 30px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          border-top: 1px solid #e6e6e6;
          border-right: 1px solid #e6e6e6;
        }

        .wareInfo-content {
          white-space: wrap;
        }
      }

      .wareInfo {
        flex-direction: column;
      }

      .after-sales {
        display: flex;

        .after-sales-item {
          border-right: none;
          padding: 24px 0 24px 15px;
          display: flex;
          align-items: center;
          border: 1px solid #E9EBF2;
        }

        .returned {
          width: 358px;
        }

        .change {
          width: 372px;
        }

        .protect {
          width: 170px;
        }

        .image {
          width: 50px;
          height: 60px;
          margin-right: 10px;
        }

        .text {
          font-size: 14px;
          color: #333333;

          .prompt {
            color: #db8d3a;
            font-weight: bold;
            margin: 0 2px;
          }
        }
      }

      .nothing-wrapper {
        .nothing {
          width: 369px;
          height: 200px;
          background: url(assets/nothing.png) no-repeat;
          margin: 40px auto;
        }

        .nothing-text {
          width: 100%;
          text-align: center;
          margin-top: 30px;
          font-size: 16px;
        }
      }
    }

    .review-list {
      width: 100%;
      margin: auto;
      font-family: Arial, sans-serif;
    }

    .review-item {
      border-bottom: 1px solid #ddd;
      padding: 16px 0;
    }

    .user-info-rating {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .user-info {
      display: flex;
      align-items: center;
    }

    .user-image {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 8px;
    }

    .user-name {
      margin-right: 8px;
    }

    .rating .star {
      color: #ddd;
    }

    .rating .star.filled {
      color: #ff9900;
    }

    .review-content {
      margin-top: 16px;
    }

    .image-gallery {
      display: flex;
      gap: 16px;
      margin-top: 16px;
    }

    .product-thumbnail {
      width: 60px;
      height: 60px;
      object-fit: cover;
      cursor: pointer;
      border: 2px solid transparent;
    }

    .product-thumbnail.selected {
      border: 2px solid #ff9900;
    }

    .selected-image-container {
      margin-top: 12px;
      position: relative;
    }

    .image-controls {
      position: absolute;
      top: 8px;
      left: 8px;
    }

    .image-controls button {
      background-color: #fff;
      border: 1px solid #ccc;
      margin-right: 4px;
      padding: 4px;
      cursor: pointer;
    }

    .selected-image {
      width: 100%;
      max-width: 300px;
      transition: transform 0.3s;
    }

    .review-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 16px;
      color: #888;
      font-size: 14px;
      height: 30px;
    }

    .review-actions {
      display: flex;
      gap: 32px;
    }

    .like-container {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .reply-container {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    button {
      background: none;
      border: none;
      cursor: pointer;
      font-size: 14px;
    }

    .pagination {
      display: flex;
      justify-content: center;
      margin-top: 16px;
    }

    .pagination button {
      margin: 0 4px;
      padding: 4px 8px;
      background-color: #f0f0f0;
      border: 1px solid #ddd;
      cursor: pointer;
    }

    .pagination button.active {
      background-color: #ff9900;
      color: #fff;
    }
  }
}
</style>
