<template>
  <div class="preferred-supplier">
    <div class="title">
      优选电商
    </div>
    <div class="data-list">
      <div class="data-item" v-for="(item, index) in supplierList" :key="index" @click="clickHandler(item)">
        <t-image
          :src="item.logoUrl || defaultSupplierLogoSrc"
          fit="contain"
          :style="{ width: '180px', height: '90px', background: '#fff' }"
        />
      </div>
    </div>
  </div>
</template>

<script>

import { mapState } from 'vuex'

export default {
  name: "PreferredSupplier",
  data() {
      return {
          defaultSupplierLogoSrc: require('@/assets/defaultSupplierLogo.png')
      };
  },
  computed: {
    ...mapState({
      defaultRegions: state => state.defaultAddress.defaultRegions,
      supplierList: state => state.Home.supplierList
    })
  },
  methods: {
    clickHandler(item) {
      this.$router.push(`/supplier?id=${item.id}`);
    },
  }
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.preferred-supplier {
  width: 1200px;
  margin: 20px auto 10px;
  .title {
    color: #333;
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
  }

  .data-list {
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: start;
    flex-wrap: wrap;
    .data-item {
      width: 220px;
      height: 110px;
      margin: 10px;
      padding: 15px;
      box-shadow: 0 1px 8px rgba(169,169,169,0.4);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
