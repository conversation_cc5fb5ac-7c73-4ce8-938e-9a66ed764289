<template>
  <div>
    <div class="block-title">请填写验收人信息</div>
    <t-row :gutter="16">
      <t-col :span="4">
        <t-form-item label="验收人姓名" name="accepterName" :rules="[{ required: true, message: '验收人姓名必选', type: 'error' }]">
          <t-select-input
            :value="accepterForm.accepterName"
            :popup-visible="accepterPopupVisible"
            style="width: 100%"
            placeholder="请输入验收人姓名，只允许本单位非本人"
            clearable
            allow-input
            @popup-visible-change="onAccepterPopupVisibleChange"
            @input-change="onAccepterInputChange"
            @clear="onAccepterClear"
          >
            <template #panel>
              <ul class="tdesign-demo__select-input-ul-single">
                <li v-for="item in comAccepterList" :key="item.value" @click="() => onAccepterOptionClick(item)">
                  {{ item.label }}
                </li>
              </ul>
            </template>
            <template #suffixIcon>
              <t-icon name="chevron-down" />
            </template>
          </t-select-input>
        </t-form-item>
      </t-col>
      <t-col :span="4">
        <t-form-item label="验收人手机号" name="accepterMobile" :rules="[
          { required: true, message: '验收人手机号必选', type: 'error' },
          { pattern: /^1[3,4,5,6,7,8,9][0-9]{9}$/, message: '验收人手机号格式不正确', type: 'error'}]">
          <t-input v-model="accepterForm.accepterMobile" :maxlength="15" placeholder="请输入验收人手机号" />
        </t-form-item>
      </t-col>
      <t-col :span="4" v-if="false">
        <t-form-item label="验收人邮箱" name="accepterEmail" :ruels="[
          { email: { ignore_max_length: false }, message: '验收人邮箱格式不正确', type: 'error' }
        ]">
          <t-input v-model="accepterForm.accepterEmail" :maxlength="50" placeholder="请输入验收人姓名" />
        </t-form-item>
      </t-col>
    </t-row>
  </div>
</template>

<script>
import * as cacheApi from '@/utils/cache'
export default {
  name: 'approveAccepter',
  props: {
    value: {
      type: Object,
      default: ()=> {}
    }
  },
  data() {
    return {
      accepterForm: {
        accepterName: '',
        accepterMobile: '',
        accepterEmail: ''
      },
      accepterList: [],
      accepterPopupVisible: false
    }
  },
  watch: {
    value: {
      deep: true,
      handler(newVal, oldVal) {
        this.accepterForm = newVal
      },
    },
    accepterForm: {
      deep: true,
      handler() {
        this.$emit('input', this.accepterForm)
      }
    }
  },
  computed: {
    comAccepterList() {
      return this.accepterList.map(item => {
        return {
          label: item.name,
          value: item.name
        }
      })
    }
  },
  mounted() {
    this.initAccepter()
  },
  methods: {
   saveAccepter2cache() {
      if(!this.accepterForm.accepterName) {
        return
      }
      const object = {
        id: this.accepterForm.accepterName,
        name: this.accepterForm.accepterName,
        mobile: this.accepterForm.accepterMobile
      }

      cacheApi.saveObject('approve-accepter', object)
    },
    initAccepter() {
      this.accepterList = cacheApi.getObjects('approve-accepter')
      if(this.accepterList.length) {
        const obj = this.accepterList[0]
        this.accepterForm.accepterName = obj.name
        this.accepterForm.accepterMobile = obj.mobile
      }
    },
    onAccepterPopupVisibleChange(val) {
      if(!this.accepterList.length) {
        this.accepterPopupVisible = false
        return
      }
      this.accepterPopupVisible = val
    },
    onAccepterClear() {
      this.accepterForm.accepterName = ''
      this.accepterForm.accepterMobile = ''
      this.accepterForm.accepterEmail = ''
    },
    onAccepterOptionClick(item) {
      if(this.accepterList.length) {
        let obj = this.accepterList.find(opt => opt.id === item.value)
        if(obj) {
          this.accepterForm.accepterName = obj.name
          this.accepterForm.accepterMobile = obj.mobile
        }
      }
      this.accepterPopupVisible = false
    },
    onAccepterInputChange(val, context) {
      this.accepterForm.accepterName = val
    }
  }
}
</script>