<template>
  <div class="book-content-con">
    <div class="p-parameter" v-if="bookExt.isbn">
      <ul class="parameter2 p-parameter-list">
        <li :title="bookExt.publishers">出版社：{{bookExt.publishers}}</li>
        <li :title="bookExt.isbn">ISBN：{{bookExt.isbn}}</li>
        <li :title="bookExt.batchNo" v-if="bookExt.batchNo">版次：{{bookExt.batchNo}}</li>
        <li :title="bookExt.publishers" >商品编码：{{ detailInfo.skuInnerId }}</li>
        <li :title="bookExt.publishers" v-if="bookExt.publishers">品牌：{{bookExt.bookBrand || bookExt.publishers}}</li>
        <li :title="bookExt.bookPackage" v-if="bookExt.bookPackage">包装：{{bookExt.bookPackage}}</li>
        <li :title="bookExt.publishers">开本：16开</li>
        <li :title="bookExt.publishTime" v-if="bookExt.publishTime">出版时间：{{bookExt.publishTime}}</li>
        <li :title="bookExt.papers" v-if="bookExt.papers">用纸：{{bookExt.papers}}</li>
        <li :title="bookExt.pagesNumber" v-if="bookExt.pagesNumber">页数：{{bookExt.pagesNumber}}</li>
        <li :title="bookExt.language" v-if="bookExt.language">正文语种：{{bookExt.language}}</li>
      </ul>
    </div>
    <div class="p-content" v-if="bookExt.productFeatures"> 
      <div class="p-title">
        <h3>产品特色</h3>
      </div>
      <div class="p-mc" v-html="bookExt.productFeatures" />
    </div>
    <div class="p-content" v-if="bookExt.contentDesc"> 
      <div class="p-title">
        <h3>内容简介</h3>
      </div>
      <div class="p-mc" v-html="bookExt.contentDesc" />
    </div>
    <div class="p-content" v-if="bookExt.editorDesc"> 
      <div class="p-title">
        <h3>作者简介</h3>
      </div>
      <div class="p-mc" v-html="bookExt.editorDesc" />
    </div>
    <div class="p-content" v-if="bookExt.bookCatalogue"> 
      <div class="p-title">
        <h3>目录</h3>
      </div>
      <div class="p-mc" v-html="bookExt.bookCatalogue" />
    </div>
    <div class="p-content" v-if="bookExt.bookAbstract"> 
      <div class="p-title">
        <h3>精彩书摘</h3>
      </div>
      <div class="p-mc" v-html="bookExt.bookAbstract" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'BookContent',
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    bookExt() {
      return this.detailInfo.bookExtInfo
    }
  },
  methods: {}
}
</script>

<style lang="less" scoped>
.book-content-con {
  padding: 20px;
  background-color: #fff;
  min-height: 160px;

  .p-parameter {
    padding: 0 10px 10px;
    ul {
      overflow: hidden;
      li {
        padding-left: 42px;
        float: left;
        margin-bottom: 5px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 200px;
      }
    }

    .p-parameter-list {
      padding: 20px 0 15px;
      border-bottom: 1px dotted #ddd;
      margin-top: -1px;
    }
  }

  .p-content {
    overflow: hidden;
    margin: 0 20px;
    padding-top: 20px;
    .p-title {
      height: 24px;
      h3 {
        line-height: 28px;
        width: 99px;
        height: 25px;
        overflow: hidden;
        color: #fff;
        background-color: gray;
        padding-left: 20px;
        font-weight: bold;
        font-size: 14px;
      }
    }
    .p-mc {
      padding: 18px 10px 0;
      line-height: 24px;
      font-size: 14px;
      font-family: pingfangSC;
      color: #757575;
    }
  }
}
</style>