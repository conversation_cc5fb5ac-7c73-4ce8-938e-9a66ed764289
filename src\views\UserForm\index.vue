
<template>
    <div class="footer">
      <div class="top-box">
        <div class="logo-left">
          <img src="@/assets/login/logo.png" alt="" @click="jumpHome" />
        </div>
        <div class="y_line"></div>
        <div style="color: #909090;font-size: 28px;">用户认证</div>
      </div>
      <div class="content-box">
        <div class="form-box">
            <span class="form-text">重设密码</span>
            <t-form
              :data="dataForm"
              @submit="onSubmit"
              status-icon
              :rules="rules"
              ref="dataForm"
              class="demo-dataForm"
            >
              <div class="form-com" v-if="false">
                <img src="@/assets/login/<EMAIL>" alt="" />
                <span>附属单位:</span>
              </div>
              <t-form-item name="deptName" v-if="false">
                <t-select v-model="dataForm.deptName" placeholder="请选择附属单位" style="width:100%;">
                  <t-option label="区域一" value="shanghai"></t-option>
                  <t-option label="区域二" value="beijing"></t-option>
                </t-select>
              </t-form-item>
              <div class="form-com" v-if="false">
                <img src="@/assets/login/Frame@3x(1).png" alt="" />
                <span>工号:</span>
              </div>
              <t-form-item name="userNo" v-if="false" :rules="[
                {required: true, message: '请输入工号', type: 'blur'}
              ]">
                <t-input
                  v-model="dataForm.userNo"
                  autocomplete="off"
                  :maxlength="50"
                ></t-input>
              </t-form-item>
              <div class="form-com">
                <img src="@/assets/login/Frame@3x(2).png" alt="" />
                <span>手机号:</span>
              </div>
              <t-form-item name="mobile">
                <t-input
                  v-model="dataForm.mobile"
                  autocomplete="off"
                  :maxlength="13"
                ></t-input>
              </t-form-item>
              <div class="form-com">
                <img src="@/assets/login/<EMAIL>" alt="" />
                <span>验证码:</span>
              </div>
              <t-form-item name="verifyCode">
                <div class="custom-verification-code">
                  <t-input v-model="dataForm.verifyCode" placeholder="请输入验证码" :maxlength="8"></t-input>
                  <t-button @click="sendVerificationCode" :disabled="sendDisabled" theme="primary">{{sendText}}</t-button>
                </div>
              </t-form-item>
              <div class="form-com">
                <img src="@/assets/login/Frame@3x(3).png" alt="" />
                <span>密码:</span>
              </div>
              <t-form-item name="newPassword" help="密码要求8位至20位,必须包含大小写字母数字及@$!%*?&等符号符号">
                <t-input
                  type="password"
                  v-model="dataForm.newPassword"
                  autocomplete="new-password"
                  :maxlength="30"
                ></t-input>
              </t-form-item>
              <div class="form-com">
                <img src="@/assets/login/Frame@3x(3).png" alt="" />
                <span>确认密码:</span>
              </div>
              <t-form-item name="newPassword2">
                <t-input
                  type="password"
                  v-model="dataForm.newPassword2"
                  :maxlength="60"
                  autocomplete="new-password"
                ></t-input>
              </t-form-item>
              <t-form-item>
                <div style="width:100%;display:flex;align-items: center;justify-content: center;">
                  <t-button type="submit" :loading="submitLoading" style="width: 200px;">确认并设置</t-button>
                </div>
              </t-form-item>
            </t-form>
            <div style="display: flex; justify-content: flex-end;">
              <span style="color: #f22e00; cursor: pointer;" @click="toLogin">返回登录</span>
            </div>
          </div>
      </div>
      <div class="lower-box">
        <span>Copyright@2024 武汉大学工会版权所有</span>
        <span>技术支持:湖北金一禾技术服务有限公司</span>
        <span>技术咨询热线：18302720011 工作日：09:00-17:30</span>
      </div>
    </div>
  </template>

  <script>
  import * as authApi from '@/views/Login/api'
  import { encryptSm2 } from '@/utils/util';

  export default {
    components: {},
    data() {
      return {
        dataForm: {},
        rules: {
          mobile: [
            {required: true, message: '请输入手机号', type: 'blur'}
          ],
          verifyCode: [
            {required: true, message: '请输入验证码', type: 'blur'}
          ],
          newPassword: [
            { required: true, message: '请输入密码', type: 'blur'},
            { pattern: /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,20}$/, message: '密码要求长度8-20位，必须包含数字、字母及特殊字符', type: 'blur'}
          ],
          newPassword2: [
            {required: true, message: '请输入密码', type: 'blur'},
            {validator: (val) => this.dataForm.newPassword === val, message: '两次密码不一致', type: 'blur'}
          ]
        },
        submitLoading: false,
        initSeconds: 61,
        smsSeconds: 61
      };
    },
    computed: {
      sendDisabled() {
        return this.smsSeconds < this.initSeconds
      },
      sendText() {
        if(this.smsSeconds === this.initSeconds) {
          return '发送验证码'
        }
        return `剩余${this.smsSeconds}秒`
      }
    },
    methods: {
      validateMobile(mobile) {
        return /^1[3-9]\d{9}$/.test(mobile)
      },
      onSubmit({ validateResult, firstError }) {
        if (validateResult === true) {
          let params = {
            mobile: this.dataForm.mobile,
            code: this.dataForm.verifyCode,
            password: encryptSm2(this.dataForm.newPassword),
            crypto: 'sm2'
          }
          if(!this.validateMobile(params.mobile)) {
            this.$message.warning('手机号格式不正确');
            return
          }
          authApi.resetPassword(params).then(res => {
            if(res.code === 0) {
              this.$message.success('密码设置成功');
              this.$router.push('/login')
            }
          })
        } else {
          console.log("Errors: ", validateResult);
          this.$message.warning(firstError);
        }
      },
      sendVerificationCode() {
        if (!this.dataForm.mobile) {
          this.$message.warning('手机号格式不能为空');
          return
        }
        if (!this.validateMobile(this.dataForm.mobile)) {
          this.$message.warning('手机号格式不正确');
          return
        }

        // 发送短信验证码...
        let params = {
          mobile: this.dataForm.mobile,
          scene: 3
        }
        authApi.sendSmsCode(params).then(res => {
          if (res.code === 0) {
            this.$message.info('短信验证码发送成功');

            let func = () => {
              if (this.smsSeconds === 0) {
                this.smsSeconds = this.initSeconds
                return
              }
              this.smsSeconds--
              setTimeout(func, 1000)
            }
            this.smsSeconds--
            setTimeout(func, 1000)
          }
        }).catch(e => {
          this.smsSeconds = this.initSeconds
        })
      },
      toLogin() {
        this.$router.push({ path: '/login' });
      },
      jumpHome() {
        this.$router.push('/home');
      }
    },
    created() { },
    mounted() { },
  };
  </script>
  <style lang="less" scoped>
  @import "@/style/variables.less";
  .footer {

    .custom-verification-code {
        display: flex;
        align-items: center;
        width: 100%;
      }
      .custom-verification-code .t-input__wrap {
        flex: 1;
        margin-right: 10px;
      }
  }
  </style>
  <style lang="less" scoped>
  @import "@/style/variables.less";
  .footer {
    .t-form__item{
      margin-bottom: 15px;
    }
    .top-box {
      display: flex;
      align-items: center; /* 图片在垂直方向上居中 */
      justify-content: center;
      width: 1300px;
      height: 90px;
      line-height: 90px;
      margin: 10 auto;
      .logo-left {
        display: flex;
        align-items: center; /* 图片在垂直方向上居中 */
        justify-content: center;
      }
      img {
        line-height: 90px;
      }
      .y_line {
        height: 60px;
        width: 0;
        margin: 0 25px;
        border-left: 1px solid #c7c7c7;
      }
    }

    .form-com {
      display: flex;
      align-items: center; /* 图片在垂直方向上居中 */
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 5px;
      margin-top: 20px;
      img {
        width: 20px;
        height: 20px;
      }
      span {
        margin-left: 5px;
      }
    }
    .form-text {
      margin: 10px 0px;
      display: block;
      width: 100%;
      text-align: center;
      font-weight: bold;
      font-size: 24px;
      color: #f22e00;
    }
    .content-box {
      width: 1200px; /* 设置元素的宽度 */
      height: 710px; /* 设置元素的高度 */
      margin: 0 auto;
      .form-box {
        margin: 0 auto;
        width: 550px;
        height: 416px;
        background-color: #fff;
        border-radius: 10px;
        padding: 20px;
      }
    }
    .lower-box {
      width: 1200px;
      text-align: center;
      margin: 0 auto;
      margin-top: 40px;
      span {
        display: block;
        margin-bottom: 5px
      }
    }
  }
  </style>
