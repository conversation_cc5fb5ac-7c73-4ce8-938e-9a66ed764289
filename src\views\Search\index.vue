<template>
  <div class="search-container" v-loading="loading">
    <!--bread-->
    <div class="bread">
      <div>全部结果</div>

      <template v-if="queryData.keyword">
        <div class="segmentation">></div>
        <div class="segmentation">{{ queryData.keyword }}</div>
      </template>
      <template v-if="categoryAggList[0]">
        <div class="segmentation">></div>
        <t-select class="selectCategory" v-model="categoryAggList[0].categoryId" @change="changeCategory(0)" clearable>
          <t-option
            v-for="item in categoryList1"
            :key="item.categoryId"
            :value="item.categoryId"
            :label="item.categoryName"
          />
        </t-select>
      </template>
      <template v-if="categoryAggList[1]">
        <div class="segmentation">></div>
        <t-select class="selectCategory" v-model="categoryAggList[1].categoryId" @change="changeCategory(1)" clearable>
          <t-option
            v-for="item in categoryList2"
            :key="item.categoryId"
            :value="item.categoryId"
            :label="item.categoryName"
          />
        </t-select>
      </template>
      <template v-if="categoryAggList[2]">
        <div class="segmentation">></div>
        <t-select class="selectCategory" v-model="categoryAggList[2].categoryId" @change="changeCategory(2)" clearable>
          <t-option
            v-for="item in categoryList3"
            :key="item.categoryId"
            :value="item.categoryId"
            :label="item.categoryName"
          />
        </t-select>
      </template>
    </div>

    <!--selector-->
    <SearchSelector
      v-if="selectorList.length > 0 || brandAggList.length > 0 || supplierAggList.length > 0"
      ref="selector"
      :selectorList="selectorList"
      :brandAggList="brandAggList"
      :supplierAggList="supplierAggList"
      @checkCategory="checkCategory"
      @checkBrand="checkBrand"
      @checkSupplier="checkSupplier"
    ></SearchSelector>

    <!-- overview -->
    <div class="overview">
      <div class="left">
        <Address @changeAddress="changeAddress" class="address-comp"></Address>
        <div class="order" :class="{checked: sortType == 1}" @click="sales">销量优先排序</div>
        <div class="order prices" @click="prices">
          <div :class="{checked: iconType !== ''}">价格排序</div>
          <div class="icon">
            <caret-up-small-icon :class="{ checked: iconType == 'up' }" />
            <caret-down-small-icon :class="{ checked: iconType == 'down' }" />
          </div>
        </div>
        <t-range-input class="range" v-model="ranges" @change="changeNum" :placeholder="placeholder" clearable />
        <t-button theme="primary" class="confirm" @click="confirm"> 确定 </t-button>
      </div>
      <div class="right">
        <div class="text">共<span class="red">{{ total }}</span>件商品</div>
        <t-pagination
          class="pagination"
          :total="total"
          v-model="pageIndex"
          :pageSize="pageSize"
          :showPageNumber="false"
          :showPageSize="false"
          showPreviousAndNextBtn
          :showJumper="false"
          :totalContent="false"
          @current-change="onCurrentChange"
        />
      </div>
    </div>

    <!-- result -->
    <Result :defaultRegions="areaIds && areaIds.split(',')" :resultData="resultData" :loading="loading"></Result>
    <t-pagination
      v-if="resultData.length > 0"
      :total="total"
      v-model="pageIndex"
      :showJumper="false"
      :pageSize="pageSize"
      :totalContent="false"
      page-ellipsis-mode="both-ends"
      @current-change="onCurrentChange"
      :showPageSize="false" />
  </div>
</template>

<script>
import { CaretDownSmallIcon, CaretUpSmallIcon } from "tdesign-icons-vue";
import { goodsSearchPageListV1, goodsSearchPageListV2 } from '@/views/Search/api'
import { getRootCategoryList, getChildCategoryTreeList } from '@/views/Home/api'
import SearchSelector from "./components/SearchSelector";
import Result from "./components/Result";
import { mapState } from 'vuex'
export default {
  name: "Search",
  components: {
    SearchSelector,
    Result,
    CaretUpSmallIcon,
    CaretDownSmallIcon,
  },
  data() {
    return {
      categoryList1: [],
      categoryList2: [],
      categoryList3: [],
      selectorList: [],
      brandAggList: [],
      supplierAggList: [],
      iconType: "",
      placeholder: "￥",
      ranges: [],
      pageIndex: 1,
      pageSize: 20,
      total: 0,
      resultData: [],
      categoryAggList: [],
      sortType: 1,
      minPrice: null,
      maxPrice: null,
      areaIds: null,
      queryData: this.$route.query,
      loading: true
    };
  },
  computed: {
    ...mapState({
      configData: state => state.Home.configData
    }),
  },
  methods: {
    changeNum(val) {
      this.ranges = val.map(item => {
        if (item) {
          item = String(item.replace(/[^\d.]/g, ''))
        }
        return item
      })
    },
    // 查询大类
    async getRootCategoryList() {
      if (this.categoryList1.length > 0) {
        return
      }
      const res = await getRootCategoryList()
      if (res.code == 0) {
        this.categoryList1 = res.data
      }
    },
    // 查询二三级类目
    async queryChildCategory(params) {
      if (this.categoryList3.length > 0) {
        return
      }
      const res = await getChildCategoryTreeList(params)
      if (res.code == 0) {
        this.categoryList2 = res.data || []
        if (this.categoryAggList[1]) {
          const temp = res.data.find(x => x.categoryId == this.categoryAggList[1].categoryId) || {}
          this.categoryList3 = temp.childCategoryList || []
        }
      }
    },
    changeAddress(val = []) {
      this.areaIds = val.length > 0 ? val.join(',') : null
      this.getData()
    },
    prices() {
      if (this.iconType == 'down') {
        this.iconType = 'up'
        this.sortType = 2
      } else {
        this.iconType = 'down'
        this.sortType = 3
      }
      this.getData()
    },
    sales() {
      this.iconType = ''
      this.sortType = 1
      this.getData()
    },
    confirm() {
      if (this.ranges[0] && this.ranges[1] && (this.ranges[1] - this.ranges[0] <= 0)) {
        const temp = this.ranges[0]
        this.$set(this.ranges, 0, this.ranges[1])
        this.$set(this.ranges, 1, temp)
      }
      this.minPrice = this.ranges[0] || null
      this.maxPrice = this.ranges[1] || null
      this.getData()
    },
    checkCategory(item) {
      if (item.cateLevel == 1) {
        this.categoryAggList = [item]
        this.categoryList2 = []
        this.categoryList3 = []
        this.$router.replace({
          query: {
            keyword: this.queryData.keyword,
            categoryId1: item.categoryId
          }
        })
      } else if (item.cateLevel == 2) {
        this.categoryAggList[1] = item
        this.categoryAggList[2] = null
        this.categoryList3 = []
        this.$router.replace({
          query: {
            keyword: this.queryData.keyword,
            categoryId2: item.categoryId
          }
        })
      } else if (item.cateLevel == 3) {
        this.categoryAggList[2] = item
        this.$router.replace({
          query: {
            keyword: this.queryData.keyword,
            categoryId3: item.categoryId
          }
        })
      }
    },
    checkBrand(item) {
      if (item.brandId) {
        this.$router.replace({
          query: {
            ...this.queryData,
            brandId: item.brandId
          }
        })
      } else {
        delete this.queryData.brandId
        const a = { ...this.queryData }
        this.$router.replace({
          query: {
            ...this.queryData,
            otherParams: true // 不加这个$route.query不会变
          }
        })
      }
    },
    checkSupplier(item) {
      if (item.supplierId) {
        this.$router.replace({
          query: {
            ...this.queryData,
            supplierId: item.supplierId
          }
        })
      } else {
        delete this.queryData.supplierId
        const a = { ...this.queryData }
        this.$router.replace({
          query: {
            ...this.queryData,
            otherParams: true // 不加这个$route.query不会变
          }
        })
      }
    },
    onCurrentChange(val) {
      this.pageIndex = val
      this.getData()
    },
    changeCategory(type) {
      if (!this.categoryAggList[type].categoryId) {
        // 清空，取上一级
        if (type == 0) {
          this.$router.replace({
            query: {
              keyword: this.queryData.keyword
            }
          })
          return
        }
        type = type - 1
      }
      if (type == 0) {
        this.$router.replace({
          query: {
            keyword: this.queryData.keyword,
            categoryId1: this.categoryAggList[0].categoryId
          }
        })
      } else if (type == 1) {
        this.$router.replace({
          query: {
            keyword: this.queryData.keyword,
            categoryId2: this.categoryAggList[1].categoryId
          }
        })
      } else if (type == 2) {
        this.$router.replace({
          query: {
            keyword: this.queryData.keyword,
            categoryId3: this.categoryAggList[2].categoryId
          }
        })
      }
    },
    async getData() {
      this.loading = true
      const { categoryId1, categoryId2, categoryId3, keyword, isSelected, brandId, supplierId, tagIds } = this.queryData
      const res = await goodsSearchPageListV2({
        categoryId1,
        categoryId2,
        categoryId3,
        keyword,
        brandId,
        supplierId,
        pageIndex: this.pageIndex,
        pageSize: this.pageSize,
        areaIds: this.areaIds,
        minPrice: this.minPrice,
        maxPrice: this.maxPrice,
        sortType: this.sortType,
        tagIds
      })
      if (res.code == 0) {
        if (res.data.pageResult.total > 0) {
          this.total = Number(res.data.pageResult.total)
          this.resultData = res.data.pageResult.list
        } else {
          this.total = 0
          this.resultData = []
        }
        if (!res.data.categoryAggList) {
          res.data.categoryAggList = []
        }
        if (!keyword) {
          this.brandAggList = res.data.brandAggList || []
          this.supplierAggList = res.data.supplierAggList || []
          // 不是通过关键词查询进的搜索页
          if (!categoryId1 && !categoryId2 && !categoryId3) {
            if (isSelected) {
              // 首页优选电商进来的
              await this.getRootCategoryList()
              this.selectorList = []
              if(res.data.categoryAggList) {
                this.selectorList = res.data.categoryAggList.filter(x => x.cateLevel == 1) || []
              }
            }
            this.loading = false
            return;
          }
          // 类目搜索
          if (res.data.categoryAggList && res.data.categoryAggList.length > 0) {
            this.categoryAggList = res.data.categoryAggList.sort((a, b) => a.cateLevel - b.cateLevel)
          } else {
            this.categoryAggList = [{
              categoryId: ''
            }, {
              categoryId: ''
            }, {
              categoryId: ''
            }]
          }

          let fucn = (level, cateId) => {
            let arr = res.data.categoryAggList.filter(item => item.cateLevel === level) || []
            if(cateId) {
              arr = arr.filter(item => item.categoryId === parseInt(cateId)) || []
            }
            if(arr.length) {
              return arr[0]
            }

            return {categoryId: ''}
          }

          if (categoryId3) {
            this.selectorList = []
            this.categoryAggList = [fucn(1, categoryId1), fucn(2, categoryId2), fucn(3, categoryId3)]
          } else if (categoryId2) {
            this.selectorList = res.data.categoryAggList.filter(x => x.cateLevel == 3) || []
            this.categoryAggList = [fucn(1, categoryId1), fucn(2, categoryId2)]
          } else if (categoryId1) {
            this.selectorList = res.data.categoryAggList.filter(x => x.cateLevel == 2) || []
            this.categoryAggList = [fucn(1, categoryId1)]
          }

          this.getRootCategoryList()
          if (this.categoryAggList[0] && this.categoryAggList[0].categoryId) {
            this.categoryList3 = []
            this.queryChildCategory({
              parentCategoryId: this.categoryAggList[0].categoryId
            })
          }
        } else {
          // 关键字搜索进的搜索页
          let num = 1
          if (categoryId1) {
            num = 2
          }
          if (categoryId2) {
            num = 3
          }
          if (categoryId3) {
            num = 4
          }
          if (res.data.categoryAggList) {
            // 后台的接口可能返回cateLevel为0的数据
            if (this.categoryList1.length == 0) {
              this.categoryList1 = res.data.categoryAggList.filter(x => x.cateLevel == 1).map(x => {
                return {
                  categoryName: x.cateName,
                  categoryId: x.categoryId,
                  cateLevel: x.cateLevel
                }
              })
            }
            if (this.categoryList2.length == 0) {
              this.categoryList2 = res.data.categoryAggList.filter(x => x.cateLevel == 2).map(x => {
                return {
                  categoryName: x.cateName,
                  categoryId: x.categoryId,
                  cateLevel: x.cateLevel
                }
              })
            }
            if (this.categoryList3.length == 0) {
              this.categoryList3 = res.data.categoryAggList.filter(x => x.cateLevel == 3).map(x => {
                return {
                  categoryName: x.cateName,
                  categoryId: x.categoryId,
                  cateLevel: x.cateLevel
                }
              })
            }
          } else {
            this.categoryList1 = []
            this.categoryList2 = []
            this.categoryList3 = []
            this.categoryAggList = []
          }
          if (num == 4) {
            this.selectorList = []
          } else if (res.data.categoryAggList) {
            this.selectorList = res.data.categoryAggList.filter(x => x.cateLevel == num) || []
          }
          this.brandAggList = res.data.brandAggList || []
          this.supplierAggList = res.data.supplierAggList || []
        }
      }
      this.loading = false
    }
  },
  watch: {
    $route: {
      handler(newVal, oldVal) {
        this.queryData = this.$route.query
        delete this.queryData.otherParams
        if (!this.queryData.categoryId3) {
          this.categoryAggList[2] = undefined
          if (!this.queryData.categoryId2) {
            this.categoryAggList[1] = undefined
            if (!this.queryData.categoryId1) {
              this.categoryAggList[0] = undefined
            }
          }
        }
        if (!this.queryData.isSelected) {
          this.selectorList = []
        }
        if (!this.queryData.brandId) {
          this.brandAggList = []
        }
        if (!this.queryData.supplierId) {
          this.supplierAggList = []
        }
        this.pageIndex = 1
        this.getData()
      },
    },
  }
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.search-container {
  width: 1200px;
  position: relative;
  margin: 0 auto 16px;
  background-color: #fff;

  .bread {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin: 24px 0 12px;
    .segmentation {
      margin: 0 8px;
    }
    .selectCategory {
      width: 160px;
    }
  }
  .overview {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    height: 60px;
    border: 1px solid #E9EBF2;
    border-radius: 4px;
    .left {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-left: 24px;
      .address-comp {
        width: 300px;
        margin-right: 16px;
      }
      .order {
        width: 85px;
        margin: 0 6px;
        color: #333;
        cursor: pointer;
        &:hover {
          color: #e11b10;
        }
      }
      .prices {
        display: flex;
        align-items: center;
        .icon {
          margin-left: 8px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #666;
          font-size: 12px;
        }
      }
      .range {
        margin-left: 8px;
        width: 200px;
      }
      .confirm {
        border: none!important;
        margin-left: 16px;
      }
    }
    .right {
      width: 260px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .red {
        color: #e11b10;
        margin: 0 4px;
      }
      .text {
        margin-right: 16px;
      }
      .pagination {
        width: 80px;
      }
    }
  }
  .checked {
    color: #e11b10!important;
  }
}
</style>
