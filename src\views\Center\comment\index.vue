<template>
  <div class="comment-wrapper">
    <div class="title">
      <h3>评价订单</h3>
      <h5 style="margin-top: 6px;">订单号：{{ orderData.no }}</h5>
    </div>
    <div class="content-wrap">
      <div class="product">
        <img style="width: 100px;height: 100px;" :src="skuInfo.imageUrl" alt="" />
        <div class="name">{{ skuInfo.skuName }}</div>
      </div>
      <div class="content">
        <t-form :data="formData" :rules="rules" ref="form" class="comment-form" @submit="onSubmit">
          <t-form-item label="商品评分" name="score">
            <t-rate v-model="formData.score" />
          </t-form-item>
          <t-form-item label="评价晒单" name="content">
            <t-textarea
              v-model="formData.content"
              placeholder="分享体验心得，给万千想买的人一个参考~"
              name="description"
              :autosize="{ minRows: 3, maxRows: 5 }"
              :maxlength="300"
            />
          </t-form-item>
          <t-form-item label="匿名评价" name="score">
            <t-switch v-model="anonymousFlag" :customValue="[1, 0]" :label="['是', '否']"></t-switch>
          </t-form-item>
          <t-form-item label="图片上传">
            <t-upload 
              ref="uploadRef"
              :action="uploadFileUrl"
              v-model="files"
              :sizeLimit="{ size: 5, unit: 'MB', message: '图片大小不超过5MB' }"
              :autoUpload="true"
              :showImageFileName="true"
              @fail="handleFail"
              theme="image"
              tips='图片大小5M以内，最多只能上传 5 张图片'
              :headers="headers"
              :format-response="formatResponse"
              :before-upload="beforeUpload"
              multiple
              :max="5"
              accept="image/*"/>
          </t-form-item>
          <t-form-item style="margin-left: 200px">
            <t-space size="10px">
              <t-button class="footer-btn" theme="primary" type="submit">发表</t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </div>
    </div>
  </div>
</template>

<script>
import { createComment, getProductCommentPage } from '@/views/Center/comment/api'
import { getDetailOrder } from '@/views/Trade/api'
import { getToken } from '@/base/cookie'

export default {
  data() {
    return {
      formData: {
        score: 0,
        content: ''
      },
      orderNo: '',
      orderData: {},
      skuInfo: {},
      files: [],
      anonymousFlag: 1,
      rules: {
        score: [
          { required: true,  message: '商品评分必选',type: 'error' }
        ]
      },
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/infra/file/upload", // 请求地址
      headers: { Authorization: "Bearer " + getToken() }
    }
  },
  created() {
    this.orderNo = this.$route.query.no
    this.getDetailOrder()
  },
  methods: {
    async getProductCommentPage() {
      const res = await getProductCommentPage({
        skuId: this.$route.query.skuId,
        orderId: this.orderData.id,
        pageNo: 1,
        pageSize: 100
      })
      // 强跳转过去的也应该提示一下并能返回。
      if (res.code === 0 && res.data.total > 0) {
        this.$message.info('该商品已评价')
        this.$router.push('/center/myorder')
      }
    },
    async getDetailOrder() {
      const res = await getDetailOrder({
        orderNo: this.orderNo || ''
      })
      if (res.code === 0) {
        this.orderData = res.data
        const skuId = this.$route.query.skuId
        if (res.data.skuInfoList && res.data.skuInfoList.length > 0) {
          this.skuInfo = res.data.skuInfoList.find(item => item.skuId == skuId)
        }
        this.getProductCommentPage()
      }
    },
    onSubmit({ validateResult, firstError }) {
      if (validateResult === true) {
        this.createComment()
      } else {
        this.$message.warning(firstError)
      }
    },
    // 提示上传文件失败
    beforeUpload(file) {
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$message.error(`文件 ${file.name} 大小超过5MB，上传失败`);
      }
      return isLt5M;
    },
    async createComment() {
      const params = {
        orderId: this.orderData.id,
        orderNo: this.orderData.no,
        skuId: this.$route.query.skuId,
        spuId: this.$route.query.skuId || '',
        supplierId: this.orderData.supplierId,
        score: this.formData.score, // 评价 1-5
        content: this.formData.content, // 评价内容
        anonymousFlag: this.anonymousFlag // 是否匿名 1-是 0 否
      }
      if(this.files.length > 0) {
        params.pics = this.files.map(item => item.url).join(',')
      }
      const res = await createComment(params)
      if (res.code === 0) {
        this.$message.success('评价成功')
        this.$router.go(-1) // 返回上一页
      }
    },
    handleFail({ file }) {
      this.$message.error(`文件 ${file.name} 上传失败`);
    },
    formatResponse(res) {
      return { url: res.data };
    }
  }
}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.comment-wrapper {
  width: 83.33%;
  .title {
    width: 100%;
    text-align: center;
  }
  .content-wrap {
    display: flex;
    // height: 440px;
    .product {
      width: 240px;
      border-right: 1px solid #ccc;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 0 24px;
      .name {
        margin-top: 12px;
      }
    }
    .content {
      flex: 1;
      margin-top: 20px;
      padding-left: 12px;
    }
    .footer-btn {
      margin-top: 20px;
      width: 300px;
    }
  }
  .image-pics {
    width: 48px;
    height: 48px;
    margin-right: 8px;
    object-fit: cover;
  }
  /deep/.t-upload__card-name {
    width: 108px;
  }
}
</style>
