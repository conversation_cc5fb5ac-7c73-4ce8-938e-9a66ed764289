<template>
  <div class="compare-main" v-loading="exportLoading">
    <div class="title">
      <h3>
        <span>商品对比</span>
        <t-button @click="exportPDF">导出pdf</t-button>
      </h3>
    </div>
    <div class="compare-goods" ref="compare">
      <div class="compare-header"  v-show="exportLoading">
        <div class="compare-title">
          <span>商品比较</span>
        </div>
        <div class="compare-date">{{ exportTime }}</div>
      </div>
      <table class="compare-table1">
        <tbody>
          <tr>
            <th>
              <div class="opt-checkbox" v-show="!exportLoading">
                <t-checkbox v-model="highFlag">高亮不同项</t-checkbox>
                <t-checkbox v-model="hiddenFlag" style="margin-top: 24px;">隐藏相同项</t-checkbox>
              </div>
            </th>
            <td v-for="item in 4">
              <template v-if="productList[item - 1]">
                <div class="goods-item">
                  <div class="p-img">
                    <img :src="productList[item - 1].imageUrl" width="160" height="160" />
                  </div>
                  <div class="p-slider">
                    <ImageList :sliderPicUrls="productList[item - 1].sliderPicUrls || []"
                      @showImage="showImage($event, productList[item - 1])"></ImageList>
                  </div>
                  <div class="p-name">{{ productList[item - 1].skuName }}</div>
                  
                  <div class="p-price">
                    <div class="p-symbol">¥</div>
                    <div>{{ productList[item - 1].salePrice }}</div>
                  </div>
                  <div class="p-btnbox" v-show="!exportLoading">
                    <a class="btn-primary" @click="toDetail(productList[item - 1])">立即查看</a>
                    <a class="btn-del" @click="delItems(item - 1)" v-if="productList.length !== 1">删除</a>
                  </div>
                </div>
              </template>
              <template v-else></template>
            </td>
          </tr>
          <tr v-for="item in compareUnit" v-show="!(hiddenFlag && !compareDiff(item.value))">
            <th>
              <div class="tb-title">{{ item.label }}</div>
            </th>
            <td v-for="i in 4" :class="{ 'differ': highFlag && compareDiff(item.value) }">
              <div class="goods-item" v-if="compareInfo[i - 1]">
                <template v-if="item.value.includes('price') || item.value.includes('Price')">￥</template>{{ compareInfo[i - 1][item.value] || '--' }}
              </div>
              <div v-else></div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import { skuPriceCompare } from '@/views/Detail/api'
import ImageList from "@/views/Compare/compare-image-list"
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

export default {
  name: "Compare",
  data() {
    return {
      productList: JSON.parse(localStorage.getItem('compareProduct')) || [],
      compareInfo: [],
      compareUnit: [
        { label: '品牌名称', value: 'brandName' },
        { label: '供应商', value: 'supplierName' },
        { label: '市场价', value: 'marketPrice' },
        { label: '销售价', value: 'salePrice' },
        // { label: '单位', value: 'unit'},
      ],
      highFlag: false,
      hiddenFlag: false,
      valueList: [],
      exportLoading: false,
      exportTime: ''
    };
  },
  components: {
    ImageList
  },
  created() {
    this.toCompare()
    this.exportTime = this.getCurrentDate()
  },
  methods: {
    getCurrentDate() {
      const today = new Date();
      const year = today.getFullYear();
      const month = (today.getMonth() + 1).toString().padStart(2, '0'); // 月份从0开始，需加1，并确保两位数
      const day = today.getDate().toString().padStart(2, '0'); // 确保两位数
      return `${year}-${month}-${day}`;
    },
    showImage(url, item) {
      item.imageUrl = url
    },
    async toCompare() {
      const res = await skuPriceCompare(this.productList.map(x => x.skuId))
      if (res.code === 0) {
        this.valueList = []
        this.compareInfo = []
        res.data.forEach(item => {
          let valueData = {}
          if (item.specValueList) {
            item.specValueList.forEach(x => {
              valueData[x.specName] = x.specValue || '--'
              this.valueList.push(x.specName)
            })
          }
          this.compareInfo.push({
            ...item,
            ...valueData
          })
        })
        this.valueList = Array(...new Set(this.valueList)).map(x => {
          return {
            label: x,
            value: x
          }
        })
        this.compareUnit = this.compareUnit.concat(...this.valueList)
      }
    },
    toDetail(item) {
      if (!item) {
        return
      }
      let ctxPath = process.env.VUE_APP_PATH_CONTEXT
      window.open(location.origin + ctxPath + `/#/sku/${item.skuId}`, '_blank')
    },
    delItems(i) {
      this.productList.splice(i, 1)
      this.compareInfo.splice(i, 1)
      localStorage.setItem('compareProduct', JSON.stringify(this.productList))
    },
    // 判断是否相同，如果是全部相同，返回false
    compareDiff(value) {
      const arr = this.compareInfo.map(x => x[value])
      if (arr.length >= 2 && arr.every((value, index, array) => index === 0 ? true : value === array[0])) {
        return false
      }
      return true
    },
    exportPDF() {
      const dom = this.$refs.compare
      this.exportLoading = true
      this.$nextTick(() => {
        html2canvas(dom, {
          allowTaint: true,
          useCORS: true,
          dpi: window.devicePixelRatio * 4, // 将分辨率提高到特定的DPI 提高四倍
          scale: 4, // 按比例增加分辨率
          logging: false,
          async: true,
          backgroundColor: null
        }).then((canvas) => {
          const pdf = new jsPDF('p', 'mm', 'a4')
          var ctx = canvas.getContext('2d')
          var a4w = 190; var a4h = 277 // A4大小，210mm x 297mm，四边各保留10mm的边距，显示区域190x277
          var imgHeight = Math.floor(a4h * canvas.width / a4w) // 按A4显示比例换算一页图像的像素高度
          var renderedHeight = 0

          while (renderedHeight < canvas.height) {
            var page = document.createElement('canvas')
            page.width = canvas.width
            page.height = Math.min(imgHeight, canvas.height - renderedHeight)// 可能内容不足一页

            // 用getImageData剪裁指定区域，并画到前面创建的canvas对象中
            page.getContext('2d').putImageData(ctx.getImageData(0, renderedHeight, canvas.width, Math.min(imgHeight, canvas.height - renderedHeight)), 0, 0)
            pdf.addImage(page.toDataURL('image/jpeg', 1.0), 'JPEG', 10, 10, a4w, Math.min(a4h, a4w * page.height / page.width)) // 添加图像到页面，保留10mm边距

            renderedHeight += imgHeight
            if (renderedHeight < canvas.height) {
              pdf.addPage()// 如果后面还有内容，添加一个空页
            }
          }
          // 保存PDF  
          pdf.save(`${this.$store.state.Home.configData.title}商品比价 - ${this.exportTime}.pdf`);
          this.exportLoading = false
        })
      })
    }
  }
};
</script>

<style lang="less" scoped>
.compare-main {
  padding-top: 20px;
  padding-bottom: 29px;
  width: 1200px;
  margin: 0 auto;

  //标题
  .title {
    margin-bottom: 22px;
    border: 1px solid #ddd;

    h3 {
      font-size: 16px;
      background-color: #f7f7f7;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 16px;
      height: 50px;
    }
  }

  .compare-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 60px;
  }

  .compare-title {
    flex-grow: 1;
    text-align: center;
    font-size: 30px;
    font-weight: bold;
  }

  .compare-date {
    font-size: 22px;
    font-weight: bold;
    text-align: right;
  }


  .compare-goods {
    background-color: #fff;
  }

  .compare-table1 {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    border-top: 1px solid #e4e4e4;
    border-left: 1px solid #e4e4e4;
    margin-top: -1px;
  }

  .compare-table1 td,
  .compare-table1 th {
    border-right: 1px solid #e4e4e4;
    border-bottom: 1px solid #e4e4e4;
    line-height: 24px;
    background: #fff;
    padding: 20px 0 16px;

    .goods-item {
      width: 210px;
      margin: 0 auto;

      .p-img {
        width: 100%;
        height: 160px;
        text-align: center;
      }

      .p-slider {
        position: relative;
        width: 100%;
      }

      .p-name {
        width: 100%;
        line-height: 16px;
        height: 32px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        color: #666;
        font-size: 12px;
        margin-top: 10px;
      }

      .p-price {
        line-height: 28px;
        margin-top: 10px;
        display: flex;
        font-weight: bold;
        color: #e11b10;
        font-size: 16px;

        .p-symbol {
          font-size: 12px;
        }
      }

      .p-btnbox {
        position: relative;
        width: 100%;
        height: 24px;
        margin-top: 10px;

        .btn-primary {
          border-radius: 0;
          width: 80px;
          height: 24px;
          line-height: 24px;
          padding: 0;
          background-color: #df3033;
          color: #fff;
          text-align: center;
          padding: 4px;
          cursor: pointer;
        }

        .btn-del {
          display: none;
          position: absolute;
          right: 0;
          top: 2px;
          font-size: 12px;
          cursor: pointer;
          &:hover {
            color: #df3033;
          }
        }
      }
      &:hover {
        .btn-del {
          display: inline!important;
        }
      }
    }
  }

  .compare-table1 th {
    text-align: left;
    font-weight: 400;
    background: #f7f7f7;
    color: #999;
  }

  .differ {
    background-color: #f4f9fd !important;
  }

  .tb-title {
    margin-right: 36px;
    float: right
  }

  .opt-checkbox {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }
}
</style>
