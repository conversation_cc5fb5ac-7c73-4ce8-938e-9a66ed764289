<template>
  <div class="profile-wrapper">
    <div class="points-info">
      <div class="title">我的积分</div>
      <div class="tip-wrap">说明：充值总积分 = （消费总积分-退款总积分）+ 可用总积分 + 到期总积分 + 未生效总积分</div>
      <t-tabs v-model="scoreType" @change="changeScoreType" style="margin-top: 16px;">
        <t-tab-panel v-for="item in scoreTypeList" :value="item.value" :label="item.label" :key="item.value">
        </t-tab-panel>
      </t-tabs>
      <div class="info-wrap">
        <div>充值总积分：{{currentAccountInfo.rechargeAmount | formatMoney(2)}}</div>
        <div>消费总积分：{{currentAccountInfo.payAmount | formatMoney(2)}}</div>
        <div>退款总积分：{{currentAccountInfo.refundAmount | formatMoney(2)}}</div>
        <div class="delay">到期总积分：{{currentAccountInfo.expiredAmount | formatMoney(2)}}</div>
        <div class="blue-points">可用总积分：{{currentAccountInfo.availableAmount | formatMoney(2)}}</div>
        <div class="blue-points">未生效总积分：{{currentAccountInfo.soonEffectiveAmount | formatMoney(2)}}</div>
      </div>
      <t-tabs v-model="status" @change="change" style="margin-bottom: 12px;">
        <t-tab-panel v-for="item in orderStatusList" :value="item.value" :label="item.label" :key="item.label">
        </t-tab-panel>
      </t-tabs>
      <recharge @jumpTo="jumpTo" v-if="status === 1"></recharge>
      <consumpotion @jumpTo="jumpTo" ref="consumpotion" v-if="status === 2"></consumpotion>
      <refund ref="refund" v-if="status === 3"></refund>
    </div>
  </div>
</template>

<script>
import recharge from './recharge.vue'
import consumpotion from './consumpotion.vue'
import refund from './refund.vue'
import * as api from '@/views/Center/points/api'
export default {
  name: 'Center-points',
  data() {
    return {
      status: 1,
      scoreType: 0, // 默认福利积分
      scoreTypeList: [
        { label: '福利积分', value: 0 },
        { label: '扶贫积分', value: 1 }
      ],
      form: {
        date: '',
        delayData: ''
      },
      orderStatusList: [
        { label: '积分充值记录', value: 1},
        { label: '积分消费记录', value: 2},
        { label: '积分退款记录', value: 3}
      ],
      accountInfo: []
    }
  },
  created() {
    this.loadAccountInfo()
  },
  components: {
    recharge,
    consumpotion,
    refund
  },
  computed: {
    userInfo() {
      return this.$store.state.User.userInfo
    },
    currentAccountInfo() {
      if (Array.isArray(this.accountInfo)) {
        return this.accountInfo.find(item => item.scoreType === this.scoreType) || {}
      }
      return this.accountInfo || {}
    }
  },
  methods: {
    async loadAccountInfo() {
      let res = await api.getMyAccount()
      if(res.code === 0) {
        this.accountInfo = res.data || {}
        this.currentAccountInfo = { ...this.accountInfo }
      }
    },
    change(val) {
      console.log(val)
      this.jumpTo({status: this.status})
    },
    jumpTo(data) {
      this.status = data.status
      this.$nextTick(() => {
        if (this.status === 2) {
          this.$refs.consumpotion.init(data.row)
        } else if (this.status === 3) {
          this.$refs.refund.init(data.row)
        }
      })
    },
    changeScoreType(val) {
      this.scoreType = val
    }
  }
}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.profile-wrapper {
  width: 83.33%;
  border: 1px solid rgba(0, 0, 0, 0.1);
  .points-info {
    background-color: #fff;
    margin: 20px;
    .title {
      font-size: 16px;
      font-weight: 600;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      color: #333;
      line-height: 20px;
      margin-top: 12px;
    }
    .tip-wrap {
      background-color: #f5f5f5;
      color: #F22E00;
      font-size: 14px;
      line-height: 36px;
      height: 36px;
      padding: 0 8px;
      margin-top: 16px;
    }
    .info-wrap {
      background-color: #f5f5f5;
      color: #333;
      font-size: 14px;
      line-height: 24px;
      margin-top: 16px;
      padding: 8px;
      .delay {
        color: #F22E00;
      }
      .blue-points {
        color: #165DFF;
      }
    }
  }
}
</style>
