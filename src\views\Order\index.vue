<template>
  <div class="order-wrapper">
    <t-loading :loading="loading" attach="#order-detail"/>
    <div class="order-detail" id="order-detail">
      <div class="header">
        <div class="cop" @click="jumpToMyOrder">我的<span class="gap">></span></div>
        <div class="cop" @click="jumpToMyOrder">订单中心<span class="gap">></span></div>
        <div class="header-no">订单：{{ orderData.no }}</div>
      </div>
      <div class="user-info">
        <div class="user-wrap">
          <div class="title">
            <span>
              订单信息 
            </span> 
            <div style="float:right;">
              <t-button class="btn" @click="cancelOrder" theme="danger" variant="outline" v-if="canCancel(orderData)">取消订单</t-button>
              <t-button class="btn" @click="toPay(orderData)" variant="outline" v-if="needPay(orderData)">订单支付</t-button>
              <!-- <t-button class="btn" @click="toApprove(orderData)" variant="outline" v-if="canApprove(orderData)">提交采购</t-button> -->
              <t-button class="btn" @click="receiveOrder(orderData)" variant="outline" v-if="canReceive(orderData)">确认收货</t-button>
              <t-button class="btn" @click="deleteOrder(orderData)" variant="outline" v-if="canDelete(orderData)">删除订单</t-button>
            </div>
          </div>
          <div class="title-block">
            <div class="block-sub">
              <span>订单号：</span>
              <span>{{ orderData.no || '--' }}</span>
            </div>
            
            <div class="block-sub">
              <span>下单时间：</span>
              <span>{{ orderData.orderSubmitTime | formatDate('yyyy-MM-dd HH:mm:ss') }}</span>
            </div>

            <div class="block-sub">
              <span>供应商：</span>
              <span>{{ orderData.supplierName || '--' }}</span>
            </div>
          </div>
          <div class="title-block">
            <div class="block-sub">
              <span>订单状态：</span>
              <span><t-tag theme="primary" variant="light">{{ orderData | orderStatusInfo}}</t-tag></span>
            </div>

            <div class="block-sub">
              <span class="title-label">支付方式：</span>
              <span><t-tag theme="primary" variant="light">{{ orderData.paymentMethod | paymentMethodInfo}}</t-tag></span>
            </div>

            <div class="block-sub" v-if="orderData.thirdOrderId">
              <span class="title-label">外部订单号：</span>
              <span>{{ orderData.thirdOrderId || '--' }}</span>
            </div>
          </div>
          <div class="title-block" v-if="orderData.payed">
            <div class="block-sub">
              <span class="title-label">支付状态：</span>
              <span><t-tag theme="primary" variant="light">{{ orderData.payed ? '已支付' : '未支付' }}</t-tag></span>
            </div>
            <div class="block-sub" v-if="orderData.orderPayTime">
              <span>支付时间：</span>
              <span>{{ orderData.orderPayTime | formatDate('yyyy-MM-dd HH:mm:ss') }}</span>
            </div>
            <div class="block-sub" v-if="orderData.payPrice">
              <span>支付金额：</span>
              <span>￥{{ orderData.payPrice | formatMoney }}</span>
            </div>
          </div>
          <div class="title-block">
            <div class="block-sub" v-if="orderData.orderDeliveryTime">
              <span>发货时间：</span>
              <span>{{ orderData.orderDeliveryTime | formatDate('yyyy-MM-dd HH:mm:ss') }}</span>
            </div>

            <div class="block-sub" v-if="orderData.orderReceiveTime">
              <span class="title-label">收货时间：</span>
              <span>{{ orderData.orderReceiveTime | formatDate('yyyy-MM-dd HH:mm:ss') }}</span>
            </div>

            <div class="block-sub" v-if="orderData.orderFinishTime">
              <span class="title-label">完成时间：</span>
              <span>{{ orderData.orderFinishTime | formatDate('yyyy-MM-dd HH:mm:ss') }}</span>
            </div>

          </div>
          <div class="title-block">
            <span>备注：</span>
            <span>{{ orderData.userRemark || '--' }}</span>
          </div>
        </div>
      </div>
      
      <div class="flex-column" v-if="[2, 3, 4, 5, 8].includes(orderData.status)">
        <div v-if="!hasLogisticsInfo && !hasTrackInfo && !hasTrackList" class="no-track">
          <t-image fit="cover" class="image" :src="emptySrc"></t-image>
          <div class="text-info">物流信息还未更新，请耐心等待</div>
        </div>
        <template v-if="hasTrackList">
          <!-- 非京东物流 -->
          <div class="product" v-for="(deliveryItem, index) in orderData.deliveryRespVOS" :key="index">
            <div class="left">
              <div class="info">
                <t-row :gutter="16">
                  <t-col :span="5">
                    <div class="text">物流名称：</div>
                  </t-col>
                  <t-col :span="7">
                    <div class="text">{{ deliveryItem.name || '--' }}</div>
                  </t-col>
                </t-row>
                <t-row :gutter="16">
                  <t-col :span="5">
                    <div class="text">货运单号：</div>
                  </t-col>
                  <t-col :span="7">
                    <div class="text">{{ deliveryItem.num || '--' }}</div>
                  </t-col>
                </t-row>
              </div>
            </div>
            <div class="right">
              <t-timeline v-if="deliveryItem.trackList.length" :mode="mode1" :theme="docStyle" class="timeline-vertical">
                <t-timeline-item v-for="item in deliveryItem.trackList" :key="item.deliveryTime + item.content" :label="item.deliveryTime | formatDate('yyyy-MM-dd HH:mm:ss')">
                  <div>{{ item.status }}</div>
                  <div class="timeline-custom-content">{{ item.content }}</div>
                </t-timeline-item>
              </t-timeline>
              <div v-else> 
                <div class="text-info">物流信息还未更新，请耐心等待</div>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <!-- 京东物流 -->
          <div class="product" v-if="hasTrackInfo || hasLogisticsInfo">
            <div class="left">
              <t-image v-if="orderData.skuInfoList[0].imageUrl" fit="cover" class="image" :src="orderData.skuInfoList[0].imageUrl"></t-image>
              <div class="info" v-if="hasLogisticsInfo">
                <t-row :gutter="16">
                  <t-col :span="5">
                    <div class="text">送货方式：</div>
                  </t-col>
                  <t-col :span="7">
                    <div class="text">{{ orderData.deliveryInfo.logisticInfoList[0].deliveryCarrier || '--'}}</div>
                  </t-col>
                </t-row>
                <t-row :gutter="16">
                  <t-col :span="5">
                    <div class="text">承运人：</div>
                  </t-col>
                  <t-col :span="7">
                    <div class="text">{{ orderData.deliveryInfo.logisticInfoList[0].deliveryCarrier || '--' }}</div>
                  </t-col>
                </t-row>
                <t-row :gutter="16">
                  <t-col :span="5">
                    <div class="text">货运单号：</div>
                  </t-col>
                  <t-col :span="7">
                    <div class="text">{{ orderData.deliveryInfo.logisticInfoList[0].deliveryOrderId || '--' }}</div>
                  </t-col>
                </t-row>
              </div>
            </div>
            <div class="right">
              <t-timeline v-if="hasTrackInfo" :mode="mode1" :theme="docStyle" class="timeline-vertical">
                <t-timeline-item v-for="item in orderData.deliveryInfo.trackInfoList" :key="item.trackMsgTime + item.trackContent" :label="item.trackMsgTime | formatDate('yyyy-MM-dd HH:mm:ss')">
                  <div>{{ item.trackOperator }}</div>
                  <div class="timeline-custom-content">{{ item.trackContent }}</div>
                </t-timeline-item>
              </t-timeline> 
              <div v-else> 
                <div class="text-info">物流信息还未更新，请耐心等待</div>
              </div>
            </div>
          </div>
        </template>
      </div>


      <div class="user-info">
        <div class="user-wrap">
          <div class="title">收货人信息</div>
          <t-row :gutter="16">
            <t-col :span="3">
              <div>收货人：</div>
            </t-col>
            <t-col :span="9">
              <div>{{ orderData.address ? orderData.address.name : '--' }}</div>
            </t-col>
          </t-row>
          <t-row :gutter="16">
            <t-col :span="3">
              <div>地址：</div>
            </t-col>
            <t-col :span="9">
              <div>{{ orderData.address ? comAddressName : '--' }}</div>
            </t-col>
          </t-row>
          <t-row :gutter="16">
            <t-col :span="3">
              <div>手机号码：</div>
            </t-col>
            <t-col :span="9">
              <div>{{ orderData.address && orderData.address.mobile ? orderData.address.mobile.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2") : '--' }}</div>
            </t-col>
          </t-row>
        </div>
        <!-- <div class="user-wrap">
          <div class="title">配送信息</div>
          <t-row :gutter="16">
            <t-col :span="4">
              <div>配送方式：</div>
            </t-col>
            <t-col :span="8">
              <div>{{ orderData.deliveryInfo.logisticInfoList[0].deliveryCarrier || '--' }}</div>
            </t-col>
          </t-row>
        </div> -->
        <!-- <div class="user-wrap">
          <div class="title">{{ orderData.status == 9 ? '退款信息' : '付款信息' }}</div>
          <t-row :gutter="16">
            <t-col :span="3">
              <div>付款方式：</div>
            </t-col>
            <t-col :span="9">
              <div>在线支付</div>
            </t-col>
          </t-row>
          <t-row :gutter="16">
            <t-col :span="3">
              <div>付款时间：</div>
            </t-col>
            <t-col :span="9">
              <div>2023-06-16 10:43:24</div>
            </t-col>
          </t-row>
        </div> -->
      </div>

      <div class="user-info">
        <div class="user-wrap">
          <div class="title">发票信息</div>
          <t-row :gutter="16">
            <t-col :span="3">
              <div>开票状态：</div>
            </t-col>
            <t-col :span="9">
              <div><t-tag theme="primary" variant="light">{{ orderData.tradeOrderInvoice ? orderData.tradeOrderInvoice.invoiceStatusName : '--' }}</t-tag></div>
            </t-col>
          </t-row>
        </div>
      </div>

      <div class="user-info" v-if="purchaseInfo.purchaseId">
        <div class="user-wrap">
          <div class="title">采购单信息</div>
          <t-row :gutter="16">
            <t-col :span="3">
              <div>审批状态：</div>
            </t-col>
            <t-col :span="9">
              <div><t-tag theme="primary" variant="light">{{ orderData.auditStatus | orderAuditStatusInfo }}</t-tag></div>
            </t-col>
          </t-row>
          <t-row :gutter="16" v-if="auditData && auditData.length">
            <t-col :span="12">
              <div>
                <t-table row-key="id" :data="auditData" :columns="auditColumns">
                  <template #auditStatus="{ row }">
                    <t-tag shape="round" :theme="row.auditStatus | ycrhAuditStatusStyle" variant="light-outline">
                      {{ row.auditStatus | ycrhAuditStatusInfo }}
                    </t-tag>
                  </template>
                </t-table>
              </div>
            </t-col>
          </t-row>
          <t-row :gutter="16">
            <t-col :span="3">
              <div>采购原因：</div>
            </t-col>
            <t-col :span="9">
              <div>{{ purchaseInfo.purchaseReason || '--' }}</div>
            </t-col>
          </t-row>
          <t-row :gutter="16" v-if="purchaseInfo.accepterName">
            <t-col :span="3">
              <div>验收人姓名：</div>
            </t-col>
            <t-col :span="9">
              <div>{{ purchaseInfo.accepterName || '--' }}</div>
            </t-col>
          </t-row>
          <t-row :gutter="16" v-if="purchaseInfo.accepterMobile">
            <t-col :span="3">
              <div>验收人手机号：</div>
            </t-col>
            <t-col :span="9">
              <div>{{ purchaseInfo.accepterMobile || '--' }}</div>
            </t-col>
          </t-row>
        </div>
      </div>

      <div class="price-info">
        <div class="table-header">
          <div class="table-product">商品</div>
          <div class="no">商品编号</div>
          <div class="price">价格</div>
          <div class="num">商品数量</div>
          <div class="opt">操作</div>
        </div>
        <div class="table-data" v-for="item in orderData.skuInfoList" :key="item.skuId">
          <div class="table-product wrap">
            <t-image v-if="item.imageUrl" class="image" fit="cover" :src="item.imageUrl"></t-image>
            <div class="title-wrap">
              <div class="main-title">{{ item.skuName }}</div>
              <!-- <div class="sub-title">暗紫色 256GB</div> -->
            </div>
          </div>
          <div class="no">
            {{ item.skuId }}
          </div>
          <div class="price">
            ￥{{ item.skuPrice | formatMoney }}
          </div>
          <div class="num">
            {{ item.skuNum }}
          </div>
          <!-- <div class="opt" @click="toComment(item)">
             评价|晒单
          </div> -->
          <div class="opt">
            <t-button variant="text" v-if="canAfterSale(orderData, item)" @click="toAfterSale(orderData, item)">{{ getAfterSaleBtnText(orderData, item) }}</t-button>
            <!-- <t-button variant="text" v-if="canAssetGo(orderData, item)" @click="toAssetsSys(item)">建档通道</t-button> -->
            <t-button variant="text" @click="toProductDetail(item.supplierId, item)">立即购买</t-button>
          </div>
        </div>
        <div class="price-detail">
          <t-row :gutter="16">
            <t-col :offset="9" :span="1">
              <div>商品总额：</div>
            </t-col>
            <t-col :span="2">
              <div>￥{{ orderData.productPrice | formatMoney }}</div>
            </t-col>
          </t-row>
          <t-row :gutter="16">
            <t-col :offset="9" :span="1">
              <div>运费：</div>
            </t-col>
            <t-col :span="2">
              <div>+ ￥{{ orderData.deliveryPrice | formatMoney }}</div>
            </t-col>
          </t-row>
          <t-row :gutter="16" class="total">
            <t-col :offset="9" :span="1">
              <div>订单总额：</div>
            </t-col>
            <t-col :span="2">
              <div class="total-price"> ￥{{ orderData.orderTotalPrice | formatMoney }}</div>
            </t-col>
          </t-row>
          <t-row :gutter="16" v-if="orderData.payScore">
            <t-col :offset="9" :span="1">
              <div>抵扣积分：</div>
            </t-col>
            <t-col :span="2">
              <div>- ￥{{ orderData.payScore | formatMoney }}</div>
            </t-col>
          </t-row>
          <t-row :gutter="16">
            <t-col :offset="9" :span="1">
              <div>实付款：</div>
            </t-col>
            <t-col :span="2">
              <div class="total-price">￥{{ orderData.orderTotalPrice | formatMoney }}</div>
            </t-col>
          </t-row>
        </div>
      </div>
    </div>
    <order-cancel-dialog @on-cancel="getDetailOrder" ref="dialog"></order-cancel-dialog>
  </div>
</template>

<script>
import { FileAddIcon, WalletIcon, UsbIcon, LocationIcon, CheckIcon, CheckCircleIcon, UserIcon, ForwardIcon } from 'tdesign-icons-vue'
import { getDetailOrder } from '@/views/Trade/api'
import { queryLogicticsInfo, queryDeliveryInfo } from '@/views/Order/api'
import OrderCancelDialog from '@/views/Order/components/order-cancel'
import { orderMixins } from '@/views/Order/components/orderMixin.js'
export default {
  name: 'TradeOrderDetail',
  mixins: [ orderMixins ],
  data() {
    return {
      direction: 'horizontal',
      docStyle: 'dot',
      mode1: 'alternate',
      loading: false,
      orderNo: '',
      orderData: {
        deliveryInfo: {
          trackInfoList: [],
          logisticInfoList: [{
            deliveryCarrier: ''
          }]
        },
        address: {},
        skuInfoList: [{
          skuId: ''
        }]
      },
      emptySrc: require('@/assets/nothing.png'),
      auditData: [],
      auditColumns: [
        { colKey: 'approvalRoleName', title: '审批角色', width: 150 },
        { colKey: 'approvalUserName', title: '审批人', width: 150 },
        { colKey: 'auditStatus', title: '审批状态', width: 150 },
        { colKey: 'approvalTime', title: '审批时间', width: 150 }
      ]
    }
  },
  computed: {
    purchaseInfo() {
      return this.orderData?.appPurchaseInfo || {}
    },
    hasLogisticsInfo() {
      return this.orderData.deliveryInfo && this.orderData.deliveryInfo.logisticInfoList && this.orderData.deliveryInfo.logisticInfoList.length
    },
    hasTrackInfo() {
      return this.orderData.deliveryInfo && this.orderData.deliveryInfo.trackInfoList && this.orderData.deliveryInfo.trackInfoList.length
    },
    hasTrackList() {
      return this.orderData.deliveryRespVOS && this.orderData.deliveryRespVOS.length > 0
    },
    comAddressName() {
      let str = ''
      if(this.orderData.address) {
        str += this.orderData.address?.provinceName || ''
        str += this.orderData.address?.cityName || ''
        str += this.orderData.address?.countyName || ''
        str += this.orderData.address?.consigneeAddress || ''
      }
      return str
    }
  },
  methods: {
    jumpToMyOrder() {
      this.$router.push('/center/myorder')
    },
    async getDetailOrder() {
      this.loading = true
      const res = await getDetailOrder({
        orderNo: this.orderNo || ''
      })
      if (res.code === 0) {
        this.orderData = res.data
        if(res.data.appPurchaseInfo) {
          try {
            this.auditData = JSON.parse(res.data.appPurchaseInfo.auditResult)
            this.auditData.forEach((item,index) => {
              item.id = index
            })
          } catch(e) {
            console.error('parse auditInfo error', e)
          }
        }
      }
      this.loading = false
    },
    async queryLogicticsInfo() {
      const res = await queryLogicticsInfo({
        orderNo: this.orderNo || ''
      })
      if (res.code === 0) {
        this.logisticsInfo = res.data
      }
    },
    async queryDeliveryInfo() {
      const res = await queryDeliveryInfo({
        orderNo: this.orderNo || ''
      })
      if (res.code === 0) {
        this.deliveryInfo = res.data
      }
    },
    async cancelOrder() {
      this.$refs.dialog.cancelOrder(this.orderData)
    },
    refresh() {
      this.getDetailOrder()
    },
    afterDelete() {
      this.jumpToMyOrder()
    }
  },
  created() {
    this.orderNo = this.$route.query.no
    this.getDetailOrder()
  },
  components: {
    OrderCancelDialog,
    FileAddIcon,
    WalletIcon,
    UsbIcon,
    LocationIcon,
    CheckIcon,
    CheckCircleIcon,
    UserIcon,
    ForwardIcon
  }
}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.order-wrapper {
  background-color: #F7F8FA;
  min-height: 500px !important;
  .order-detail {
    width: 1200px;
    margin: 0 auto;
    padding: 16px;
    /deep/.t-row {
      font-size: 14px!important;
      margin: 10px 0 0;
    }
    .header {
      min-width: 300px;
      height: 32px;
      line-height: 32px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .gap {
        margin: 0 16px;
      }
      .header-no {
        font-weight: 600;
      }
    }

    .title-block {
      margin-top: 20px;
      display: block;
      .block-sub {
        display: inline-block;
        margin-right: 30px;
        width: 260px;
      }
      .title-label {
        margin-left: 0px;
      }
    }
    .order-header {
      background-color: #fff;
      height: 360px;
      display: flex;
      border: 1px solid #E9EBF2;
      border-radius: 4px;
      .left {
        // width: 400px;
        width: 100%;
        padding: 24px;
        border-right: 1px solid #E9EBF2;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        .order {
          margin-bottom: 48px;
          font-size: 14px;
          color: #86909C;
        }
        .status {
          font-size: 28px;
          font-weight: 700;
          color: #00B42A;
          margin-bottom: 48px;
        }
        .btn {
          width: 160px;
        }
        .invoice {
          margin-bottom: 12px;
        }
      }
      .right {
        flex: 1;
        padding: 24px;
        .tip {
          margin-bottom: 60px;
          font-size: 14px;
          color: #86909C;
        }
        .timeline-wrap {
          margin-left: 24px;
          .custom-icon {
            color: #00B42A;
            font-size: 36px;
          }
          .custom-icon-complete {
            color: @primary-color;
            font-size: 36px;
          }
          /deep/.t-timeline-item {
            width: 25%;
            min-width: 130px;
            &:last-child {
              width: 130px;
            }
            .t-timeline-item__tail {
              left: 40px;
              width: calc(100% - 40px);
            }
          }
        }
      }
    }
    .flex-column {
      display: flex;
      flex-direction: column;
      .product {
        background-color: #fff;
        width: 100%;
        height: 280px;
        display: flex;
        border: 1px solid #E9EBF2;
        border-radius: 4px;
        margin-top: 24px;
      }
      .left {
        width: 500px;
        padding: 40px 24px;
        display: flex;
        border-right: 1px solid #E9EBF2;
        .image {
          width: 100px;
          height: 100px;
        }
        .info {
          margin-left: 12px;
          flex: 1;
          height: 100px;
          margin-top: 16px;
          .text {
            font-size: 14px;
          }
        }
      }
      .right {
        padding: 24px;
        .timeline-vertical {
          height: 100%;
          overflow-y: auto;
          .custom-icon {
            color: rgba(0, 0, 0, 0.9);
            font-size: 16px;
          }
          .custom-icon-complete {
            color: @primary-color;
            font-size: 16px;
          }
        }
        /deep/.t-timeline-item__wrapper {
          margin-left: 200px;
          .t-timeline-item__dot--primary {
            border-color: rgba(0, 0, 0, 0.9);
          }
        }
        /deep/.t-timeline-item__label {
          width: 180px;
          color: #1D2129;
        }
      }
      .no-track {
        background-color: #fff;
        height: 280px;
        border: 1px solid #E9EBF2;
        border-radius: 4px;
        margin-top: 24px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        width: 100%;
        padding-left: 24px;
        .image {
          width: 160px;
          height: 160px;
          border-radius: 4px;
        }
        .text-info {
          font-size: 16px;
          margin-top: 24px;
        }
      }
    }    
    .user-info {
      background-color: #fff;
      display: flex;
      border: 1px solid #E9EBF2;
      border-radius: 4px;
      margin-top: 24px;
      .btn {
        margin-left: 16px;
        width: 160px;
      }
      .user-wrap {
        flex: 1;
        padding: 24px;
        border-right: 1px solid #E9EBF2;
        &:last-child {
          border-right: none;
        }
        .title {
          font-size: 16px;
          height: 32px;
          line-height: 32px;
        }
      }
    }
    .price-info {
      background-color: #fff;
      border: 1px solid #E9EBF2;
      border-radius: 4px;
      margin-top: 24px;
      .table-product {
        width: 45%;
        padding-left: 12px;
      }
      .no, .price, .num, .opt {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .no {
        width: 22%;
        padding-left: 12px;
      }
      .price {
        width: 10%;
        padding-left: 12px;
      }
      .num {
        width: 10%;
        padding-left: 12px;
      }
      .opt {
        width: 13%;
        padding-left: 12px;
      }
      .table-header {
        background-color: #F3F3F3;
        width: 100%;
        display: flex;
        height: 32px;
        line-height: 32px;
      }
      .table-data {
        display: flex;
        border-bottom: 1px solid #E9EBF2;
        .wrap {
          display: flex;
          align-items: center;
          .image {
            width: 60px;
            height: 60px;
            margin-right: 12px;
          }
          .title-wrap {
            height: 100px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            font-size: 14px;
            line-height: 22px;
            .sub-title {
              color: #86909C;
            }
          }
        }
      }
      .price-detail {
        margin: 16px;
        .total {
          color: @primary-color;
          line-height: 24px;
          .total-price {
            font-size: 28px;
            color: red;
            font-size: 600;
          }
        }
        /deep/.t-col {
          text-align: right;
        }
      }
    }
    .timeline-custom-content {
      font-size: 12px;
      margin-top: 16px;
      height: 70px;
      white-space: pre-wrap;
      line-height: 14px;
    }
    .mt0 {
      margin-top: 0;
    }
    .cop {
      cursor: pointer;
    }
  }
}
</style>
