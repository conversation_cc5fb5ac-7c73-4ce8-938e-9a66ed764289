<template>
  <div class="selector-wrapper">
    <div class="choose-box" v-if="chooseStr">
      <div class="title">您已选择：</div>
      <div class="tags">
        <div class="name">品牌 |</div>
        <div class="type-str" :title="chooseStr">{{ chooseStr }}</div>
        <div class="close" @click="closeTags">X</div>
      </div>
    </div>
    <div class="category" v-if="selectorList && selectorList.length">
      <div class="content">
        <div class="title">相关类目：</div>
        <div id="category-list" class="category-list" :class="{ expand: categoryMore }">
          <div v-for="item in selectorList" :key="item.categoryId" class="item" @click="clickCategory(item)">
            {{ item.cateName }}
          </div>
        </div>
      </div>
      <t-button
        v-show="showButton"
        theme="default"
        shape="square"
        variant="outline"
        class="button"
        @click="categoryMore = !categoryMore"
      >
        <chevron-up-icon v-if="categoryMore" />
        <chevron-down-icon v-if="!categoryMore" />
      </t-button>
    </div>
    <div class="type" v-if="!chooseStr && brandAggList && brandAggList.length">
      <div class="content">
        <div class="title">品牌：</div>
        <div
          class="type-list"
          id="type-list"
          :class="{ expand: typeMore }"
          v-if="!checkboxShow"
        >
          <div
            v-for="item in brandAggList"
            :key="item.brandId"
            class="item text-ellipsis"
            :title="item.name"
            @click="clickType(item)"
          >
            {{ item.name }}
          </div>
        </div>
        <div v-else class="type-list checkbox-expand">
          <t-checkbox-group v-model="value">
            <t-checkbox
              v-for="item in brandAggList"
              :key="item.brandId"
              :label="item.name"
              :value="item.brandId"
            ></t-checkbox>
          </t-checkbox-group>
          <div class="btn-list">
            <t-button style="margin-right: 16px" @click="confirm">
              确定
            </t-button>
            <t-button theme="default" variant="outline" @click="cancel"
              >取消</t-button
            >
          </div>
        </div>
      </div>
      <div class="button-list" v-if="!checkboxShow">
        <!-- <t-button
          theme="default"
          variant="outline"
          style="margin-right: 16px"
          @click="showCheckbox"
        >
          多选
          <add-icon slot="icon" />
        </t-button> -->
        <t-button
          v-show="showBrandButton"
          theme="default"
          shape="square"
          variant="outline"
          class="button"
          @click="typeMore = !typeMore"
        >
          <chevron-up-icon v-if="typeMore" />
          <chevron-down-icon v-if="!typeMore" />
        </t-button>
      </div>
    </div>
  </div>
</template>

<script>
import { AddIcon, ChevronUpIcon, ChevronDownIcon } from "tdesign-icons-vue";

export default {
  name: "SearchSelector",
  data() {
    return {
      categoryMore: false,
      typeMore: false,
      checkboxShow: false,
      value: "",
      chooseList: ""
    };
  },
  props: {
    selectorList: {
      type: Array,
      default: () => []
    },
    brandAggList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    chooseStr() {
      if (typeof this.chooseList === "string") {
        return this.chooseList;
      }
      return this.chooseList.join("、");
    },
    showButton() {
      const dom = document.getElementById('category-list')
      if (!dom) {
        return false
      }
      const contentWidth = dom.clientWidth
      const scrollWidth = dom.scrollWidth
      
      // 比较实际内容宽度与容器宽度，如果实际内容宽度大于容器宽度，则表示内容被换行了
      if (scrollWidth > contentWidth) {
        return true;
      }
      
      return false;
    },
    showBrandButton() {
      const dom = document.getElementById('type-list')
      if (!dom) {
        return false
      }
      const contentWidth = dom.clientWidth
      const scrollWidth = dom.scrollWidth
      
      // 比较实际内容宽度与容器宽度，如果实际内容宽度大于容器宽度，则表示内容被换行了
      if (scrollWidth > contentWidth) {
        return true;
      }
      
      return false;
    }
  },
  components: {
    AddIcon,
    ChevronUpIcon,
    ChevronDownIcon,
  },
  created() {
    this.queryData = this.$route.query
    if (this.queryData.brandId) {
      const i = this.brandAggList.findIndex(item => item.brandId == this.queryData.brandId)
      if (i !== -1) {
        this.chooseList = this.brandAggList[i].name
      }
    }
  },
  methods: {
    reset() {
      this.value = '';
      this.chooseList = '';
      this.checkboxShow = false;
      this.typeMore = false;
      this.categoryMore = false;
    },
    showCheckbox() {
      this.value = [];
      this.checkboxShow = true;
    },
    confirm() {
      this.chooseList = JSON.parse(JSON.stringify(this.value));
      this.value = "";
      this.checkboxShow = false;
    },
    cancel() {
      this.chooseList = "";
      this.value = "";
      this.checkboxShow = false;
    },
    clickType(item) {
      this.chooseList = item.name;
      this.$emit('checkBrand', item);
    },
    closeTags() {
      this.chooseList = "";
      this.$emit('checkBrand', {});
    },
    clickCategory(item) {
      this.$emit('checkCategory', item);
    }
  },
  watch: {
    $route: {
      handler(newVal, oldVal) {
        this.queryData = this.$route.query
        this.chooseList = ''
        this.supplierCheck = ''
        if (this.queryData.brandId) {
          const i = this.brandAggList.findIndex(item => item.brandId == this.queryData.brandId)
          if (i !== -1) {
            this.chooseList = this.brandAggList[i].name
          }
        }
      },
    }
  }
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";
.selector-wrapper {
  width: 1200px;
  margin: 14px auto;
  .choose-box {
    background: #f5f6f8;
    line-height: 48px;
    border-bottom: 1px solid #E9EBF2;
    display: flex;
    align-items: center;
    .title {
      width: 70px;
      margin-left: 30px;
      color: #666;
    }
    .tags {
      border: 1px solid #ddd;
      margin-right: 20px;
      background-color: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      height: 32px;
      line-height: 32px;
      &:hover {
        border-color: #e11b10;
      }
      .name {
        width: 40px;
        color: #999;
        margin-left: 16px;
        margin-right: 10px;
      }
      .type-str {
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #e11b10;
      }
      .close {
        font-size: 16px;
        color: #e11b10;
        cursor: pointer;
        margin-left: 10px;
        margin-right: 16px;
        padding: 4px;
      }
    }
  }
  .category {
    display: flex;
    background: #f5f6f8;
    justify-content: space-between;
    align-items: flex-start;
    border-bottom: 1px solid #E9EBF2;
    .content {
      display: flex;
      line-height: 40px;
      width: calc(100% - 80px);
      .title {
        width: 70px;
        margin-left: 30px;
        margin-right: 10px;
        color: #666;
      }
      .category-list {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        overflow: hidden;
        flex-wrap: wrap;
        .item {
          margin-right: 20px;
          color: #333;
          align-self: flex-start;
          max-width: 180px;
          white-space: nowrap;
          cursor: pointer;
          overflow: hidden;
          text-overflow: ellipsis;
          &:hover {
            color: @primary-color;
          }
        }
      }
    }
    .button {
      margin-top: 8px;
      margin-right: 24px;
    }
  }
  .type {
    display: flex;
    background: #f5f6f8;
    justify-content: space-between;
    align-items: flex-start;
    border-bottom: 1px solid #E9EBF2;
    .content {
      display: flex;
      line-height: 40px;
      width: calc(100% - 80px);
      .title {
        width: 70px;
        margin-left: 30px;
        margin-right: 10px;
        color: #666;
      }
      .type-list {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        overflow: hidden;
        flex-wrap: wrap;
        .item {
          margin-right: 20px;
          color: #333;
          align-self: flex-start;
          max-width: 180px;
          white-space: nowrap;
          cursor: pointer;
          overflow: hidden;
          text-overflow: ellipsis;
          &:hover {
            color: @primary-color;
          }
        }
      }
      .expand {
        flex-wrap: wrap;
      }
    }
    .button-list {
      margin-top: 8px;
      margin-right: 24px;
    }
    .checkbox-expand {
      flex-wrap: wrap;
      max-height: 200px;
      overflow-y: auto;
      .btn-list {
        width: 100%;
        text-align: center;
      }
    }
    /deep/.t-checkbox-group {
      gap: 0 16px;
    }
    /deep/.t-checkbox__label {
      line-height: 48px;
    }
  }
  .expand {
    flex-wrap: wrap;
  }
}
</style>
