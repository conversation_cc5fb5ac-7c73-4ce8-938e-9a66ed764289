<template>
  <div class="order-table">
    <table class="header-item">
      <thead>
        <tr>
          <th class="table-first-th"  width="10%">
            <t-checkbox :checked="isAllChecked && orderList.length != 0" :indeterminate="indeterminate"
              :onChange="updateAllCartChecked" style="vertical-align: text-bottom;">全选</t-checkbox>
          </th>
          <th class="table-th" width="50%">商品名称</th>
          <th class="table-th" width="20%">数量</th>
          <th class="table-th" width="20%">价格</th>
        </tr>
      </thead>
    </table>
    <table class="order-item" v-for="order in orderList" :key="order.id">
      <thead>
        <tr>
          <th colspan="4" class="table-th">
            <div class="th-wrapper">
              <t-checkbox :checked="order.selected" @change="updateChecked(order, $event)"></t-checkbox>
              <span style="margin-left: 12px;">{{ getFormatTime(order.createTime) }}</span>
              <!-- 展示用no，查询用id -->
              <span style="margin-left: 24px;"> 订单编号：{{ order.no }} </span>
              <span style="margin-left: 24px;"> 订单总金额：¥{{ saveTwoDecimal(order.orderPrice) }} </span>
              <span style="margin-left: 24px;"> 供应商：{{ order.supplierName}} </span>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="skuItem in order.items" :key="skuItem.id">
          <td class="table-td" width="60%">{{ skuItem.skuName }}</td>
          <td class="table-td" width="13%">{{ skuItem.count }}</td>
          <td class="table-td" width="15%">¥{{ saveTwoDecimal(skuItem.skuTotalPrice) }}</td>
          <td class="table-td" width="12%">
            <div v-if="projectDetail.projectNo">
              <template v-if="skuAllowedStatus(skuItem) === 1">
                <t-icon name="check-circle" style="color: green" size="20px" /> 可购买
              </template>
              <template v-else-if="skuAllowedStatus(skuItem) === 2">
                <t-icon name="check-circle" style="color: orange" size="20px" /> 可购买:须补充材料
              </template>
              <template v-else>
                <t-icon name="error-circle" style="color: red" size="20px"/> 不可购买
              </template>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
  
<script>
import { getApprovalDetail, updateSelected } from '@/views/Approve/api'

export default {
  name: 'approve',
  props: {
    projectDetail: {
      type: Object,
      default: () => {}
    },
    ppRelated: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      orderList: []
    }
  },
  async created() {
    const res = await getApprovalDetail()
    if (res.code === 0) {
      this.orderList = res.data.orders || []
      let selectedOrder = {id: ''}
      if (this.$route.query.no) {
        selectedOrder = res.data.orders.find(x => {
          return x.no === this.$route.query.no
        }) || {id: ''}
      } else if(this.orderList.length) {
        selectedOrder = this.orderList[0]
      }

      this.updateAllCartChecked(false, 'init', selectedOrder)
    }
  },
  computed: {
    isAllChecked() {
      return this.orderList.every(item => {
        return item.selected == true
      })
    },
    indeterminate() {
      let num = 0
      this.orderList.forEach(item => {
        if (item.selected == true) {
          num++
        }
      })
      return num > 0 && num < this.orderList.length
    }
  },
  methods: {
    skuAllowedStatus(sku) {
      if(!this.ppRelated) {
        return 1
      }
      if(!this.projectDetail || !this.projectDetail.projectNo) {
        return 1
      }
      if(this.projectDetail && this.projectDetail.skuCategoryList) {
        let skuCatetorys = this.projectDetail.skuCategoryList
        if(skuCatetorys.length) {
          let obj = skuCatetorys.find(item => item.categoryCode === sku.categoryCode)
          if(obj) {
            return obj.status === '1' ? 2 : 1
          }
        }
      }
      return 0
    },
    getFormatTime(value = new Date()) {
      let date = new Date()
      if (value) {
        date = new Date(value)
      }
      const year = date.getFullYear()
      const month = `00${date.getMonth() + 1}`.slice(-2)
      const dateStr = `00${date.getDate()}`.slice(-2)
      const hour = `00${date.getHours()}`.slice(-2)
      const minutes = `00${date.getMinutes()}`.slice(-2)
      const seconds = `00${date.getSeconds()}`.slice(-2)
      return `${year}-${month}-${dateStr} ${hour}:${minutes}:${seconds}`
    },
    // 保留两位小数
    saveTwoDecimal(val = 0) {
      return (Math.round(val * 100) / 100).toFixed(2)
    },
    async updateChecked(order, val) {
      // const selectedRowKeys = this.orderList.filter(x => x.selected).map(x => x.id )
      const res = await updateSelected({
        orderIds: [order.id],
        selected: val
      })
      if (res.code === 0) {
        this.orderList = res.data.orders || []
        this.$emit('selectOrder', res.data)
      }
    },
    async updateAllCartChecked(val, type, selectedOrder) {
      const res = await updateSelected({
        orderIds: this.orderList.map(x => x.id),
        selected: val
      })
      if (res.code === 0) {
        this.orderList = res.data.orders || []
        if (type === 'init') {
          this.updateChecked(selectedOrder, true)
        } else {
          this.$emit('selectOrder', res.data)
        }
      }
    }
  }
}
</script>
    
<style lang="less" scoped>
.order-table {
  max-height: 450px;
  overflow: scroll;;
  .header-item {
    border: 1px solid #e6e6e6;
    border-collapse: separate;
    border-radius: 2px;
    width: 100%;
    max-width: 100%;
    border-spacing: 0;
    margin-bottom: 16px;
    .table-first-th {
      background-color: #f4f4f4;
      text-align: left;
      padding-left: 8px;
    }
    .table-th {
      padding: 6px 8px;
      color: #666;
      font-weight: 700;
      vertical-align: bottom;
      background-color: #f4f4f4;
      line-height: 18px;
      font-size: 12px;
      text-align: left;
    }
  }
  .order-item {
    border: 1px solid #e6e6e6;
    border-collapse: collapse;
    border-radius: 2px;
    width: 100%;
    margin-bottom: 18px;
    max-width: 100%;
    color: #666;
    .table-th {
      padding: 6px 8px;
      line-height: 18px;
      text-align: left;
      vertical-align: bottom;
      background-color: #f4f4f4;
      font-size: 12px;
      color: #666;
      .th-wrapper {
        display: flex;
        align-items: center;
      }
    }
    .table-td {
      font-size: 12px;
      color: #666;
      padding: 6px 8px;
      line-height: 18px;
      vertical-align: middle;
      text-align: left;
      border: 1px solid #e6e6e6;
    }
  }
}
</style>