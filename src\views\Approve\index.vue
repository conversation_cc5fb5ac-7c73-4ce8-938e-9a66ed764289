<template>
  <div class="approve-wrapper" v-loading="submitLoading || initLoading">
    <div class="block-title" style="padding:20px 0 0;">请勾选需要结算的订单</div>
    <order-table @selectOrder="selectOrder" :ppRelated="ppRelated" :projectDetail="curProjectDetail" />
    <div v-if="this.configData.projectSwitch" class="block-title">请选择项目及负责人</div>
    <t-form :data="formData" :rules="rules" ref="form" class="approve-form" labelAlign="top" @submit="onSubmit">


      <!--经济分类字段开关-->
      <template v-if="projectSwitch && ycrhConfig.formEconomyClassSwitch">
        <t-form-item label="经济分类" name="economyClass" :rules="[{required: true, message: '请选择经济分类', type: 'change'}]" >
          <t-select v-model="formData.economyClass" placeholder="请选择经济分类" filterable clearable style="width: 450px;">
            <t-option v-for="item in economyClassList" :value="item.code" :label="item.name" :key="item.code"></t-option>
          </t-select>
        </t-form-item>
        <t-card bordered :title="`${economyClassName}说明`" class="economy-tips-con" v-if="economyClassMemo">
          <div v-html="economyClassMemo"></div>
        </t-card>
      </template>

      <t-form-item v-if="projectSwitch" label="经费项目" name="projectNo">
        <t-select v-model="formData.projectNo" filterable clearable style="width: 550px;">
          <t-option v-for="item in comProjectList" :label="`${item.projectName}(${item.projectNo})`" :key="item.projectNo" :value="item.projectNo" :disabled="item.status === '0'" >
            <div :class="`project-option ${item.status === '0' ? 'p-status-inactive' : 'p-status-active'}`">
              <div class="name">{{ item.projectName }}({{item.projectNo}})</div>
              <div class="status">
                <template v-if="!item.status || item.status === '1'">
                  <t-icon name="check-circle" style="color: green" size="20px" /> 可使用
                </template>
                <template v-if="item.status === '2'">
                  <t-icon name="check-circle" style="color: orange" size="20px" /> 可使用:须补充材料
                </template>
                <template v-if="item.status === '0'">
                  <t-icon name="error-circle" style="color: red" size="20px"/> 不可用
                </template>
              </div> 
            </div>
          </t-option>
        </t-select>
        <div class="form-notice2" v-if="formNotice2">
          <div v-html="formNotice2"></div>
        </div>
      </t-form-item>

      <t-form-item label="项目详情" v-if="projectSwitch">
        <div>
          <div class="project-detail">
            <div class="first-item">项目负责人：{{ projectData.chargeName }}</div>
            <div class="second-item">项目部门：{{ projectData.departmentName }}</div>
          </div>
          <div class="project-detail">
            <div class="first-item">项目名称：{{ projectData.projectName }}</div>
            <div class="second-item">项目类型：{{ projectData.projectType }}</div>
          </div>
          <div class="project-detail" v-if="projectBalanceInfo && projectBalanceInfo.xmye">
            <div class="first-item">项目余额：{{ projectBalanceInfo.xmye | formatMoney }}  
              <span class="highlight-tip" v-if="purchasePrice > projectBalanceInfo.xmye ">（余额不足，无法采购）</span>
            </div>
            <div class="second-item" v-if="projectBalanceInfo.edkzbm">项目额度：{{ projectBalanceInfo.xmed | formatMoney }} 
              <span class="highlight-tip" v-if="purchasePrice > projectBalanceInfo.xmed ">（额度超限，无法采购）</span>
            </div>
          </div>
          <div class="project-detail" v-if="approvalFormOn">
            <div class="first-item">审批流程（必填）：</div>
            <t-table v-loading="approveUserLoading" style="flex: 1;" ref="table" bordered row-key="index" :data="approveUsers" :columns="columns">
              <template #ygName="{ row }">
                <template v-if="row.list && row.list.length > 0">
                  <t-select v-model="row.userId" filterable placeholder="请输入人员姓名或编号" :class="row.userId ? 'highlight-ok' : ''" clearable style="width: 260px;" @change="changeData(row)">
                    <t-option v-for="item in row.list" :key="item.ygNo" :value="item.ygNo"
                      :label="`${item.ygName}(${item.ygNo})`" />
                  </t-select>
                </template>
                <template v-else-if="row.ygName">
                  <t-tag variant="outline" theme="primary" :closable="row.editable" @close="row.ygName = ''">{{ row.ygName }}</t-tag>
                </template>
                <template v-else>
                  <t-row>
                    <t-col :span="8">
                      <div :class="row.ygNo ? 'highlight-ok' : ''"><t-input v-model="row.ygNo" @blur="fetchYgInfo(row)" :suffix="row.ygName" placeholder="请输入人员编号" style="width: 260px;"></t-input></div>
                    </t-col>
                    <t-col :span="4">
                      <span> {{ row.ygName }}</span>
                    </t-col>
                  </t-row>
                </template>
              </template>
            </t-table>
          </div>
          <div class="project-detail" v-if="approvalFormOn">
            <div :class="`first-item`">补充材料 {{projectData.status === '2' ? '' : '（非必填）'}}：</div>
            <t-upload tips='文件大小10M以内' :autoUpload="false" :sizeLimit="{ size: 10, unit: 'MB', message: '图片大小不超过10MB' }" ref="uploadRef" v-model="attachments"/>
          </div>
        </div>
      </t-form-item>

      <t-form-item label="采购原因（必填）" name="reason">
        <t-textarea v-model="formData.reason" :maxlength="450" placeholder="请输入采购原因" />
      </t-form-item>
      <t-form-item label="补充材料" name="attachments" v-if="approveAndNotYcrh">
        <t-upload tips='文件大小10M以内' :autoUpload="false" :sizeLimit="{ size: 10, unit: 'MB', message: '图片大小不超过10MB' }" ref="uploadRef" v-model="attachments"/>
      </t-form-item>

      <template v-if="ycrhConfig.formAcceptorSwitch">
        <accepter v-model="accepterValue" ref="accepter"></accepter>
      </template>
      <div class="block-title" style="margin-top:15px;">请确认提交申请</div>
      <t-form-item>
        <div class="confirm-text">已选订单数<span class="num">{{ selectedCount }}</span></div>
        <div class="confirm-text">订单总金额<span class="num">¥{{ purchasePrice | formatMoney }}元</span></div>
      </t-form-item>

      <div class="button-list">
        <t-space size="12px">
          <t-button theme="primary" type="submit">提交</t-button>
          <t-button theme="default" variant="base" @click="cancelApprove">取消</t-button>
        </t-space>
      </div>
    </t-form>
    <t-dialog theme="info" header="确认框" :visible.sync="visible" :onConfirm="onConfirm" :onCloseBtnClick="onClickCloseBtn">
      <div>确认提交申请吗？</div>
      <div>已选择{{ selectedCount }}个订单 总计 ¥{{ purchasePrice | formatMoney }}元</div>
    </t-dialog>

    <t-dialog :visible.sync="noticeVisible" attach="body" header="采购提示" confirmBtn="我知道了"
       :cancelBtn="null" :closeBtn="false" :closeOnEscKeydown="false" :closeOnOverlayClick="false" destroyOnClose :onConfirm="closeNotice">
      <div slot="body">
        <div v-html="formNotice"></div>
      </div>
    </t-dialog>

  </div>
</template>

<script>
import { getYcrhConfig, getProjectList, getProjectDetail, getApprovalUser, submitPurchase, getYgInfo, uploadAttachments, getEconomyClassList, getProjectBalanceInfo } from '@/views/Approve/api'
import orderTable from './components/orderTable.vue'
import accepter from './components/accepter.vue'
import { mapState } from 'vuex'
export default {
  name: 'approve',
  data() {
    return {
      initLoading: false,
      approveUserLoading: false,
      submitLoading: false,
      visible: false,
      attachments: [],
      formData: {
        projectNo: '',
        ignoreBpm: false,
        reason: '',
        economyClass: '',
        accepterName: '',
        accepterMobile: '',
        accepterEmail: '',
      },
      rules: {
        projectNo: [{ required: true, message: '经营项目必选', type: 'change' }],
        reason: [{ required: true, message: '采购原因必填', type: 'blur' }]
      },
      orderColumns: [
        { colKey: 'row-select', type: "multiple", width: 50 },
        { colKey: 'createTime', title: '下单时间' },
        { colKey: 'id', title: '订单编号', width: '200px' },
        { colKey: 'orderPrice', title: '订单总金额', width: '136px' },
      ],
      productColumns: [
        { colKey: 'skuName', title: '商品名称' },
        { colKey: 'count', title: '数量', width: '200px' },
        { colKey: 'skuTotalPrice', title: '价格', width: '120px' },
      ],
      approveUsers: [],
      handledList: [],
      certifierList: [],
      prequalificationList: [],
      columns: [
        { colKey: 'rank', width: 180, title: '审批级别' },
        { colKey: 'roleName', title: '角色', width: 200 },
        { colKey: 'ygName', title: '姓名' },
      ],
      purchasePrice: 0,
      selectedCount: 0,
      checkOrderIds: [], 
      checkOrders: [], 
      projectData: {},
      projectArray: [],
      ycrhConfig: {},
      projectBalanceInfo: {},
      economyClassList: [],
      noticeVisible: false,
      curProjectDetail: {},
      accepterValue: {}
    }
  },
  watch: {
    'formData.projectNo': {
      handler() {
        this.handleProjectNoChange()
      },
    },
    'formData.economyClass': {
      handler() {
        this.handleEconomyClassChange()
      },
    },
    checkOrderIds() {
      this.handleOrderIdsChange()
    },
    accepterValue: {
      deep: true,
      handler(newVal) {
        this.formData.accepterName = newVal.accepterName
        this.formData.accepterMobile = newVal.accepterMobile
        this.formData.accepterEmail = newVal.accepterEmail
      }
    }
  },
  components: {
    orderTable, accepter
  },
  computed: {
    ...mapState({
      configData: state => state.Home.configData
    }),
    projectSwitch() {
      return this.configData.projectSwitch
    },
    approveSwitch() {
      return this.configData.approveSwitch
    },
    purchaseSwitch() {
      return this.approveSwitch || this.projectSwitch
    },
    approveAndYcrh() {
      return this.approveSwitch && this.ycrhConfig.bpmSysCode === 'ycrh'
    },
    approveAndNotYcrh() {
      return this.approveSwitch && this.ycrhConfig.bpmSysCode !== 'ycrh'
    },
    needEconomyClass() {
      return this.projectSwitch && this.ycrhConfig && this.ycrhConfig.formEconomyClassSwitch
    },
    ppRelated() {
      return this.projectSwitch && this.ycrhConfig && this.ycrhConfig.ppRelated
    },
    comProjectList() {
      let arr = this.projectArray || []
      let needSort = arr.filter(p => ['0','1','2'].includes(p.status)).length === arr.length
      if(needSort) {
        arr.sort((a,b) => b.status.localeCompare(a.status))
      } 
      return arr
    },
    formNotice() {
      return this.ycrhConfig?.notice || ''
    },
    formNotice2() {
      return this.ycrhConfig?.notice2 || ''
    },
    economyClassMemo() {
      if(this.needEconomyClass && this.economyClassList) {
        let obj = this.economyClassList.find(item => item.code === this.formData.economyClass)
        if(obj) return obj.memo
      }
      return ''
    },
    economyClassName() {
      if(this.needEconomyClass && this.economyClassList) {
        let obj = this.economyClassList.find(item => item.code === this.formData.economyClass)
        if(obj) return obj.name
      }
      return '办公费'
    },
    approvalFormOn() {
      if(!this.approveAndYcrh) {
        return false
      }
      if(!this.selectedCount) {
        return false
      }
      if(!this.formData.projectNo) {
        return false
      }
      if(this.needEconomyClass && !this.formData.economyClass) {
        return false
      }
      return true
    },
    economyClass() {
      return this.formData.economyClass || '30201'
    },
    attachmentsValid() {
      return this.attachments && this.attachments.length && this.attachments[0].raw
    }
  },
  created() {
    if(!this.purchaseSwitch) {
      this.$message.error('采购配置开关未打开，请联系管理员处理')
      this.$router.back()
      return;
    }
    this.loadYcrhConfig()
  },
  methods: {
    fetchYgInfo(row) {
      if(row.ygNo) {
        // 证明人不能是项目负责人
        if(row.ygNo === this.projectData.chargeNo) {
          this.$message.error(row.roleName + '不能填写项目负责人!')
          row.ygNo = ''
          return
        }

        getYgInfo({userNo: row.ygNo}).then(res => {
          if(res.code === 0 && res.data) {
            // 证明人只能是员工
            if(row.roleName === '证明人' && res.data.ygType !== '员工') {
              this.$message.error(row.roleName + '只能选择教师!')
              row.ygNo = ''
              return
            }

            row.ygName = res.data.ygName
          } else {
            row.ygNo = ''
            row.ygName = ''
            this.$message.error('人员编号不存在，请重新输入!')
          }
        })
      } else {
        row.ygName = ''
      }
    },
    async loadProjectBalance() {
      if(!this.needEconomyClass) {
        return
      }
      if(!this.projectData.departmentNo || !this.formData.economyClass) {
        return
      }
      let params = {
        departmentNo: this.projectData.departmentNo,
        projectNo: this.projectData.projectNo,
        chargeNo: this.projectData.chargeNo,
        economyClass: this.economyClass
      }
      let res = await getProjectBalanceInfo(params)
      if(res.code === 0) {
        this.projectBalanceInfo = res.data
      }
    },
    async loadYcrhConfig() {
      this.initLoading = true
      try {
        let res = await getYcrhConfig()
        if(res.code === 0) {
          this.ycrhConfig = res.data || {}
          if(this.ycrhConfig.formEconomyClassSwitch) {
            this.loadEconomyClassList()
          }
          this.showFormNotice()
        }
        this.initLoading = false
        if(this.configData.projectSwitch){
          this.loadProjectList()
        }
      } catch(e) {
        this.initLoading = false
      }
    },
    showFormNotice() {
      if(this.formNotice && this.formNotice.length > 5) {
        let status = sessionStorage.getItem("purchase-notice")
        this.noticeVisible = !status
      }
    },
    closeNotice() {
      this.noticeVisible = false
      sessionStorage.setItem("purchase-notice", "1")
    },
    async loadEconomyClassList() {
      let res = await getEconomyClassList()
      if(res.code === 0) {
        this.economyClassList = res.data || []
        this.$refs.form.clearValidate(['economyClass'])
      }
    },
    selectOrder(data) {
      this.purchasePrice = data.purchasePrice
      this.selectedCount = data.selectedCount
      this.checkOrderIds = data.orders.filter(x => x.selected).map(x => x.id)
      this.checkOrders = data.orders.filter(x => x.selected)
    },
    handleOrderIdsChange() {
      if(this.ppRelated && this.needLoadProject()) {
        this.projectArray = []
        if(this.projectSwitch){
          this.loadProjectList()
        }
      }
    },
    needLoadProject() {
      if(!this.curProjectDetail || !this.curProjectDetail.skuCategoryList) {
        return true
      }
      let checkedCategorys = this.getCheckedOrderCategorys()
      if(!checkedCategorys.length) {
        return true
      }
      let permitCategorys = this.curProjectDetail.skuCategoryList.filter(item => item.categoryCode && item.status !== '0')
      return !checkedCategorys.every(item => permitCategorys.includes(item))
    },
    handleProjectNo4Reset() {
      let hitIndex = this.projectArray.findIndex(p => p.projectNo === this.formData.projectNo)
      if(hitIndex < 0) {
        this.formData.projectNo = null
      }
    },
    handleEconomyClassChange() {
      if(this.formData.projectNo) {
        this.getApprovalUser()
        this.loadProjectBalance()
      }
    },
    handleProjectNoChange() {
      this.projectData = this.projectArray.find(x => x.projectNo === this.formData.projectNo) || {}
      console.log('projectNo===', this.formData.projectNo)
      if (this.formData.projectNo) {
        this.loadProjectDetail()
        this.getApprovalUser()
        this.loadProjectBalance()
      } else {
        this.approveUsers = []
      }
    },
    getCheckedOrderCategorys() {
      if(this.checkOrders && this.checkOrders.length) {
        let categoryCodes = []
        this.checkOrders.forEach(order => {
          order.items.forEach(item => {
            categoryCodes.push(item.categoryCode)
          })
        })
        return categoryCodes
      }
      return []
    },
    getCheckedOrderCategoryStr() {
      return this.getCheckedOrderCategorys().join(',')
    },
    async loadProjectList() {
      let params = {}
      if(this.ppRelated) {
        params.productCategoryCodes = this.getCheckedOrderCategoryStr() || ''
      }
      const res = await getProjectList(params)
      if (res.code === 0) {
        this.projectArray = res.data || []
        if(!this.projectArray.length) {
          this.$message.info('无匹配的项目信息，请您联系平台管理员')
        }
        this.$refs.form.clearValidate(['projectNo'])
        this.handleProjectNo4Reset()
      }
    },
    async loadProjectDetail() {
      if(!this.ppRelated) {
        return
      }
      this.curProjectDetail = {}
      if(!this.projectData.projectNo) {
        return
      }
      let params = {
        projectNo: this.projectData.projectNo,
        departmentNo: this.projectData.departmentNo
      }
      let res = await getProjectDetail(params)
      if(res.code === 0 && res.data) {
        this.curProjectDetail = res.data[0] || {}
      }
    },
    async getApprovalUser() {
      if(!this.approveAndYcrh) {
        return
      }
      this.approveUserLoading = true
      this.approveUsers = []
      const res = await getApprovalUser({
        departmentNo: this.projectData.departmentNo,
        projectNo: this.projectData.projectNo,
        economyClass: this.economyClass,
        orderAmount: this.purchasePrice
      })
      if (res.code === 0) {
        const arr = []
        res.data.forEach(item => {
          const i = arr.findIndex(x => x.rank === item.rank)
          if (i === -1) {
            item.editable = !item.ygName
            arr.push(item)
          } else {
            arr[i] = {
              rank: item.rank,
              roleName: item.roleName,
              roleId: item.roleId,
              userId: '',
              userName: '',
              list: arr[i].list || [{
                ygName: arr[i].ygName,
                ygNo: arr[i].ygNo
              }]
            }
            arr[i].list.push({
              ygName: item.ygName,
              ygNo: item.ygNo
            })
          }
        })
        this.approveUsers = arr
      } else {
        this.approveUsers = []
      }
      this.approveUserLoading = false
    },
    changeData(row) {
      row.userName = row.list.find(x => x.ygNo === row.userId).ygName
    },
    async uploadFile() {
      if(this.attachmentsValid) {
        const formData = new FormData()
        formData.append('file', this.attachments[0].raw)
        let res = await uploadAttachments(formData)
        console.log('res========', res)
        return [res.data]
      }
      return []
    },
    async onConfirm() {
      let approvalUser = []
      if(this.approveAndYcrh) {
        approvalUser = this.approveUsers.map(x => {
          if (x.list) {
            return {
              approvalLevel: x.rank,
              approvalUserName: x.userName,
              approvalUserNo: x.userId,
              approvalRoleName: x.roleName,
              approvalRoleNo: x.roleId
            }
          }
          return {
            approvalLevel: x.rank,
            approvalUserName: x.ygName,
            approvalUserNo: x.ygNo,
            approvalRoleName: x.roleName,
            approvalRoleNo: x.roleId
          }
        })
      }
      this.submitLoading = true
      this.visible = false
      try {
        var data = {
          ids: this.checkOrderIds,
          purchaseReason: this.formData.reason,
          accepterName: this.formData.accepterName,
          accepterMobile: this.formData.accepterMobile,
          accepterEmail: this.formData.accepterEmail
        }
        if(this.projectSwitch){
          data['projectNo'] = this.projectData.projectNo
          data['projectInfo'] = {
            departmentNo: this.projectData.departmentNo,
            departmentName: this.projectData.departmentName,
            projectNo: this.projectData.projectNo,
            projectName: this.projectData.projectName,
            projectType: this.projectData.projectType,
            chargeNo: this.projectData.chargeNo,
            chargeName: this.projectData.chargeName
          }
          data['economyClass'] = this.economyClass
          data['economyClassName'] = this.economyClassName || this.economyClass
          data['approvalUserInfos'] = approvalUser
        }
        let fileUrls = await this.uploadFile()
        data.attachments = fileUrls
        const res = await submitPurchase(data)
        if(this.ycrhConfig.formAcceptorSwitch && this.$refs.accepter) {
          this.$refs.accepter.saveAccepter2cache()
        }

        this.$message.success('采购申请成功！')
        this.$router.push('/center/myorder')
      } catch(e) {
        console.error('提交采购异常', e)
      }
      this.submitLoading = false
    },
    onClickCloseBtn() {
      this.visible = false
    },
    cancelApprove() {
      this.$router.push('/center/myorder')
    },
    onSubmit({ validateResult, firstError }) {
      if (this.configData.projectSwitch && this.projectData.status === '0') {
        this.$message.warning('当前所选经费项目不可用，请您重新选择!')
        return
      }
      if (this.selectedCount === 0) {
        this.$message.warning('请选择订单')
        return
      }
      if(this.projectSwitch && this.projectBalanceInfo && this.projectBalanceInfo.xmye < this.purchasePrice) {
        this.$message.warning('项目余额不足，无法提交')
        return
      }
      if(this.projectSwitch && this.projectBalanceInfo && this.projectBalanceInfo.edkzbm && this.projectBalanceInfo.xmed < this.purchasePrice) {
        this.$message.warning('项目额度不足，无法提交')
        return
      }
      if (this.configData.projectSwitch) {
        for (let i = 0; i < this.approveUsers.length; i++) {
          if (this.approveUsers[i].list && !this.approveUsers[i].userId) {
            this.$message.warning('请选择审批人员')
            return
          } if (!this.approveUsers[i].list && !this.approveUsers[i].ygName) {
            this.$message.warning('请输入人员编号')
            return
          }
        }

        if (!this.approvalFormOn) {
          this.$message.warning('请选择审批人员')
          return
        }
      }
      
      if (validateResult === true) {
        this.visible = true
      } else {
        this.$message.warning(firstError);
      }
    }
  }
}
</script>
  
<style lang="less" scoped>
.approve-wrapper {
  position: relative;
  width: 1200px;
  margin: 0 auto 16px;
  min-height: 500px;
  .order-table {
    margin: 24px 0;
  }

  .form-notice2 {
    margin-left: 20px;
    padding: 6px;
    border: 1px solid #a5a0a08f;
    border-radius: 5px;
  }

  .block-title {
    padding: 10px 0;
    font-weight: bold;
    font-size: 15px;
  }
  .approve-form {
    .project-detail {
      display: flex;
      margin-top: 16px;

      .first-item {
        width: 400px;
      }
    }

    .confirm-text {
      flex: 1;
      .num {
        margin-left: 24px;
      }
    }
    .button-list {
      text-align: center;
    }
    .upload-input {
      margin-bottom: 16px;
    }
  }
  .highlight-tip {
    margin-left: 10px;
    color: red;
  }
}

</style>
<style lang="less">
.economy-tips-con {
  .t-card__header {
    padding: 10px 10px 5px !important;
  }
  .t-card__body {
    padding: 5px 10px 5px !important;
  }
}
.highlight-ok {
  .t-input {
    border-color: green !important;
  }
}

.project-option {
  width:100%;
  display: flex;
  align-content: center;
  justify-content: space-between;

}
.p-status-active {
  background: #0080001a;
}

.p-status-inactive {
  background: #8080801a;
}
.c-field-required::before {
  display: inline-block;
  margin-right: var(--td-comp-margin-xs);
  color: var(--td-error-color);
  line-height: var(--td-line-height-body-medium);
  content: "*";
}

</style>