const { defineConfig } = require('@vue/cli-service')
const path = require('path')
const webpack = require('webpack')
const TerserPlugin = require('terser-webpack-plugin');
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const productionGzipExtensions = ['js', 'css']

function resolve(dir) {
  return path.join(__dirname, dir)
}

module.exports = defineConfig({
  transpileDependencies: true,
  publicPath: ``,
  // 生产环境是否生成 sourceMap 文件
  productionSourceMap: false,
  configureWebpack: {
    resolve: {
      extensions: ['.js', '.vue', '.json'],
      alias: {
        '@': resolve('src'),
        'assets': resolve('src/assets'),
        'common': resolve('src/common'),
        'components': resolve('src/components'),
        'views': resolve('src/views'),
        'network': resolve('src/network'),
        'utils': resolve('src/utils')
      }
    },
    plugins: [
      new CompressionWebpackPlugin({
        algorithm: 'gzip',
        test: new RegExp('\\.(' + productionGzipExtensions.join('|') + ')$'),
        threshold: 10240,
        minRatio: 0.8
      }),
      new webpack.optimize.LimitChunkCountPlugin({
        maxChunks: 10,
        chunkOverhead: 1024
      })
    ]
  },
  devServer: {        
    host: 'localhost',
    port: 9900,
    https: false,
    hot: false,
    client: {
      overlay: false
    },
    historyApiFallback: true
    // historyApiFallback: {
    //   index: '/index.html',
    //   rewrites: [
    //     { from: /.*/, to: '/index.html' },
    //   ],
    // }
    // proxy: {
    //   "/api": {
    //     target: 'http://gmall-h5-api.atguigu.cn'
    //     // pathRewrite: {"^/api" : ""}
    //   }
    // }
  },
  chainWebpack(config) {
    config.plugin('html')
      .tap(args => {
        const date = new Date()
        args[0].createDate = date
        args[0].minify = true
        return args
      })
    config.optimization.minimize(true);
    config.plugins.delete('prefetch');
    config.optimization.minimizer = [
      new TerserPlugin({
        parallel: true,
        minify: TerserPlugin.uglifyJsMinify
      })
    ]
  },
})
