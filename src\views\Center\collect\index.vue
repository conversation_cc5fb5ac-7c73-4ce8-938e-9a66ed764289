<template>
  <div class="collect-right">
    <div class="collect-content" v-loading="loading">
      <div class="title">
        <h3>收藏商品</h3>
      </div>
      <div class="chosetype">
        <table>
          <thead>
            <tr>
              <th>商品图片</th>
              <th width="60%">商品名称</th>
              <th width="25%">操作</th>
            </tr>
            <tr v-if="goodsList.length === 0 && !loading">
              <td colspan="3" style="font-size: 14px;text-align: center;padding: 12px;">暂无数据</td>
            </tr>
          </thead>
        </table>
      </div>
      <table class="list-table">
        <tbody class="list-wrapper">
          <template v-if="goodsList.length > 0">
            <tr v-for="goods in goodsList" :key="goods.id" class="list-item">
              <td class="image-td">
                <img @click="jumpToDetail(goods.supplierId, goods.skuId)" class="image" :src="goods.picUrl" />
              </td>
              <td width="60%" class="name" @click="jumpToDetail(goods.supplierId, goods.skuId)">
                {{ goods.skuName }}
              </td>
              <td width="25%">
                <div class="join" @click.stop="cancelCollect(goods.skuId)">取消收藏</div>
                <div class="join" @click.stop="joinCart(goods)">加入购物车</div>
              </td>
            </tr>
          </template>
        </tbody>
      </table>
      <div class="choose-order" v-if="goodsList.length > 0">
        <t-pagination class="pagination" :total="total" :showPageNumber="true" :showPageSize="false"
          showPreviousAndNextBtn :totalContent="false" @current-change="changePage" />
      </div>
    </div>
  </div>
</template>

<script>
import { getCollectPage, cancelCollect } from '@/views/Detail/api'
import { addCount } from '@/views/ShopCart/api'
import { mapState } from 'vuex'

export default {
  data() {
    return {
      page: 1,
      goodsList: [],
      total: 0,
      loading: false
    }
  },
  created() {
    this.getCollectPage()
  },
  computed: {
    ...mapState({
      defaultRegions: state => state.defaultAddress.defaultRegions
    })
  },
  methods: {
    jumpToDetail(supplierId, skuId) {
      this.$router.push(`/sku/${skuId}`)
    },
    async cancelCollect(skuId) {
      const res = await cancelCollect({
        skuId
      })
      if (res.code === 0) {
        this.$message.success('操作成功')
        this.getCollectPage()
      } else {
        this.$message.error('操作失败')
      }
    },
    // 保留两位小数
    saveTwoDecimal(val = 0) {
      return (Math.round(val * 100) / 100).toFixed(2)
    },
    changePage(page) {
      this.page = page
      this.getCollectPage()
    },
    async joinCart(item) {
      if(!item.skuId) {
        return
      }
      this.loading = true
      try {
        const res = await addCount({
          skuId: item.skuId,
          count: 1,
          supplierId: item.supplierId,
          area: {
            provinceId: this.defaultRegions[0],
            cityId: this.defaultRegions[1],
            countyId: this.defaultRegions[2],
            townId: this.defaultRegions[3] || null,
          }
        })
        if (res.code === 0) {
          this.$store.dispatch('loadCartCount')
          this.$message.success("加入采购车成功")
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    },
    async getCollectPage() {
      const params = {
        pageNo: this.page,
        pageSize: 10
      }
      this.loading = true
      const res = await getCollectPage(params)
      if (res.code === 0 && res.data) {
        this.goodsList = res.data.list
        this.total = Number(res.data.total)
      } else {
        this.goodsList = []
        this.total = 0
      }
      this.loading = false
    }
  }
}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";
//右边
.collect-right {
  width: 83.33%;

  //订单部分
  .collect-content {
    margin: 0 20px;
    color: #666;

    //标题
    .title {
      margin-bottom: 22px;
      border: 1px solid #ddd;
      h3 {
        padding: 12px 10px;
        font-size: 16px;
        background-color: #f1f1f1;

      }
    }
    .chosetype {
      margin-bottom: 16px;
      color: #666;

      table {
        border: 1px solid #e6e6e6;
        border-collapse: separate;
        border-radius: 2px;
        width: 100%;
        max-width: 100%;
        border-spacing: 0;

        th {
          padding: 6px 8px;
          color: #666;
          font-weight: 700;
          vertical-align: bottom;
          background-color: #f4f4f4;
          line-height: 18px;
          font-size: 12px;
          text-align: left;
        }
      }
    }
    .list-table {
      border: 1px solid #e6e6e6;
      border-collapse: collapse;
      border-radius: 2px;
      width: 100%;
      margin-bottom: 18px;
      max-width: 100%;
      td {
        font-size: 12px;
        color: #666;
        padding: 6px 8px;
        line-height: 18px;
        text-align: left;
        vertical-align: middle;
      }
    }
    .list-wrapper {
      .list-item {
        border-bottom: 1px solid #e6e6e6;
        &:last-child {
          border-bottom: none;
        }
        .image-td {
          text-align: center;
          .image {
            width: 100px;
            object-fit: cover;
            cursor: pointer;
          }
        }

        .name {
          flex: 1;
          white-space: wrap;
          cursor: pointer;
        }
        .join {
          cursor: pointer;
          padding: 0 8px;
          display: inline-block;
          &:last-child {
            border-left: 1px solid #eee;
          }
          &:hover {
            color: @primary-color;
          }
        }
      }
    }
  }
}
</style>
