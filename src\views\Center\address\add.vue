<template>
  <t-dialog :visible.sync="visible" :header="dialogTitle" :closeBtn="!this.closeNotShow" :closeOnEscKeydown="!closeNotShow" :onCancel="close" :closeOnOverlayClick="false"
    :onClose="close" :footer="false" :destroyOnClose="true" width="550px">
    <t-form :data="formData" :rules="rules" ref="form" @submit="onSubmit" v-loading="loading">
      <t-form-item label="收货人" name="name">
        <t-input v-model.trim="formData.name" placeholder="请输入用户名"></t-input>
      </t-form-item>
      <t-form-item label="所在地区" name="region" class="address-label">
        <Address-center @changeAddress="changeAddress" class="address-comp"
          :defaultRegions="defaultRegions"></Address-center>
      </t-form-item>
      <t-form-item label="详细地址" name="consigneeAddress">
        <t-input v-model.trim="formData.consigneeAddress" placeholder="请输入详细地址"></t-input>
      </t-form-item>
      <t-form-item label="手机号码" name="mobile">
        <t-input-group separate>
          <t-input :style="{ width: '100px' }" default-value="0086" disabled />
          <span :style="{ lineHeight: '32px' }">&nbsp;-&nbsp;</span>
          <t-input :style="{ width: '200px' }" v-model.trim="formData.mobile" />
        </t-input-group>
      </t-form-item>
      <t-form-item label="固定电话" name="telephone">
        <t-input-group separate>
          <t-input :style="{ width: '100px' }" default-value="0086" disabled />
          <span :style="{ lineHeight: '32px' }">&nbsp;-&nbsp;</span>
          <t-input :style="{ width: '200px' }" v-model.trim="formData.telephone" />
        </t-input-group>
      </t-form-item>
      <t-form-item label="邮箱地址" name="email">
        <t-input v-model.trim="formData.email" placeholder="邮箱地址" />
      </t-form-item>
      <t-form-item label="地址别名" name="alias" help="建议填写常用名称">
        <div>
          <t-input v-model.trim="formData.alias" placeholder="地址别名" />
          <div class="alias-address">
            <t-button variant="outline" @click="formData.alias = '家里'">家里</t-button>
            <t-button variant="outline" @click="formData.alias = '父母家'">父母家</t-button>
            <t-button variant="outline" @click="formData.alias = '公司'">公司</t-button>
          </div>
        </div>
      </t-form-item>

      <t-form-item style="margin-left: 100px">
        <t-space size="10px">
          <t-button theme="primary" type="submit" :disabled="!canSave">保存收货地址</t-button>
        </t-space>
      </t-form-item>
    </t-form>
  </t-dialog>
</template>
  
<script>
import { createAddress, updateAddress } from '@/views/Center/address/api'

const INITIAL_DATA = {
  name: '',
  consigneeAddress: '',
  mobile: '',
  telephone: '',
  email: '',
  alias: '',
  provinceId: '',
  cityId: '',
  countyId: '',
  townId: '',
  provinceName: '',
  cityName: '',
  countyName: '',
  townName: ''
};

function nameValidator(val) {
  if (val.length < 2 || val.length > 10) {
    return { result: false, message: '收货人名称长度在2-10之间', type: 'error' };
  }
  return { result: true };
}

function addressValidator(val) {
  if (val.length > 100) {
    return { result: false, message: '详细地址长度不超过100', type: 'error' };
  }
  return { result: true };
}

function phoneValid(val) {
  const reg = /^1[3-9]\d{9}$/
  if (!reg.test(val)) {
    return { result: false, message: '请输入正确的手机号码', type: 'error' };
  }
  return { result: true };
}

function telValid(val) {
  const reg = /^([0-9]{3,4}-)?[0-9]{7,8}$/
  if (val && !reg.test(val)) {
    return { result: false, message: '请输入正确的固定电话', type: 'error' };
  }
  return { result: true };
}

function emailValid(val) {
  const reg = /^[a-zA-Z0-9]+((_|-|\.)[a-zA-Z0-9]+)*@([a-zA-Z0-9]+(-|\.))+[a-zA-Z]{2,5}$/
  if (val && !reg.test(val)) {
    return { result: false, message: '请输入正确的邮箱地址', type: 'error' };
  }
  return { result: true };
}

export default {
  name: 'add-address',
  data() {
    return {
      loading: false,
      canSave: true,
      visible: true,
      formData: { ...INITIAL_DATA },
      errorMessage: {
        date: '${name}不正确',
        url: '${name}不正确',
        required: '请输入${name}',
        max: '${name}字符长度不能超过 ${validate} 个字符，一个中文等于两个字符',
        min: '${name}字符长度不能少于 ${validate} 个字符，一个中文等于两个字符',
        len: '${name}字符长度必须是 ${validate}',
        pattern: '${name}不正确',
        validator: '${name}有误',
        region: '所在地区不能为空'
      },
      rules: {
        name: [
          { required: true, type: 'error' },
          { validator: nameValidator }
        ],
        consigneeAddress: [
          { required: true, type: 'error' },
          { validator: addressValidator }
        ],
        mobile: [
          { required: true, type: 'error' },
          { validator: phoneValid }
        ],
        telephone: [
          { validator: telValid }
        ],
        email: [
          { validator: emailValid }
        ]
      }
    }
  },
  props: {
    editData: {
      type: Object,
      default: () => {}
    },
    defaultRegions: {
      type: Array,
      default: () => []
    },
    closeNotShow: {
      type: Boolean,
      default: false
    }
  },
  created() {
    if (this.defaultRegions.length > 0) {
      // 说明是修改地址
      this.formData = this.editData
    }
  },
  computed: {
    dialogTitle() {
      return `${this.defaultRegions.length > 0 ? '修改' : '添加'}收货地址`
    }
  },
  methods: {
    changeAddress(val = [], labelVal) {
      this.formData.provinceId = val[0]
      this.formData.cityId = val[1]
      this.formData.countyId = val[2]
      this.formData.townId = val[3] || ''
      this.formData.provinceName = labelVal.split(' / ')[0]
      this.formData.cityName = labelVal.split(' / ')[1]
      this.formData.countyName = labelVal.split(' / ')[2]
      this.formData.townName = labelVal.split(' / ')[3] || ''
    },
    onSubmit({ validateResult, firstError }) {
      if (validateResult === true) {
        if (!this.formData.provinceId) {
          this.$message.warning('请选择地址')
          return
        }
        if (this.defaultRegions.length > 0) {
          this.updateAddress()
        } else {
          this.createAddress()
        }
      } else {
        this.$message.warning(firstError)
      }
    },
    close() {
      this.formData = { ...INITIAL_DATA }
      this.$emit('closeDialog')
    },
    async createAddress() {
      this.canSave = false
      this.loading = true
      const res = await createAddress({
        ...this.formData,
        defaulted: 0,
        timestamp: new Date().getTime()
      })
      this.loading = false
      this.canSave = true
      if (res.code === 0) {
        this.$message.success('添加地址成功')
        this.$emit('successOpt')
      } else {
        // this.$message.error('添加地址失败')
      }
    },
    async updateAddress() {
      this.canSave = false
      this.loading = true
      const res = await updateAddress({
        ...this.formData,
        timestamp: new Date().getTime()
      })
      this.loading = false
      this.canSave = true
      if (res.code === 0) {
        this.$message.success('修改地址成功')
        this.$emit('successOpt')
      } else {
        // this.$message.error('修改地址失败')
      }
    }
  }
}
</script>
  
<style lang="less" scoped>
.alias-address {
  margin: 12px 0 0;

  .t-button {
    margin-right: 6px;
  }
}
.cust1-form-item {
  height: 32px;
  margin-bottom: 24px;
}
.address-label {
  /deep/label::before {
    display: inline-block;
    margin-right: var(--td-comp-margin-xs);
    color: var(--td-error-color);
    line-height: var(--td-line-height-body-medium);
    content: "*";
  }
}
</style>
  