<template>
  <div class="message-center-con" v-if="isLogin"> 
    <div @click="showList">
      <template v-if="!toolbar">
        <t-badge :count="unreadCount" dot>
          <icon name="notification"/>
        </t-badge> 消息
      </template>
      <template v-if="toolbar">
        <t-badge :count="unreadCount">
          <icon name="notification" size="25px" style="cursor: pointer;color: var(--td-brand-color-7)"/>
        </t-badge>
      </template>
    </div>

    <t-dialog :visible.sync="shown" :footer="false" :closeBtn="false" attach="body" width="950px" class="message-dialog">
      <t-layout v-loading="loading">
        <t-header height="30px">
          <div class="flex-bewteen">
            <div class="msg-header-title">消息中心</div>
            <div class="msg-header-oper">
              <t-button variant="outline" theme="primary" :disabled="unreadCount === 0" @click="updateReadStatus()">全部已读</t-button>
            </div>
          </div>
        </t-header>
        <t-layout>
          <t-aside width="180px">
            <t-menu v-model="curMsgType" style="width:180px;" @change="menuChangeHandle">
              <t-menu-item v-for="(menu,index) in msgTypeList" :key="index" :value="menu.value">{{ menu.label }}</t-menu-item>
            </t-menu>
          </t-aside>
          <t-content>
            <t-tabs v-model="curTab" @change="tabChangeHandle">
              <t-tab-panel  v-for="(tab,index) in tabList" :key="index" :value="tab.value">
                <template #label>
                  <t-badge :count="unreadCount" v-if="tab.value === '1' && curMsgType === '10'">
                    <span>{{ tab.label }}</span>
                  </t-badge>
                  <span v-else>{{ tab.label }}</span>
                </template>
              </t-tab-panel>
            </t-tabs>

            <div class="message-data-list"> 
              <t-list v-if="list.length" split stripe>
                <t-list-item v-for="(item,index) in list" :key="index">
                  <t-list-item-meta>
                    <template #title>
                      <span><t-tag theme="primary" variant="outline">{{ getTagName(item.templateType) }}</t-tag></span>
                      <span class="title-date">{{ item.createTime | formatDateTime }}</span>
                    </template>
                    <template #description>
                      <div v-html="item.templateContent"></div>
                    </template>
                  </t-list-item-meta>
                  <template #action>
                    <span>
                      <t-link theme="primary" hover="color" style="margin-left: 16px" @click="updateReadStatus(item)" v-if="!item.readStatus">已读</t-link>
                    </span>
                  </template>
                </t-list-item>
              </t-list>
              <div v-if="list.length > 0" style="margin: 12px 0 10px;">
                <t-pagination class="pagination" :total="total" :showPageNumber="true" :showPageSize="false"
                  showPreviousAndNextBtn :totalContent="true" @current-change="changePage" />
              </div>
              <div class="nothing" v-if="!list.length && !loading">
                <t-image fit="cover" class="image" :src="emptyImg"></t-image>
                <div class="text">暂无内容</div>
              </div>
            </div>
          </t-content>
        </t-layout>
      </t-layout>
    </t-dialog>
  </div>
</template>

<script>
import { Icon } from 'tdesign-icons-vue'
import * as api from '@/components/Message/api'
import { mapState } from 'vuex'
export default {
  name: 'MessageCenter',
  components: { Icon },
  props: {
    toolbar: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  data() {
    return {
      emptyImg: require('@/assets/nothing.png'),
      shown: false,
      loading: false,
      curMsgType: '10',
      msgTypeList: [
      { label: '全部', value: '10', extParam: { templateType : null } },
      { label: '订单消息', value: '20', extParam: { templateType : 11 } },
      { label: '心愿单消息', value: '30', extParam: { templateType : 12 } },
      { label: '系统消息', value: '40', extParam: { templateType : 2 } },
      ],
      curTab: '1',
      tabList: [
        { label: '未读消息', value: '1', extParam: { readStatus : false} },
        { label: '已读消息', value: '2', extParam: { readStatus : true} }
      ],
      total: 0,
      queryParams: {
        pageNo: 1,
        pageSize: 5
      },
      list: []
    }
  },
  computed: {
    ...mapState({
      unreadCount: state => state.User.unreadCount,
      userInfo: state => state.User.userInfo,
    }),
    isLogin() {
      return this.userInfo && this.userInfo.nickname
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.firstTipShow()
    },
    getTagName(type) {
      const dic = {
        1: '通知公告',
        2: '系统消息',
        11: '订单消息',
        12: '心愿单消息'
      }
      return dic[type] || type
    },
    initContentEvent() {
      let self = this
      window.M_GO = (code, param) => {
        console.log('mg----', code, param)
        self.shown = false
        if(code === '10') {
          self.$router.push('/center/myorder')
        } else if(code === '11' && param) {
          self.$router.push('/orderDetail?no=' + param)
        } else if(code === '20') {
          self.$router.push('/center/wishlist')
        } else if(code === '21' && param) {
          let sid = param.split(',')[0]
          let sku = param.split(',')[1]
          self.$router.push(`/sku/${sku}`)
        } else if(code === '22' && param) {
          self.$router.push(`/search?keyword=/${param}`)
        } else if(code === '30') {
          self.$router.push(`/center/purchase`)
        } else if(code === '31' && param) {
          self.$router.push(`/purchaseDetail?id=${param}`)
        } else if(code === '40') {
          self.$router.push(`/center/orderAssets`)
        } else {
          return
        }
      }
    },
    showList() {
      if(!this.isLogin) {
        return
      }
      this.shown = true
      this.initContentEvent()
      this.handleSearch()
    },
    loadUnreadCount() {
      this.$store.dispatch('loadUnreadCount')  
    },
    menuChangeHandle() {
      this.handleSearch()
    },
    tabChangeHandle() {
      this.handleSearch()
    },
    async updateReadStatus(row) {
      if(row && row.id) {
        let params = {
          ids: row.id
        }
        await api.updateReadStatus(params)
        row.readStatus = true
        let index = this.list.findIndex(item => item.id === row.id)
        this.list.splice(index, 1)
        this.total--
      } else {
        await api.updateAllReadStatus()
        this.handleSearch()
      }
      this.loadUnreadCount()
    },
    changePage(page) {
      this.queryParams.pageNo = page
      this.loadList()
    },
    handleSearch() {
      this.queryParams.pageNo = 1
      this.list = []
      this.loadList()
    },
    async loadList() {
      this.loading = true
      try {
        let params = {...this.queryParams}

        let menuObj = this.msgTypeList.find(msg => msg.value === this.curMsgType)
        Object.assign(params, menuObj.extParam)

        let tabObj = this.tabList.find(tab => tab.value === this.curTab)
        Object.assign(params, tabObj.extParam)

        const res = await api.getMessagePage(params)
        this.list = res.data.list
        this.total = res.data.total
        this.loading = false
      } catch(e) {
        this.loading = false
      }
    },
    firstTipShow() {
      let indicator = sessionStorage.getItem('first-msg-tip')
      if(indicator) {
        return
      }
      sessionStorage.setItem('first-msg-tip', 'true')
      setTimeout(() => {
        if(!this.unreadCount) {
          return
        }
        this.$notify.info({
          title: '温馨提示',
          content: '您当前有未读消息，请点击右上角消息或工具栏查看',
          duration: 4000,
        })
      }, 500)
    }
  }

}
</script>

<style lang="less">
@import "@/style/variables.less";

.message-center-con {
  .t-badge {
    color: inherit;
  }
}
.message-dialog {
  .title-date {
    font-size: 13px;
    margin: 0 15px;
  }
  .message-data-list {
    height: 550px;
    overflow: auto;
    background-color: #fff;
  }
  .t-dialog--default {
    padding: 10px 15px;
  }
  .msg-header-title {
    font-size: 15px;
    font-weight: 500;
    margin-left: 50px;
    display: inline-block;
  }
  .t-layout {
    cursor: auto;
  }
  .t-list-item {
    padding: 10px 10px;
  }
}

</style>