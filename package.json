{"name": "", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode development", "build:pre": "vue-cli-service build --mode pre", "build:wdfl": "vue-cli-service build --mode wdfl", "build:prod": "vue-cli-service build --mode production", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.27.2", "core-js": "^3.8.3", "dayjs": "^1.11.10", "html2canvas": "^1.4.1", "js-base64": "^3.7.7", "js-cookie": "2.2.0", "jspdf": "^2.5.1", "lodash": "^4.17.21", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "qrcode": "^1.5.4", "qs": "^6.11.2", "sm-crypto": "^0.3.13", "tdesign-icons-vue": "^0.2.2", "tdesign-vue": "^1.4.0", "vue": "2.6.14", "vue-lazyload": "^1.3.3", "vue-router": "^3.6.5", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@types/sm-crypto": "^0.3.4", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-service": "~5.0.0", "compression-webpack-plugin": "^11.1.0", "less": "^4.1.3", "less-loader": "^11.0.0", "terser-webpack-plugin": "^5.3.10", "vue-template-compiler": "2.6.14"}, "eslintConfig": {}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}