<template>
  <div class="result-wrapper">
    <div
      class="result-item"
      v-for="item in resultData"
      :key="item.skuId"
      @click="toDetail(item)"
    >
      <div class="result-header">
        <div class="hover-collect" @click.stop="toCollect(item)">
          <heart-icon v-if="!collectList.includes(String(item.skuId))" />
          <heart-filled-icon style="color: #e11b10" v-else />
        </div>
        <div class="hover-block">
          <div class="join" :class="{'notJoin': !$bus.isStockValid(item.stockStateType) || item.skuState != 1}" @click.stop="join(item)">加入购物车</div>
          <!-- <div class="compare" @click.stop="compare">对比</div> -->
          <!-- <div class="buy" @click.stop="buy(item)">立即采购</div> -->
        </div>
        <t-image :src="item.imageUrl" class="image"></t-image>
        <div v-if="!$bus.isStockValid(item.stockStateType) || item.skuState != 1" class="no-product-mask-wrapper">
          <div class="no-product-mask">
            <span>{{ item.stockStateType | stockStateText }}</span>
          </div>
        </div>
        <div v-else-if="item.skuState !== 1" class="no-product-mask-wrapper">
          <div class="no-product-mask">
            <span style="width: 76px;">当前区域不可售</span>
          </div>
        </div>
      </div>
      <div v-if="item.salePrice != -1" class="price">￥ {{ item.salePrice | formatMoney }}
        <span class="market-price" v-if="enableMarketPrice() && item.marketPrice">￥{{ (item.marketPrice || 0) |
          formatMoney }}</span>
      </div>
      <div v-else class="need-login" style="margin-top: 20px;; margin-bottom: 5px">登录后显示价格</div>
      <div class="name">{{ item.skuName }}</div>
    </div>
    <div v-if="resultData.length === 0 && !loading" class="nothing">
      <t-image fit="cover" class="image" :src="emptySrc"></t-image>
      <div class="nothing-text">暂无商品</div>
    </div>
  </div>
</template>

<script>
import { HeartIcon, HeartFilledIcon } from "tdesign-icons-vue";
import { addCount } from '@/views/ShopCart/api'
import { addToCollect, cancelCollect, queryCollectStatus } from '@/views/Detail/api'

export default {
  name: "result",
  data() {
    return {
      collectList: [],
      skuIds: [],
      emptySrc: require('@/assets/nothing.png')
    };
  },
  props: {
    resultData: {
      type: Array,
      default: () => []
    },
    defaultRegions: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: () => true
    }
  },
  components: {
    HeartIcon,
    HeartFilledIcon,
  },
  watch: {
    resultData: {
      handler(val) {
        const ids = val.map(x => x.skuId)
        this.skuIds = ids
        this.queryCollectStatus(ids)
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async join(item) {
      if(!item.skuId) {
        return
      }
      if (!this.$bus.isStockValid(item.stockStateType) || item.skuState != 1) {
        return
      }
      const res = await addCount({
        skuId: item.skuId,
        count: item.lowestBuy || 1,
        supplierId: item.supplierId,
        area: {
          provinceId: this.defaultRegions[0],
          cityId: this.defaultRegions[1],
          countyId: this.defaultRegions[2],
          townId: this.defaultRegions[3] || null,
        }
      })
      if (res.code === 0) {
        this.$store.dispatch('loadCartCount')
        this.$message.success("加入采购车成功")
      }
    },
    async queryCollectStatus(skuIds) {
      const res = await queryCollectStatus({ skuIds })
      if (res.code === 0) {
        this.collectList = res.data
      }
    },
    compare() {
      console.log("对比");
    },
    buy(item) {
      this.$router.push(`/trade-now/${item.skuId}?supplierId=${item.supplierId}`)
    },
    toDetail(item) {
      let prefixPath = location.origin + process.env.VUE_APP_PATH_CONTEXT
      window.open(`${prefixPath}/#/sku/${item.skuId}`, '_blank')
    },
    async toCollect(item) {
      let res = null
      if (!this.collectList.includes(String(item.skuId))) {
        res = await addToCollect({
          skuId: item.skuId,
          supplierId: item.supplierId
        })
      } else {
        res = await cancelCollect({
          skuId: item.skuId,
          supplierId: item.supplierId
        })
      }
      if (res.code === 0) {
        this.$message.success('操作成功')
        this.queryCollectStatus(this.skuIds)
      } else {
        this.$message.error('操作失败')
      }
    }
  },
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.result-wrapper {
  width: 1200px;
  margin: 24px auto;
  min-height: 400px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  .result-item {
    width: calc(20% - 12px);
    overflow: hidden;
    height: 310px;
    margin-right: 15px;
    margin-top: 15px;
    border: 1px solid #E9EBF2;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    padding-left: 14px;
    padding-right: 14px;
    cursor: pointer;
    &:nth-child(5n) {
      margin-right: 0;
    }
    &:hover {
      border-color: @primary-color;
      .hover-collect {
        display: block !important;
      }
      .hover-block {
        display: flex !important;
      }
    }
    .result-header {
      width: 200px;
      height: 200px;
      margin: 14px auto 0;
      position: relative;
      .hover-collect {
        display: none;
        position: absolute;
        top: 0;
        right: 0;
        z-index: 100;
        font-size: 24px;
      }
      .hover-block {
        display: none;
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 28px;
        align-items: center;
        justify-content: flex-start;
        border: 1px solid #E9EBF2;
        cursor: pointer;
        z-index: 100;
        .join {
          width: 100%;
          text-align: center;
          background-color: @primary-color;
          color: #fff;
          border-right: 1px solid #E9EBF2;
          padding: 5px 3px;
        }
        .notJoin {
          cursor: not-allowed;
          background-color: #b5c7ff;
        }
        .compare,
        .buy {
          flex: 1;
          text-align: center;
          background-color: #fff;
          color: @primary-color;
          padding: 5px 6px;
        }
        .compare {
          border-right: 1px solid #E9EBF2;
          padding: 5px 10px;
        }
      }
      .image {
        width: 100%;
        height: 100%;
      }
      .no-product-mask-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.4);
        .no-product-mask {
          position: absolute;
          top: 50%;
          margin-top: -48px;
          left: 50%;
          margin-left: -48px;
          width: 96px;
          height: 96px;
          border-radius: 50%;
          background: rgba(0, 0, 0, 0.5);
          text-align: center;
          color: #fff;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 16px;
          z-index: 100;
        }
      }
    }
    .price {
      font-size: 18px;
      margin-top: 20px;
      line-height: 20px;
      color: @primary-color;
    }
    .name {
      color: #333;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }
  }
  .nothing {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .image {
      margin-top: 40px;
      margin-bottom: 24px;
      width: 360px;
    }
    .nothing-text {
      font-size: 24px;
      margin-bottom: 24px;
    }
  }
}
</style>
