# mall

## Project setup

```
npm install
```

### Compiles and hot-reloads for development

```
npm run serve
```

### Compiles and minifies for production

```
npm run build
```

### Lints and fixes files

```
npm run lint
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).

打包：
npm run build:wdpre
tar -czvf mall-pc.tar.gz dist/\*

解压：
tar -xzvf mall-pc.tar.gz -C mall-pc
