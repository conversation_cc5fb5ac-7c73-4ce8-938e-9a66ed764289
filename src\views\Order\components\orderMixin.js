import { mapState } from 'vuex'
import { deleteOrder } from '@/views/Trade/api'
import { getAfterSaleComponentUrl, getAssetsLink, confirmReceiveByOrder } from '@/views/Order/api'
export const orderMixins = {
  data() {
    return {}
  },
  computed: {
    ...mapState({
      configData: state => state.Home.configData,
      extConfig: state => state.Home.extConfig
    })
  },
  methods: {
    refresh() {},
    afterDelete() {},
    toOrderDetail(no) {
      this.$router.push('/orderDetail?no=' + no)
    },
    toProductDetail(supplierId, item) {
      this.$router.push(`/sku/${item.skuId}`)
    },
    canAfterSale(order, orderItem) {
      return [2, 3, 4, 5, 6, 7, 8, 9].includes(order.status) && orderItem.canAfterSale;
    },
    async toAfterSale(order, orderItem) {
      if(order.supplierType === 1) {
        const res = await getAfterSaleComponentUrl({
          orderItemId: orderItem.id || orderItem.orderItemId
        })
        if (res.code === 0) {
          window.open(res.data, '_blank')
        }
      } else if(order.status !== 9) {
        this.$router.push({
          path: '/center/after-sale',
          query: {
            no: order.no,
            itemId: orderItem.id || orderItem.orderItemId
          }
        })
      }
    },
    getAfterSaleBtnText(order, orderItem) {
      let status = orderItem.afterSaleStatus
      if(status === 1) {
        return '退款中'
      } else if(status === 2) {
        return '退款完成'
      } else if(orderItem.afterSaleMemo ) {
        return orderItem.afterSaleMemo
      } else if(order && order.status !==9 ){
        return '售后申请'
      } 
      return ''
    },
    needPay(order) {
      return [5,7].includes(order.paymentMethod) && order.status === 1 && !order.payed
    },
    toPay(order) {
      this.$router.push({
        path: '/pay',
        query: {
          no: order.no
        }
      })
    },
    canComment(orderItem) {
      return !orderItem.commented && orderItem.canComment
    },
    toComment(order, orderItem) {
      this.$router.push(`/center/comment?no=${order.no}&skuId=${orderItem.skuId}`)
    },
    canAssetGo(order, orderItem) {
      return [1].includes(orderItem.assetStatus) && ![9].includes(order.status)
    },
    async toAssetsSys(orderItem) {
      const res = await getAssetsLink({
        orderItemId: orderItem.id || orderItem.orderItemId
      })
      if(res.code === 0 && res.data) {
        window.open(res.data, '_blank')
      }
    },
    canApprove(order) {
      return (this.configData.approveSwitch || this.configData.projectSwitch) && order.status === 1 && [1,3].includes(order.auditStatus)
    },
    toApprove(order) {
      this.$router.push({
        path: '/approve',
        query: {
          no: order.no
        }
      })
    },
    canReceive(order) {
      return [3,4].includes(order.status)
    },
    async receiveOrder(order) {
      let mydialog = this.$dialog({
        header: '提示',
        body: '您确认要收货吗？',
        onConfirm: async () => {
          const res = await confirmReceiveByOrder({
            orderNo: order.no
          })
          if (res.code === 0) {
            this.$message.success('已确认收货！')
            this.refresh()
          }
          mydialog.hide()
        },
        onClose: () => {
          mydialog.hide()
        }
      })
    },
    canCancel(order) {
      return order.status === 1
    },
    canDelete(order) {
      return order.status === 9
    },
    deleteOrder(order) {
      let mydialog = this.$dialog({
        header: '提示',
        body: '您确定要删除该订单吗？',
        onConfirm: async () => {
          const res = await deleteOrder(order.no)
          if (res.code === 0) {
            this.$message.success('删除订单成功')
            this.afterDelete()
          } else {
            this.$message.error('删除订单失败')
          }
          mydialog.hide()
        },
        onClose: () => {
          mydialog.hide()
        }
      })
    }
  }
}