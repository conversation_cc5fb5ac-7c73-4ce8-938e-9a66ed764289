.nothing {
  width: 100%;
  background-color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .image, .empty-image {
    height: 170px;
    margin: 30px 0;
    background-color: white;
  }
}


.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis; 
}

.rich-content {
  img {
    max-width: 100% !important;
    display: inherit;
    vertical-align: top;
    outline-width: 0px;
  }
}

.market-price {
  font-size: 0.85em;
  color: #808080ad;
  text-decoration: line-through;
  margin-left: 6px;
}

.need-login {
  font-size: 0.85em;
  color: #808080ad;
}

.tag-supplier {
  font-size: 0.9em;
  color: #808080ad;
  position: absolute;
  left: 6px;
  bottom: 1px;
}

.flex-column {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.flex-column-start {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-direction: column;
}

.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-bewteen {
  display: flex;
  align-items: center;
  justify-content: space-between;
}