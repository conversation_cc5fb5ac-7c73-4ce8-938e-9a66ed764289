<template>
  <div class="InfoWrap">
    <div class="goodsDetail">
      <div class="InfoName">{{ detailData.skuName }}</div>
      <div class="priceArea">
        <div class="title">销售价</div>
        <div class="skuprice">
          <div v-if="detailData.salePrice != -1" class="symbol">¥</div>
          <div v-if="detailData.salePrice != -1">{{ detailData.salePrice === undefined ? '--' : detailData.salePrice }}</div>
          <div v-else class="need-login">{{ '登录后显示' }}</div>
        </div>
        <div class="tip-price" v-if="enableMarketPrice() && detailData.salePrice != -1">
          <div class="tip-symbol">市场价 </div>
          <div class="through-line"> ¥{{ detailData.marketPrice === undefined ? '--' : detailData.marketPrice }}</div>
        </div>
      </div>
      <div class="info-item">
        <div class="info-label">供应商</div>
        <div class="info-content" style="display: flex;align-items: center;cursor: pointer;" @click="jumpSupplier">
          <img style="width: 24px;height: 24px;margin-right: 4px;" :src="detailData.logoUrl" v-if="false" />
          {{ detailData.supplierName }}
        </div>
      </div>
      <div class="info-item">
        <div class="info-label">品牌</div>
        <div class="info-content">{{ detailData.brandName }}</div>
      </div>
      <!-- <div class="info-item">
        <div class="info-label">销量</div>
        <div class="info-content">{{ detailData.brandName }}{{ detailData.saleUnit }}</div>1
      </div> -->
      <div class="info-item">
        <div class="info-label">购买区域</div>
        <div class="info-content">
          <Address style="width: 400px" @changeAddress="changeAddress"></Address>
        </div>
      </div>
      <div class="info-item">
        <div class="info-label">是否有货</div>
        <div class="info-content">
          {{ detailData.skuState == 0 ? '商品已下架' : stockObj.stockStateType | stockStateText }}
          <span v-if="[33].includes(stockObj.stockStateType)">（下单立即发货）</span>
          <span v-if="skuPredictInfo.predictContent" v-html="`${skuPredictInfo.predictContent || ''}`"
            class="predictText"></span>
        </div>
      </div>
      <div class="info-item" v-if="detailData.returnRuleStr">
        <div class="info-label">温馨提示</div>
        <div class="info-content">
          <span>{{ detailData.returnRuleStr }}</span>
        </div>
      </div>
      <div class="choose-specs" v-if="brotherSkuSpecs && brotherSkuSpecs.length">
        <div v-for="spec in brotherSkuSpecs" :key="spec.specName" class="spec-item">
          <div class="spec-name">{{ spec.specName }}</div>
          <div class="spec-values">
            <button v-for="value in spec.specValues" :key="value.specValue" class="spec-value" :class="{
              selected: value.selected,
            }" @click="selectSpec(spec.specName, value)">
              <img v-if="value.imagePath" :src="value.imagePath" class="spec-image" />
              <span v-if="!value.selected && value.hasStock != undefined && !value.hasStock"
                class="no-stock-tip">无货</span>
              {{ value.specValue }}
            </button>
          </div>
        </div>
      </div>
      <div class="buy-item">
        <div class="info-label">购买数量</div>
        <div class="info-content">
          <t-input-number v-model="skuNum" @change="changeSkuNum" :min="1" :max="99999" :allowInputOverLimit="false"
            style="width:170px;" />
        </div>
        <div class="btn">
          <t-button
            :disabled="!$bus.isStockValid(stockObj.stockStateType) || detailData.skuState != 1 || detailData.saleStatus != 1"
            theme="primary" class="inCart" @click="toCart">加入采购车</t-button>
          <t-button
            :disabled="!$bus.isStockValid(stockObj.stockStateType) || detailData.skuState != 1 || detailData.saleStatus != 1"
            theme="primary" variant="outline" class="buy" @click="toProcurement">立即购买</t-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { addCount } from '@/views/ShopCart/api'
import { queryGoodsStockInfo, getNewStockById, getSkuPredictPromise } from '@/views/Detail/api'
import { mapState } from 'vuex'
export default {
  name: "detailInfo",
  data() {
    return {
      skuNum: 1,
      areaIds: [],
      stockObj: {},
      newStockObjs: [],
      skuPredictInfo: {},
      selectedSpecs: {},
    };
  },
  props: {
    detailData: {
      type: Object,
      default: () => { }
    },
    brotherSkuSpecs: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState({
      defaultRegions: state => state.defaultAddress.defaultRegions
    }),
  },
  created() {
    this.skuNum = this.detailData.lowestBuy || 1
  },
  watch: {
    detailData: {
      handler(newVal, oldVal) {
        this.init()
      },
    },
  },
  methods: {
    init() {
      if (this.detailData.skuState != 0) {
        this.getNewStockById(this.brotherSkuSpecs)
        this.loadSkuPredictPromise()
      }
    },
    changeAddress(val = []) {
      this.areaIds = val
      this.$emit('changeAddress', this.areaIds)
      if (this.detailData.skuState != 0) {
        this.loadSkuPredictPromise()
        this.getNewStockById(this.brotherSkuSpecs)
      }
    },
    changeSkuNum(value) {
      const minNum = this.detailData.lowestBuy || 1
      if (!/^(([1-9]\d+)|([2-9]))$/.test(value) || value < minNum) {
        this.skuNum = minNum;
      }
    },
    toProcurement() {
      this.$router.push(`/trade-now/${this.detailData.skuId}?num=${this.skuNum}&supplierId=${this.detailData.supplierId}`)
    },
    async loadSkuPredictPromise() {
      if (!this.detailData.isJD) {
        return
      }
      let params = {
        skuId: this.detailData.skuInnerId,
        skuNum: this.skuNum,
        supplierId: this.detailData.supplierId,
        provinceId: this.areaIds[0],
        cityId: this.areaIds[1],
        countyId: this.areaIds[2],
        townId: this.areaIds[3] || null
      }
      let res = await getSkuPredictPromise(params)
      if (res.code === 0 && res.data) {
        this.skuPredictInfo = res.data
      }
    },
    async toCart() {
      if (!this.detailData.skuId) {
        return
      }
      const res = await addCount({
        skuId: this.detailData.skuId,
        count: this.skuNum,
        supplierId: this.detailData.supplierId,
        area: {
          provinceId: this.areaIds[0],
          cityId: this.areaIds[1],
          countyId: this.areaIds[2],
          townId: this.areaIds[3] || null,
        }
      })
      if (res.code === 0) {
        this.$store.dispatch('loadCartCount')
        this.$message.success("加入采购车成功")
      }
    },
    async queryGoodsStockInfo() {
      const res = await queryGoodsStockInfo({
        skuId: this.detailData.skuId,
        supplierId: this.detailData.supplierId,
        area: {
          provinceId: this.areaIds[0],
          cityId: this.areaIds[1],
          countyId: this.areaIds[2],
          townId: this.areaIds[3] || null,
        }
      })
      if (res.code === 0) {
        this.stockObj = res.data
      }
    },
    async getNewStockById() {
      var skuIds = []
      this.brotherSkuSpecs.forEach(item => {
        item.specValues.forEach(value => {
          skuIds.push(...value.skuIdList);
        })
      })

      skuIds = [...new Set(skuIds)];

      if (!skuIds.includes(this.detailData.skuId)) {
        skuIds.push(this.detailData.skuId)
      }

      const res = await getNewStockById({
        skuIds: skuIds,
        supplierId: this.detailData.supplierId,
        provinceId: this.areaIds[0],
        cityId: this.areaIds[1],
        countyId: this.areaIds[2],
        townId: this.areaIds[3] || null,
      })
      if (res.code === 0) {
        this.newStockObjs = res.data
        // 计算specValue的库存状态
        this.brotherSkuSpecs.forEach(item => {
          item.specValues.forEach(value => {
            var newStock = this.calcSkuStock(item.specName, value, res.data)
            if (newStock != null) {
              value['stockStateType'] = newStock.stockStateType
              value['hasStock'] = [33, 39, 40].includes(newStock.stockStateType)
            }
          })
        })

        res.data.forEach(newStock => {
          if (newStock.skuId === this.detailData.skuId) {
            this.stockObj = newStock
          }
        })
      }
    },
    calcSkuStock(specName, value, stock) {
      // console.log(JSON.stringify(this.brotherSkuSpecs, null, 4));
      // 假设当前specValue是选中状态，计算选中sku，再查询sku的库存状态
      // 收集所有选中的 spec 对应的 skuId
      const selectedSkuLists = this.brotherSkuSpecs.map(spec => {
        // 选中项的 SKU ID 列表
        const selectedValues = spec.specValues.filter(item => {
          if (value.specValue == item.specValue) {
            return true;
          }
          else if (specName != spec.specName && item.selected) {
            return true;
          }
          else {
            return false;
          }
        });
        return selectedValues.length > 0 ? selectedValues[0].skuIdList : [];
      });

      // 计算多个 SKU 列表的交集
      const findCommonElements = (arrays) => {
        return arrays.reduce((result, current) => {
          return result.filter(element => current.includes(element));
        });
      }
      const selectedSkuIds = findCommonElements(selectedSkuLists);

      // 如果有多个交集的 skuId，跳转到第一个匹配的详情页
      if (selectedSkuIds.length > 0) {
        const selectedSkuId = selectedSkuIds[0];  // 选取第一个交集中的 skuId
        const newStock = stock.find(stock => stock.skuId === selectedSkuId);
        return newStock;
      }

      return null;
    },
    jumpSupplier() {
      this.$router.push({
        path: '/supplier',
        query: {
          id: this.detailData.supplierId
        }
      })
    },
    selectSpec(specName, specValue) {
      // 如果当前值已经选中，不做任何操作
      if (specValue.selected) {
        return;
      }

      var selectedValues = [];

      // 获取当前选中的 specName 对应的所有选项
      const spec = this.brotherSkuSpecs.find(spec => spec.specName === specName);
      if (spec) {
        spec.specValues.forEach(value => {
          // 选中当前 specValue 并取消其他值的选中状态
          if (value.specValue === specValue.specValue) {
            value.selected = true;
            selectedValues.push(value);
          } else {
            value.selected = false;
          }
        });
      }

      // 找出其他specName中选中的specValue
      this.brotherSkuSpecs.forEach(specItem => {
        if (specItem.specName !== specName) {
          specItem.specValues.forEach(value => {
            if (value.selected) {
              selectedValues.push(value);
            }
          })
        }
      })

      // 计算当前specValue以外的其他selectedValue是否还应该是选中状态
      const set = new Set(specValue.skuIdList)
      selectedValues.forEach(selectedValue => {
        if (specValue.specValue != selectedValue.specValue) {
          if (!selectedValue.skuIdList.some(item => set.has(item))) {
            selectedValue.selected = false
          }
        }
      })

      // 计算specValue的库存状态
      this.brotherSkuSpecs.forEach(item => {
        item.specValues.forEach(value => {
          var newStock = this.calcSkuStock(item.specName, value, this.newStockObjs)
          if (newStock != null) {
            value['stockStateType'] = newStock.stockStateType
            value['hasStock'] = [33, 39, 40].includes(newStock.stockStateType)
          }
          else {
            value['stockStateType'] = 34
            value['hasStock'] = true
          }
        })
      })

      // 计算并跳转到选中的 skuId 详情页
      this.getSelectedSkuId();
    },
    getSelectedSkuId() {
      // 收集所有选中的 spec 对应的 skuId
      const selectedSkuLists = this.brotherSkuSpecs.map(spec => {
        // 选中项的 SKU ID 列表
        const selectedValues = spec.specValues.filter(value => value.selected);
        return selectedValues.length > 0 ? selectedValues[0].skuIdList : [];
      });

      // 计算多个 SKU 列表的交集
      const findCommonElements = (arrays) => {
        return arrays.reduce((result, current) => {
          return result.filter(element => current.includes(element));
        });
      }
      const selectedSkuIds = findCommonElements(selectedSkuLists);

      // 如果有多个交集的 skuId，跳转到第一个匹配的详情页
      if (selectedSkuIds.length > 0) {
        const selectedSkuId = selectedSkuIds[0];  // 选取第一个交集中的 skuId
        this.toDetail(selectedSkuId);
      }
    },
    toDetail(skuId) {
      this.$router.push(`/sku/${skuId}`)
    }
  }
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.InfoWrap {
  flex: 1;
  margin-left: 24px;

  .InfoName {
    font-size: 18px;
    font-weight: bold;
    margin-top: 15px;
    line-height: 26px;
  }

  .predictText {
    margin-left: 10px;
    font-size: 0.9em;
    color: gray
  }

  .priceArea {
    background-color: rgba(225, 27, 16, 0.04);
    border: 1px solid rgba(225, 27, 16, 0.3);
    height: 60px;
    line-height: 60px;
    margin-top: 20px;
    display: flex;
    border-radius: 4px;

    .title {
      color: #999;
      margin: 0;
      margin-left: 15px;
      width: 100px;
    }

    .skuprice {
      display: flex;
      font-weight: bold;
      color: #e11b10;
      font-size: 24px;

      .symbol {
        font-size: 14px;
      }
    }

    .tip-price {
      margin-left: 20px;
      color: gray;
      display: flex;
      padding-top: 10px;

      .tip-symbol {
        font-size: 0.9em;
      }

      .through-line {
        text-decoration: line-through;
      }
    }
  }

  .info-item {
    width: 100%;
    display: flex;
    line-height: 50px;
    height: 50px;
    border-bottom: 1px solid #E9EBF2;

    .info-label {
      width: 100px;
      color: #999;
      margin-left: 15px;
    }

    .info-content {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .buy-item {
    width: 100%;
    display: flex;
    line-height: 80px;
    height: 80px;

    .info-label {
      width: 100px;
      color: #999;
      margin-left: 15px;
    }

    .info-content {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .btn {
      .inCart {
        width: 200px;
        margin-right: 24px;
      }

      .buy {
        width: 200px;
      }
    }
  }

  .choose-specs {
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    border-bottom: 1px solid #E9EBF2;
  }

  .spec-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
  }

  .spec-name {
    width: 100px;
    text-align: left;
    flex-shrink: 0;
    color: #999;
    margin-left: 15px;
  }

  .spec-values {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: flex-start;
  }

  .spec-value {
    padding: 8px 15px;
    font-size: 12px;
    text-align: left;
    background-color: white;
    border: 1px solid #d3d3d3;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    position: relative;
    /* Make the button container relative to position the "no stock" text */
  }


  .spec-value.selected {
    border: 1px solid #e11b10;
    color: #e11b10;
  }

  .spec-value.disabled {
    opacity: 0.6;
  }

  .spec-value.stock {
    color: #e11b10;
  }

  .spec-image {
    width: 20px;
    height: 20px;
    margin-right: 6px;
  }

  .no-stock-tip {
    position: absolute;
    top: 0;
    right: 0;

    background-color: #bfbfbf;
    padding: 3px 4px;
    height: 13px;
    line-height: 8px;
    border-bottom-left-radius: 3px;
    border-top-right-radius: 3px;
    color: #fff;
    font-size: 10px;
    transform: translate(0%, -50%);
  }
}
</style>
