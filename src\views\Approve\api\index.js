import request from '@/base/service'

const requestA = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_TRADE_API
    }
  })
}

// 查询用户项目列表
export function getProjectList(params) {
  return requestA({
    url: '/purchase/get-project-list',
    method: 'get',
    params
  })
}

// 查询用户项目详情
export function getProjectDetail(params) {
  return requestA({
    url: '/purchase/get-project-detail',
    method: 'get',
    params
  })
}

// 查询审批人员
export function getApprovalUser(data) {
  return requestA({
    url: '/purchase/get-approval-user',
    method: 'post',
    data
  })
}

// 检查项目卡余额
export function confirmReceiveByOrder(data) {
  return requestA({
    url: '/purchase/check-balance',
    method: 'post',
    data
  })
}

// 查询用户的采购购物车详情
export function getApprovalDetail(params) {
  return requestA({
    url: '/purchase/get-detail',
    method: 'get',
    params
  })
}

// 更新采购购物车商品是否选中
export function updateSelected(data) {
  return requestA({
    url: '/purchase/update-selected',
    method: 'post',
    data
  })
}

// 采购提交
export function submitPurchase(data) {
  return requestA({
    url: '/purchase/submit-purchase',
    method: 'post',
    data
  })
}

// 上传采购附件
export function uploadAttachments(data) {
  return request({
    url: '/infra/file/upload',
    method: 'post',
    data
  })
}

// 查询用户项目卡信息
export function getYcrhConfig() {
  return request({
    url: '/mall/ycrh-config/get',
    method: 'get'
  })
}

// 查询员工详情
export function getYgInfo(params) {
  return requestA({
    url: '/purchase/get-yg-info',
    method: 'get',
    params
  })
}

// 查询用户项目卡额度信息
export function getProjectBalanceInfo(params) {
  return requestA({
    url: '/purchase/get-balance-info',
    method: 'get',
    params
  })
}


// 查询经济分类
export function getEconomyClassList(params) {
  return request({
    url: '/mall/economic-classification/list',
    method: 'get',
    params
  })
}