<template>
  <div class="pay-main">
    <t-loading :loading="true" text="支付处理中..." fullscreen />
    <form id="submit_form" method="post" :action="serverUrl">
      <input type="hidden" name="json" :value="json"/>
      <input type="hidden" name="signature" :value="signature"/>
    </form>
  </div>
</template>

<script>

import { submitPayOrder} from '@/views/Pay/api'
import { Base64 } from 'js-base64';
export default {
  name: 'PayBridge',
  data() {
    return {
      payInfo: {}
    }
  },
  computed: {
    orderNo() {
      return this.$route.query.orderNo
    },
    payPrice() {
      return this.$route.query.payPrice
    },
    oType() {
      return this.$route.query.oType
    },
    payOrderId() {
      return this.$route.query.payOrderId
    },
    channelCode() {
      return this.$route.query.channelCode
    },
    params() {
      let content = this.payInfo.displayContent || ''
      return content.split('||')
    },
    serverUrl() {
      if(this.params && this.params.length > 1) {
        return this.params[1]
      }
      return ''
    },
    json() {
      if(this.params && this.params.length > 2) {
        return this.params[2]
      }
      return ''
    },
    signature() {
      if(this.params && this.params.length) {
        return this.params[0]
      }
      return ''
    },
    userInfo() {
      let userinfo = sessionStorage.getItem('session-userinfo')
      if(userinfo) {
        userinfo = JSON.parse(userinfo)
      }
      return userinfo || {}
    }
  },
  mounted() {
    this.submitPayment()
  },
  methods: {
    buildReturnUrl() {
      let ctxPath = process.env.VUE_APP_PATH_CONTEXT
      let redirect = location.origin + ctxPath + `/#/paysuccess?no=${this.orderNo}&oType=${this.oType}`
      redirect = Base64.encode(redirect)
      let url = process.env.VUE_APP_PAY_API + `/common/front-call-back?payId=${this.$route.query.payOrderId}&callbackUrl=${redirect}`
      return url
    },
    async submitPayment() {
      if(!this.payOrderId || !this.channelCode) {
        this.$message.error('必选参数为空')
        return
      }
      let params = {
        id: this.payOrderId,
        channelCode: this.channelCode,
        returnUrl: this.buildReturnUrl()
      }
      if(this.channelCode === 'axinfu_web') {
        params.channelExtras = {
          userNo: this.userInfo.userNo,
          userName: this.userInfo.name,
          clientType: 'web'
        }
      }

      console.log('params====', params)
      try {
        let res = await submitPayOrder(params)
        if(res.code === 0) {
          this.payInfo = res.data
          this.$nextTick(() => { 
            console.log('params: ', this.params)
            console.log('userInfo: ', this.userInfo)
            document.forms[0].submit();
          })
        }
      } catch(e) {
        this.$router.push('/')
      }
    }
  }
}
</script>