import { getConfig, getHomeConfig, getTopList } from "@/views/Home/api"
import { MessagePlugin  } from 'tdesign-vue'
import router from "@/router";

const state = {
  inited: false,
  initializing: false,
  standard: true, // 是标品还是其他大学的
  configData: {},
  extConfig: {},
  homeConfigData: {},
  styleConfigData: {},
  supplierList: []
}

const mutations = {
  setConfigData(state, configData) {
    const rootElement = document.documentElement;
    // 福利平台，按照主题是100来做
    rootElement.setAttribute('theme-mode', 100);
    state.configData = configData
    state.extConfig = configData.extConfig || {}
  },
  setInited(state, inited) {
    state.inited = inited
  },
  setInitializing(state, initializing) {
    state.initializing = initializing
  },
  setHomeConfigData(state, homeConfigData) {
    state.homeConfigData = homeConfigData
  },
  setStyleConfigData(state, styleConfigData) {
    state.styleConfigData = styleConfigData
    const rootElement = document.documentElement;
    styleConfigData.themeCode = '100'
    styleConfigData.logoUrl = require("@/components/FixHeader/images/wdfl.png"),
    // 默认是100主题红,110是高校主题
    rootElement.setAttribute('theme-mode', styleConfigData.themeCode || '100');
    state.standard = !styleConfigData.themeCode || styleConfigData.themeCode == '100'
    // 同步修改favicon
    let faviconUrl = styleConfigData.faviconUrl
    if(faviconUrl && faviconUrl.length > 5) {
      let link = document.querySelector("link[rel*='icon']") || document.createElement('link');
      link.type = 'image/x-icon';
      link.rel = 'shortcut icon';
      link.href = faviconUrl + "?rn=" + new Date().getTime();
      document.getElementsByTagName('head')[0].appendChild(link);
    }
    // document.querySelector(':root').style.cssText = styleConfigData.diyCss
  },
  setSupplierList(state, supplierList) {
    state.supplierList = supplierList || []
  }
}

const actions = {
  async getConfigData({ commit }) {
    let host = location.host || ''
    console.log('process.env.VUE_APP_TENANT_HOST====', process.env.VUE_APP_TENANT_HOST)
    if (process.env.NODE_ENV === 'development') {
      host = process.env.VUE_APP_TENANT_HOST
    }
    let params = { host: host  }
    let cacheKey = 'jcymall-basisconfig-v2'
    let cacheVal = localStorage.getItem(cacheKey)
    if(cacheVal) {
      try {
        commit('setConfigData', JSON.parse(cacheVal))
      } catch(e) {}
    }
    const res = await getConfig(params)
    if(res.data.tenantId) {
      commit('setConfigData', res.data)
      localStorage.setItem(cacheKey, JSON.stringify(res.data))
      return res.data
    } else {
      router.push('/error-page')
      MessagePlugin.error('商城基础配置异常，请联系管理员')
      return false
    }
  },
  async getHomeConfigData({ commit }) {
    let cacheHomeKey1 = 'jcymall-homeconfig-v2'
    let cacheHomeKey2 = 'jcymall-styleconfig-v2'
    // 首页配置一般变化频率很低，加上缓存后，页面刷新或进入时能减少页面样式过渡及闪烁
    let cacheHomeVal1 = localStorage.getItem(cacheHomeKey1)
    let cacheHomeVal2 = localStorage.getItem(cacheHomeKey2)
    if(cacheHomeVal1) {
      commit('setHomeConfigData', JSON.parse(cacheHomeVal1))
    }
    if(cacheHomeVal2) {
      commit('setStyleConfigData', JSON.parse(cacheHomeVal2))
    }
    let params = { platform: 'pc' }
    const res = await getHomeConfig(params)
    if(res.code === 0 && res.data) {
      res.data.forEach(item => {
        if (item.type === 10) {
          commit('setHomeConfigData', JSON.parse(item.content))
          localStorage.setItem(cacheHomeKey1, item.content)
        }
        if (item.type === 11) {
          commit('setStyleConfigData', JSON.parse(item.content))
          localStorage.setItem(cacheHomeKey2, item.content)
        }
      })
    } else {
      MessagePlugin.error('商城首页配置信息异常，请联系管理员')
      return false
    }
  },
  async getSupplierList({ commit }) {
    const res = await getTopList()
    if(res.code === 0) {
      commit('setSupplierList', res.data)
      return res.data
    } else {
      console.log('供应商查询失败...')
      return false
    }
  }
}

const getters = {

}

export default {
  state, mutations, actions, getters
}