<template>
  <t-cascader v-model="region" value-type="full" :placeholder="placeholder" :load="load" @change="handleChange"
    :options="regionOptions" :input-props="inputProps" clearable />
</template>
  
<script>
import { getAreas } from './api'

export default {
  name: 'address-center',
  data() {
    return {
      regionOptions: [],
      placeholder: '请选择所在地区',
      region: [],
      inputProps: {
        value: '',
      }
    }
  },
  created() {
    this.getProvince()
  },
  props: {
    defaultRegions: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    async getProvince() {
      const res = await getAreas({
        addressLevel: 'PROVINCE',
        areaId: ''
      })
      if (res.code === 0) {
        this.regionOptions = res.data.map(x => {
          return {
            label: x.areaName,
            value: String(x.areaId),
            children: true
          }
        })
        if (this.defaultRegions && this.defaultRegions.length > 0) {
          const province = this.regionOptions.find(x => x.value == this.defaultRegions[0]).label
          let res = await getAreas({
            addressLevel: 'CITY',
            areaId: this.defaultRegions[0]
          })
          const cityList = res.data
          const city = cityList.find(x => x.areaId == this.defaultRegions[1]).areaName
          res = await getAreas({
            addressLevel: 'COUNTRY',
            areaId: this.defaultRegions[1]
          })
          const countyList = res.data
          const county = countyList.find(x => x.areaId == this.defaultRegions[2]).areaName
          if (this.defaultRegions[3]) {
            res = await getAreas({
              addressLevel: 'TOWN',
              areaId: this.defaultRegions[2]
            })
            const townList = res.data
            const town = townList.find(x => x.areaId == this.defaultRegions[3]).areaName
            this.inputProps.value = `${province} / ${city} / ${county} / ${town}`
          } else {
            this.inputProps.value = `${province} / ${city} / ${county}`
          }
          this.region = this.defaultRegions.map(x => String(x))
        }
      }
    },
    async load(node) {
      const list = ['PROVINCE', 'CITY', 'COUNTRY', 'TOWN']
      const res = await getAreas({
        addressLevel: list[node.level + 1],
        areaId: node.value
      })
      const special_provinces = ['北京', '天津', '重庆', '上海']
      let hasChildren = node.level < 1
      if(node.level === 1 && !special_provinces.includes(node.parent?.label)) {
        hasChildren = true
      }

      if (res.code === 0) {
        
        return res.data.map(x => {
          return {
            label: x.areaName,
            value: String(x.areaId),
            children: hasChildren
          }
        })
      }
    },
    handleChange(value, context) {
      const { node } = context;
      if (node) {
        const path = node.getPath();
        const labelPath = path.map((item) => item.label).join(' / ');
        this.inputProps.value = labelPath;
      } else {
        this.inputProps.value = ''
      }
      this.$emit('changeAddress', this.region, this.inputProps.value)
    }
  }
}
</script>
  
<style lang="less" scoped>
</style>
  