<template>
  <div class="login-container">
    <!-- 登录 -->
    <div class="left">
      <t-image class="image" :src="logoImg"></t-image>
    </div>
    <div class="right">
      <div class="title">欢迎登录！</div>
      <div class="right-tabs">
        <t-tabs v-model="value">
          <t-tab-panel value="account">
            <template #label>
              <span class="tabs-name">账号登录</span>
            </template>
          </t-tab-panel>
          <!-- <t-tab-panel value="phone">
            <template #label>
              <span class="tabs-name">手机登录</span>
            </template>
          </t-tab-panel> -->
        </t-tabs>
      </div>
      <div class="right-form">
        <t-form
          :data="formData"
          :rules="rules"
          ref="form"
          @reset="onReset"
          @submit="onSubmit"
          :colon="true"
          labelAlign="top"
        >
          <template v-if="value === 'account'">
            <t-form-item name="phone" label="手机号">
              <t-input
                clearable
                size="large"
                v-model.trim="formData.phone"
                :maxlength="13"
                placeholder="请输入手机号"
              >
                <desktop-icon slot="prefix-icon"></desktop-icon>
              </t-input>
            </t-form-item>
            <t-form-item name="password" label="密码">
              <t-input
                type="password"
                clearable
                size="large"
                v-model="formData.password"
                :maxlength="50"
                placeholder="请输入密码"
              >
                <lock-on-icon slot="prefix-icon"></lock-on-icon>
              </t-input>
            </t-form-item>
          </template>

          <template v-else>
            <t-form-item name="phone" label="手机号">
              <t-input
                clearable
                size="large"
                v-model.trim="formData.phone"
                :maxlength="13"
                placeholder="请输入手机号"
              >
                <desktop-icon slot="prefix-icon"></desktop-icon>
              </t-input>
            </t-form-item>
            <t-form-item name="code" label="验证码">
              <div style="display: flex">
                <t-input
                  clearable
                  size="large"
                  v-model.trim="formData.code"
                  placeholder="请输入验证码"
                  style="width: 300px"
                >
                  <lock-on-icon slot="prefix-icon"></lock-on-icon>
                </t-input>
                <t-button
                  style="margin-left: 20px"
                  size="large"
                  class="login"
                  @click="getCode"
                  >获取验证码</t-button
                >
              </div>
            </t-form-item>
          </template>
          <t-form-item>
            <t-button theme="primary" class="login" type="submit" block
              >登录</t-button
            >
          </t-form-item>
          <!-- <t-form-item>
            <div class="bottom">
              <div>
                没有账号？
                <span class="red"> 供应商注册 </span>
              </div>
              <div class="red">忘记密码</div>
            </div>
          </t-form-item> -->
        </t-form>
      </div>
    </div>
  </div>
</template>

<script>
import { DesktopIcon, LockOnIcon } from "tdesign-icons-vue";
import { mapState } from 'vuex'
import { encryptSm2 } from "@/utils/util"

const INITIAL_DATA = {
  account: "",
  password: "",
  phone: "",
  code: "",
};

export default {
  name: "Login",
  data() {
    return {
      value: "account",
      defaultBgUrl: require("./images/login-logo-100.png"),
      formData: { ...INITIAL_DATA },
      rules: {
        account: [{ required: true, message: "用户名必填", type: "error" }],
        password: [{ required: true, message: "密码必填", type: "error" }],
        phone: [
          { required: true, message: "手机号必填", type: "error" },
          { pattern: /^1[********]\d{9}$/, message: "手机号格式错误", type: "error" }
        ],
        code: [
          { required: true, message: "验证码必填", type: "error" },
          { min: 4, message: '验证码长度不能少于4', type: 'warning', trigger: 'blur' },
          { max: 8, message: '验证码长度不能大于8', type: 'warning', trigger: 'blur' }
        ],
      },
    };
  },
  components: {
    DesktopIcon,
    LockOnIcon,
  },
  computed: {
    ...mapState({
      styleConfigData: state => state.Home.styleConfigData
    }),
    logoImg() {
      return this.styleConfigData.loginBgUrl || this.defaultBgUrl
    }
  },
  methods: {
    onReset() {
      this.$message.success("重置成功");
    },
    onSubmit({ validateResult, firstError }) {
      if (validateResult === true) {
        this.userLogin();
      } else {
        console.log("Errors: ", validateResult);
        this.$message.warning(firstError);
      }
    },
    getCode() {
      this.$message.info("需要获取验证码");
    },
    async userLogin() {
      const params = {
        mobile: this.formData.phone,
        password: encryptSm2(this.formData.password),
        crypto: "sm2",
      }
      sessionStorage.setItem('loginWay', 'account')
      this.$store.dispatch('userLogin', params).then(() => {
        let toPath = this.$route.query.redirect || "/home";
        this.$router.push(toPath);
      })
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.login-container {
  display: flex;
  .left {
    flex: 1;
    .image {
      width: 100%;
      height: 100%;
    }
  }
  .right {
    width: 33%;
    height: 100%;
    background-color: #fff;
    padding: 60px 80px;
    .title {
      font-size: 32px;
      margin-bottom: 32px;
    }
    .right-tabs {
      height: 80px;
      .tabs-name {
        font-size: 20px;
      }
    }
    .right-form {
      .red {
        color: @primary-color;
        cursor: pointer;
      }
      .login {
        height: 40px;
        // border-color: @primary-color;
        // color: @primary-color;
        // background-color: #fff;
      }
    }
    .bottom {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
