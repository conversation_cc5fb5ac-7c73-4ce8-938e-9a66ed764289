<template>
  <div class="profile-wrapper">
    <div class="password-info">
      <t-form :data="formData" :rules="rules" ref="form" @reset="onReset" @submit="onSubmit">
        <t-form-item label="原密码" name="oldPassword">
          <t-input type="password" v-model="formData.oldPassword" :maxlength="50" placeholder="请输入原密码"></t-input>
        </t-form-item>
        <t-form-item label="新密码" name="newPassword">
          <t-input type="password" v-model="formData.newPassword" :maxlength="50" placeholder="请输入新密码"></t-input>
        </t-form-item>
        <t-form-item label="确认新密码" name="newPassword2">
          <t-input type="password" v-model="formData.newPassword2" :maxlength="50" placeholder="请再次输入新密码"></t-input>
        </t-form-item>
        <t-form-item style="margin-left: 100px">
          <t-space size="10px">
            <t-button theme="primary" type="submit">提交</t-button>
            <t-button theme="default" variant="base" type="reset">重置</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </div>
  </div>
</template>

<script>
import { updatePassword } from '@/components/Header/api'
import { encryptSm2 } from '@/utils/util'

export default {
  name: 'Center-Password',
  data() {
    const validatePasswor2 = (value) => {
      return value && value === this.formData.newPassword
    };
    return {
      formData: {
        oldPassword: '',
        newPassword: '',
        newPassword2: ''
      },
      rules: {
        oldPassword: [
          { required: true, message: '请输入原密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { pattern: /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,20}$/, message: '密码要求长度8-20位，必须包含数字、字母及特殊字符', trigger: 'blur' }
        ],
        newPassword2: [
          { required: true, message: '请再次输入原密码', trigger: 'blur' },
          { validator: validatePasswor2, message: '两次密码输入不一致', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.User.userInfo
    }
  },
  methods: {
    onReset() {},
    async onSubmit({ validateResult, firstError }) {
      if (validateResult === true) {
        const params = {
          oldPassword: encryptSm2(this.formData.oldPassword),
          newPassword: encryptSm2(this.formData.newPassword)
        }
        let res = await updatePassword(this.formData)
        if(res.code === 0) {
          this.$message.success('修改成功');
          this.$refs.form.reset();
        }
      } else {
        console.log('Errors: ', validateResult);
        this.$message.warning(firstError);
      }
    },
  }
}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.profile-wrapper {
  width: 83.33%;
  .password-info {
    background-color: #fff;
    width: 500px;
    margin: 20px;

  }
}
</style>
