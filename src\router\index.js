import Vue from "vue";
import VueRouter from "vue-router";
import routes from "./router";
import store from "@/store";
import { getToken } from "@/base/cookie";
import { toLogin } from "@/utils/util";

Vue.use(VueRouter);

// 重写push|replace方法
//先把VueRouter的push和replace方法保存一份
let originPush = VueRouter.prototype.push;
let originReplace = VueRouter.prototype.replace;
VueRouter.prototype.push = function (location, resolve, reject) {
  // 此函数上下文(this指向)为VueRouter的一个实例
  if (resolve && reject) {
    //如果我们自己指定了成功/失败的回调，则自己传入
    originPush.call(this, location, resolve, reject);
    //若此时直接使用originPush()方法，则函数内的this指向window（内部代码将无法执行）。故应用call或apply方法修改this指向
  } else {
    //如果我们没有指定成功/失败的回调，则自动帮我们生成，防止报错
    originPush.call(
      this,
      location,
      () => {},
      () => {}
    );
  }
};

// replace方法同理
VueRouter.prototype.replace = function (location, resolve, reject) {
  if (resolve && reject) {
    originReplace.call(this, location, resolve, reject);
  } else {
    originReplace.call(
      this,
      location,
      () => {},
      () => {}
    );
  }
};

let router = new VueRouter({
  // mode: "history",
  routes: routes,
  scrollBehavior() {
    // 始终滚动到顶部
    return { y: 0 };
  },
});

async function dealBeforeEach(to, from, next) {
  let token = getToken();
  const userInfo = store.state.User.userInfo;

  const dealRouter = () => {
    if (to.meta.title) {
      let title = to.meta.title;
      let customTitle = store.state.Home.configData.title;
      if (customTitle) {
        title = title.replace("金采通", customTitle);
      }
      document.title = title;
    }
    if(!store.state.Home.inited) {
      return
    }
    if (token) {
      try {
        next();
      } catch (error) {
        next("login");
      }
    } else {
      // 未登录不能去交易页、支付页、个人中心页
      // 可以去home页、搜索页
      let cantPath = ["/shopcart", "/approve", "/trade", "pay", "/center"];
      cantPath.forEach((item) => {
        if (to.path.indexOf(item) != -1) {
          toLogin(2, to.fullPath);
        } else {
          next();
        }
      });
    }
  };

  if (token && (!userInfo || !userInfo.nickname)) {
    store.dispatch("getUserInfo");
    store.dispatch("loadCartCount");
  } 
  dealRouter();
}

router.beforeEach(async (to, from, next) => {
  if (to.query.req === "false") {
    next();
  }
  const isInited = store.state.Home.inited
  const isErrorPage = to.path === "/error-page"
  if (!isInited && !isErrorPage) {
    // 避免重复初始化：添加状态标记
    if (store.state.Home.initializing) {
      return // 已在初始化中，等待完成
    }
    try {
      store.commit("setInitializing", true) // 重置状态
      // 等待配置数据
      const res = await store.dispatch("getConfigData")
      if (!res) throw new Error("Config data fetch failed")
      store.dispatch("loadDefaultRegions")
      store.dispatch("getHomeConfigData")
      store.dispatch("getSupplierList")

      // 标记初始化完成
      store.commit("setInited", true)
      dealBeforeEach(to, from, next)
    } catch(error) {
      const errorCode = error?.data?.code || 500
      next({ path: "/error-page", query: { code: errorCode, req: false } }) // 重定向
    } finally {
      store.commit("setInitializing", false) // 重置状态
    }
  } else {
    dealBeforeEach(to, from, next)
  }
});

export default router;
