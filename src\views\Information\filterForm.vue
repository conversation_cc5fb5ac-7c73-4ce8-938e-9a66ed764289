<template>
  <div class="info-form">
    <div class="title">公告筛选</div>
    <t-form class="form" labelAlign="top" @reset="onReset" @submit="onSubmit">
      <t-form-item label="关键词" name="keyword">
        <t-input v-model="formData.keyword" placeholder="请输入内容" />
      </t-form-item>
      <t-form-item label="公告类型" name="noticeType">
        <t-checkbox-group v-model="formData.noticeType">
          <t-checkbox v-for="(item,index) in categoryList" :key="index" :value="item.categoryCode">{{item.categoryName}}</t-checkbox>
        </t-checkbox-group>
      </t-form-item>
      <t-form-item v-if="false" label="竞采方式" name="competitiveType">
        <t-checkbox-group v-model="formData.competitiveType">
          <t-checkbox value="1">竞价</t-checkbox>
          <t-checkbox value="2">询价</t-checkbox>
        </t-checkbox-group>
      </t-form-item>
      <t-form-item label="发布时间" name="date">
        <t-date-range-picker clearable v-model="formData.date"></t-date-range-picker>
      </t-form-item>
      <t-form-item style="text-align: right;">
        <t-space size="10px">
          <t-button theme="primary" type="submit">筛选</t-button>
          <t-button theme="default" variant="base" type="reset">重置</t-button>
        </t-space>
      </t-form-item>
    </t-form>
  </div>
</template>
    
<script>
export default {
  name: 'InformationFilterForm',
  props: {
    categoryList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      formData: {
        keyword: '',
        noticeType: [],
        competitiveType: [],
        date: []
      }
    };
  },
  methods: {
    onReset() {
      this.formData = {
        keyword: '',
        noticeType: [],
        competitiveType: [],
        date: []
      }
      this.$emit('submit', this.formData)
    },
    onSubmit() {
      this.$emit('submit', this.formData)
    }
  }
};
</script>
    
<style lang="less" scoped>
@import "@/style/variables.less";

.info-form {
  .title {
    height: 48px;
    line-height: 48px;
    padding: 0 20px;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    border-bottom: 1px solid #eee;
  }
  .form {
    padding: 16px;
  }
}
</style>
    