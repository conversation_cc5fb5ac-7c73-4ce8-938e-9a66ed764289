import { getDefaultAddress } from '@/views/Center/address/api'
import { DialogPlugin } from 'tdesign-vue'
import { getToken } from '@/base/cookie'
import router from "@/router"
import store from "@/store"

const state = {
  defaultRegions: []
}

const mutations = {
  setDefaultRegions(state, addressInfo) {
      state.defaultRegions = addressInfo || [17, 1381, 50718]
  },
}

const actions = {
  async loadDefaultRegions({ commit }) {
    const res = await getDefaultAddress()
    if(res.code === 0 && res.data) {
      if (res.data.provinceId) {
        let areaIds = [res.data.provinceId, res.data.cityId, res.data.countyId, res.data.townId]
        areaIds = areaIds.filter(id => id !== null && id !== undefined)
        commit('setDefaultRegions', areaIds)
      } else {
        commit('setDefaultRegions', null)
      }
      if (!res.data.id && store.state.User.userInfo) {
        const locationHash = window.location.hash
        const hitPath = ['/trade', '/trade-now'].find(item => locationHash.includes(item))
        if(!hitPath) {
          return
        }

        const mydialog = DialogPlugin.confirm({
          header: '提示',
          body: '还未选择默认地址，请前往工作台进行选择，否则无法进行正常购物',
          confirmBtn: '确定',
          cancelBtn: '暂不选择',
          onConfirm: () => {
            mydialog.hide();
            let isCenter = router.currentRoute.path.indexOf('/center') >= 0
            if(isCenter) {
              router.push('/center/address')
            } else {
              let ctxPath = process.env.VUE_APP_PATH_CONTEXT
              window.open(location.origin + ctxPath + '/#/center/address', '_blank')
            }
          },
          onClose: () => {
            mydialog.hide()
          },
        });
      }
    }
  }
}

const getters = {
  defaultRegions(state) {
    return state.defaultRegions || []
  }
}

export default {
  state, mutations, actions, getters
}