<template>
  <div class="order-main">
    <div class="container">
      <div class="order-body">
        <!--左侧列表-->
        <div class="order-left">
          <div class="header1">
            <div class="title">直采</div>
            <div @click="toCenterPage('myorder')" :class="{ checked: checked === 'myorder' }">我的订单</div>
            <div @click="toCenterPage('collect')" :class="{ checked: checked === 'collect' }">收藏的商品</div>
            <div @click="toCenterPage('wishlist')" :class="{ checked: checked === 'wishlist' }">我的心愿单</div>
            <!-- <div @click="toCenterPage('customerservice')" :class="{ checked: checked === 'customerservice' }">客服</div> -->
            <!-- <div @click="toGourpOrder">团购订单</div>
            <div>本地生活订单</div>
            <div>我的预售</div>
            <div>评价晒单</div>
            <div>取消订单记录</div> -->
          </div>
          <div class="header1">
            <div class="title">我的信息</div>
            <div @click="toCenterPage('profile')" :class="{ checked: checked === 'profile' }">个人信息</div>
            <div @click="toCenterPage('address')" :class="{ checked: checked === 'address' }">收货地址</div>
            <div v-if="showPassModify" @click="toCenterPage('password')" :class="{ checked: checked === 'password' }">修改密码</div>
            <div @click="toCenterPage('points')" :class="{ checked: checked === 'points' }">积分管理</div>
          </div>
        </div>
        <!-- 右侧内容 -->
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  methods: {
    toCenterPage(page) {
      this.$router.push(`/center/${page}`)
    }
  },
  computed: {
    checked() {
      return this.$route.path.slice(8)
    },
    showPassModify() {
      const configData = this.$store.state.Home.configData || {}
      return configData.loginType === 1
    }
  }
}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.order-main {
  .container {
    margin: 0 auto;
    width: 1200px;

    .order-body {
      padding: 12px 12px 12px 0px;
      color: #333;
      display: flex;

      &:after {
        content: "";
        display: block;
        clear: both;
      }

      //左边
      .order-left {
        width: calc(16.67% - 20px);
        background-color: #f5f5f5;
        margin-right: 20px;
        padding: 0 20px 20px;
        min-height: 500px;

        .header1 {
          font-size: 14px;
          line-height: 22px;
          cursor: pointer;

          .title {
            font-weight: 600;
            font-size: 16px;
            margin-top: 16px;
          }

          .checked {
            color: @primary-color;
          }
        }
      }
    }
  }
}</style>