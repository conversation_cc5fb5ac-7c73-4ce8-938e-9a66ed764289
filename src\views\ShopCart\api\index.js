import request from '@/base/service'

const requestC = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_TRADE_API
    }
  })
}

// 更新购物车商品是否选中
export function updateSelected(data) {
  return requestC({
    url: '/cart/update-selected',
    method: 'post',
    data
  })
}

// 更新购物车商品数量
export function updateCount(data) {
  return requestC({
    url: '/cart/update-count',
    method: 'post',
    data
  })
}

// 删除购物车商品
export function deleteCart(params) {
  return requestC({
    url: '/cart/delete',
    method: 'post',
    params
  })
}

// 福利和扶贫商品检查
export function checkMix() {
  return requestC({
    url: '/cart/check-mix',
    method: 'get',
  })
}

// 订单检查页
export function checkOrder(data) {
  return requestC({
    url: '/cart/check-order',
    method: 'post',
    data
  })
}

// 添加商品到购物车
export function addCount(data) {
  return requestC({
    url: '/cart/add-count',
    method: 'post',
    data
  })
}

// 查询用户的购物车的详情
export function getDetail(params) {
  return requestC({
    url: '/cart/get-detail',
    method: 'get',
    params
  })
}

// 查询用户在购物车中的商品数量
export function getCount(params) {
  return requestC({
    url: '/cart/get-count',
    method: 'get',
    params
  })
}
