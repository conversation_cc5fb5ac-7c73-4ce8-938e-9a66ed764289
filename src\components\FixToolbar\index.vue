<template>
  <div class="fix-toolbar flex-column" v-if="showToolbar">
    <div class="flex-column item-con">
      <div class="item bottom-line item-vertical " v-if="isLogin">
        <div>
          <MessageCenter toolbar></MessageCenter>
        </div>
        <div>消息</div>
      </div>
      <div class="item bottom-line item-vertical">
        <div>
          <t-badge :count="cartCount">
          <t-icon name="cart" size="25px" style="color: var(--td-brand-color-7)" @click="toCart"/>
        </t-badge>
        </div>
        <div>购物车</div>
      </div>
      <div class="item bottom-line item-vertical" @click="toMine">
        <t-icon name="user" size="25px"/>
        <div>我的</div>
      </div>
      <div v-if="h5Switch" class="bottom-line">
        <t-popup placement="left" overlayClassName="cust-app-popup" trigger="click" @visible-change="handlePopupVisible">
          <div class="item item-vertical m0">
            <t-icon name="mobile" size="25px" style="color: var(--td-brand-color-7)"/>
            <span>移动端</span>
          </div>
          <template #content>
            <div class="flex">   
              <div style="width: 100px">
                <canvas ref="qrcodeCanvas">...</canvas>
              </div>    
              <div class="flex-column">           
                <p>手机扫码</p>        
                <p class="tip">开启便捷采购之旅</p>
              </div>
            </div>
          </template>
        </t-popup>
      </div>
      <div>
        <t-popup placement="left" overlayClassName="cust-service-popup" trigger="click">
          <div class="item item-vertical m0">
            <t-icon name="earphone" size="25px" style="color: var(--td-brand-color-7)"/>
            <span>客服</span>
          </div>
          <template #content>
            <div class="flex-column-start">  
              <div>           
                <t-link theme="primary" @click="toCustomerService">点击查看供应商客服</t-link>
              </div>    
              <div class="flex-column-start p-service">
                <p>平台运营客服：</p>  
                <div> 
                  <img :src="extCustQRImg || customerQR" width="100" title="点击跳转客服" @click="toCustLink">
                </div>
                <div v-if="extCustLink">           
                  <t-link theme="primary" @click="toCustLink">点击跳转平台客服</t-link>
                </div>
                <template v-if="jctServiceList && jctServiceList.length">
                  <p>平台运营电话：</p>  
                  <div v-for="(item,index) in jctServiceList" :key="index"> 
                    <span class="name">{{ item.name }}</span>
                    <span class="phone">{{ item.phone }}</span>
                  </div>
                </template>          
              </div>
            </div>
          </template>
        </t-popup>
      </div>
    </div>
    <div id="back-to-top" class="flex-column display-none" @click="toTop"> 
      <ArrowUpIcon></ArrowUpIcon>
      <div>回顶部</div>
    </div>  
  </div>
</template>

<script>
import QRCode from 'qrcode'
import { ArrowUpIcon } from 'tdesign-icons-vue'
import MessageCenter from '@/components/Message'
import { mapState } from 'vuex'
export default {
  name: 'FixToolbar',
  components: { ArrowUpIcon, MessageCenter },
  data() {
    return {
      qrStatus: 0,
      customerQR: require("@/assets/qr-cust.jpg"),
      showToolbar: false
    }
  }, 
  computed: {
    ...mapState({
      extConfig: state => state.Home.extConfig,
      homeConfigData: state => state.Home.homeConfigData,
      styleConfigData: state => state.Home.styleConfigData,
      cartCount: state => state.User.cartCount,
      userInfo: state => state.User.userInfo,
    }),
    isLogin() {
      return this.userInfo && this.userInfo.nickname
    },
    faviconUrl() {
      return this.styleConfigData.faviconUrl || ''
    },
    h5Switch() {
      return this.homeConfigData.h5Switch
    },
    extParamService() {
      // 平台客服定义，如无定义则不显示平台客服，k=v&k2=v2
      return this.extConfig.custPhone
    },
    extCustQRImg() {
      // 平台客服二维码
      return this.extConfig.custQRImg
    },
    extCustLink() {
      // 平台客服链接
      return this.extConfig.custLink
    },
    isJctService() {
      return this.extParamService && this.extParamService.length > 5
    },
    jctServiceList() {
      let str = this.extParamService
      if(!str) {
        return []
      }

      let arr = []
      let segs = str.split('&')
      segs.forEach(seg => {
        let subSegs = seg.split('=')
        arr.push({
          name: subSegs[0],
          phone: subSegs[1]
        })
      })
      console.log('jctservice-', arr)

      return arr
    }
  },
  watch: {
    $route() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    needShow() {
      let path = window.location.hash
      let allowPaths = ['/home', '/supplier', '/search', '/storeSearch', '/detail', '/center']
      let hit = allowPaths.find(item => path.indexOf(item) >= 0)
      return !!hit
    },
    init() {
      this.showToolbar = this.needShow()
      if(!this.showToolbar) {
        return
      }
      const backToTop = document.querySelector("#back-to-top");
      window.addEventListener("scroll", function() {
        if(!backToTop) {
          return
        }
        if (window.scrollY > 200) { // 当页面向下滚动200像素时
          backToTop.className = "flex-column fade-in-animation"; // 显示“返回顶部”按钮
        } else {
          backToTop.className = 'flex-column fade-out-animation'
        }
      })
    },
    handlePopupVisible() {
      if(!this.h5Switch || this.qrStatus) {
        return
      }
      setTimeout(() => {
        this.generateAppQRCode()
      }, 100)
    },
    generateAppQRCode() {
      this.$nextTick(() => {
        let ctxPath = process.env.VUE_APP_PATH_CONTEXT
        let h5Url = location.origin + ctxPath + '/h5' 
        const canvas = this.$refs.qrcodeCanvas
        if(canvas) {
          this.qrStatus = 1
          QRCode.toCanvas(canvas, h5Url, { width: 100, errorCorrectionLevel: 'L' }, function (error) {
            if (error) {
              console.error(error)
            }
          })
        }
      })
    },
    toCustomerService() {
      this.$router.push('/customerservice')
    },
    toCart() {
      this.$router.push('/shopcart')
    },
    toMine() {
      this.$router.push('/center/myorder')
    },
    toCustLink() {
      if(!this.extCustLink) {
        return
      }
      window.open(this.extCustLink, '_blank')
    },
    toTop() {
      window.scrollTo({
        top: 0, // 滚动到距离顶部 1000 像素的位置
        left: 0,   // 不改变水平滚动位置
        behavior: 'smooth' // 使用平滑滚动
      })
    }
  }
}
</script>

<style lang="less">
@import "@/style/variables.less";

.fix-toolbar {
  position: fixed;
  width: 60px;
  line-height: 1.6;
  z-index: 300;
  bottom: 220px;
  right: 0;
  font-size: 12px;
  color: var(--td-brand-color-7);
  justify-content: center;
  box-shadow: -2px 0 9px 0 rgba(232, 232, 232, 0.3);
  .m0 {
    margin: 0 !important;
  }
  .item-con {
    width: 100%;
    background-color: white;
  }
  .bottom-line {
    border-bottom: 1px solid #eee;
  }
  .item {
    width: 100%;
    padding: 5px;
    margin: 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      display: inline-block;
    }
    &:hover {
      color: @primary-color;
    }
  }
  .item-vertical {
    flex-direction: column;
    cursor: pointer;
  }
  #back-to-top {
    width: 100%;
    background: white;
    padding: 10px 5px;
    cursor: pointer;
    margin-top: 10px;
    border: 0;
    box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.15);
    &:hover {
      box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.25);
      background-color: @primary-color;
      color: #fff;
    }
  }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; display: none;}
}
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; display: flex;}
}

.fade-out-animation {
  animation: fadeOut 1s forwards; /* 动画持续2秒，结束后停留在最后一帧 */
}
.fade-in-animation {
  animation: fadeIn 1s forwards; /* 动画持续2秒，结束后停留在最后一帧 */
}

.display-none {
  display: none;
}

.cust-app-popup {
  width: 250px;
  height: 114px;
  font-size: 13px;
  margin-right: 15px !important;
  canvas {
    width: 100px;
    height: 100px;
  }
  div {
    margin: 0;
  }
  img {
    width: 100px;
    height: 100px;
  }
  .tip {
    color: #999;
    font-size: 12px;
  }
}
.text-link {
  cursor: pointer;
  font-weight: 500px;
  text-decoration: underline;
}
.cust-service-popup {
  padding: 5px;
  height: 114px;
  font-size: 13px;
  margin-right: 15px !important;
  .t-popup__content {
    padding: 15px;
  }
  .p-service {
    margin-top: 10px;
  }
  span {
    display: inline-block;
  }
  .phone {
    color: #999;
    margin-left: 10px;
  }
}
</style>