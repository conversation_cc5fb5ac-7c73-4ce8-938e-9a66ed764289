<template>
  <div class="tradesuccess">
    <div class="success">
      <h3>
        <img src="./images/right.png" width="48" height="48">
        <!-- <span v-if="needPurchase">
        订单提交成功，请点击提交采购申请按钮发起采购流程，采购流程通过后订单才会发货，请点击下方按钮去提交
        </span> -->
      </h3>
      <h5 style="text-align: center;font-size: 1em;">
        您可以在<span class="tip-text">我的订单</span>中查看订单信息
      </h5>
      <div class="tradeDetail">
        <p class="button" style="text-align: center;">
          <t-button theme="primary" class="mr12" variant="base" @click="toOrder">查看订单</t-button>
          <!-- <t-button theme="primary" class="mr12" variant="base" @click="submitPurchase" v-if="needPurchase">提交采购申请</t-button> -->
          <t-button theme="primary" variant="outline" @click="toShop">继续购买</t-button>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'tradesuccess',
  computed: {
    ...mapState({
      configData: state => state.Home.configData
    }),
    orderNo() {
      return this.$route.query.no
    },
    oType() {
      return this.$route.query.oType
    },
    needPurchase() {
      return this.configData.projectSwitch || this.configData.approveSwitch
    }
  },
  mounted() {
    // if(this.needPurchase) {
    //   if(this.configData.projectSwitch) {
    //     this.$message.success('请填写采购申请并绑定项目经费卡')
    //   } else  if(this.configData.projectSwitch) {
    //     this.$message.success('请填写采购申请')
    //   }
    //   this.submitPurchase()
    // }
  },
  methods: {
    toOrder() {
      // 加了商户，后台不能返回订单id，只能跳订单列表页
      if(this.oType !== '1' && this.orderNo) {
        this.$router.push('/orderDetail?no=' + this.orderNo)
        return
      }
      this.$router.push('/center/myorder')
    },
    submitPurchase() {
      this.$router.push('/approve')
    },
    toShop() {
      this.$router.push('/shopCart')
    }
  }
}
</script>

<style lang="less" scoped>
.tradesuccess {
  margin: 20px auto;
  padding: 25px;
  width: 1200px;
  min-height: 500px !important;

  .tip-text {
    color: #e12228;
  }

  .success {
    width: 500px;
    margin: 0 auto;

    .mr12 {
      margin-right: 12px;
    }

    h3 {
      margin: 20px 0;
      font-weight: 700;
      font-size: 20px;
      line-height: 30px;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;

      img {
        max-width: 100%;
        vertical-align: middle;
        border: 0;
        margin-right: 14px;
        margin-bottom: 4px;
      }
    }

    .tradeDetail {
      font-size: 15px;
      p {
        width: 100%;
      }

      .button {
        margin: 30px 0;
        line-height: 26px;
      }
    }
  }
}
</style>