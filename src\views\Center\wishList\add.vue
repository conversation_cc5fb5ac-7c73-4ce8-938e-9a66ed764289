<template>
  <t-dialog attach="body" top="10vh" :visible.sync="visible" width="700px" :header="dialogTitle" :closeBtn="!this.closeNotShow" :closeOnEscKeydown="!closeNotShow" :onCancel="close" :closeOnOverlayClick="false"
    :onClose="close" :footer="false" :destroyOnClose="true">
    <t-form :data="formData" :rules="rules" ref="form" @submit="onSubmit">
      <t-form-item label="品目" name="categoryIds">
        <t-cascader v-model="formData.categoryIds" :checkStrictly="checkStrictly" value-type="full" :load="load" @change="handleChange"
          :options="wishOptions" :input-props="inputProps" clearable />
      </t-form-item>
      <t-form-item label="品牌" name="brand">
        <t-input v-model.trim="formData.brand" placeholder="请输入品牌" :maxlength="100"></t-input>
      </t-form-item>
      <t-form-item label="型号" name="model">
        <t-input v-model.trim="formData.model" placeholder="请输入型号" :maxlength="100"></t-input>
      </t-form-item>
      <t-form-item label="数量" name="quantity">
        <t-input-number v-model.trim="formData.quantity" :min="1" placeholder="请输入数量" style="width: 250px"></t-input-number>
      </t-form-item>
      <t-form-item label="下单时间" name="orderTime">
        <t-date-picker v-model="formData.orderTime" placeholder="请选择下单时间" clearable :disable-date="disabledDate" />
      </t-form-item>
      <t-form-item label="产品链接" name="productLink">
        <t-input v-model.trim="formData.productLink" :maxlength="200" placeholder="请输入产品链接"></t-input>
      </t-form-item>
      <t-form-item label="需求描述" name="productMemo">
        <t-textarea v-model.trim="formData.productMemo" placeholder="请输入需求描述" :autosize="{ minRows: 4, maxRows: 6 }" :maxlength="300" />
      </t-form-item>
      <t-form-item label="联系人" name="contact">
        <t-input v-model.trim="formData.contact" placeholder="请输入联系人" :maxlength="30"></t-input>
      </t-form-item>
      <t-form-item label="联系电话" name="phone">
        <t-input v-model.trim="formData.phone" placeholder="请输入联系电话" :maxlength="30"></t-input>
      </t-form-item>

      <t-form-item style="margin-left: 100px">
        <t-space size="10px">
          <t-button theme="primary" type="submit">保存收货心愿单</t-button>
        </t-space>
      </t-form-item>
    </t-form>
  </t-dialog>
</template>
  
<script>
import { wishCreate, wishUpdate } from '@/views/Center/wishList/api'
import { getRootCategoryList, getChildCategoryTreeList } from '@/views/Home/api'
import dayjs from 'dayjs';

// categoryIds "12,1445"
// categoryNames "手机通讯,手机"
const INITIAL_DATA = {
  categoryIds: [],
  categoryNames: '',
  brand: '',
  model: '',
  quantity: 1,
  orderTime: '',
  productLink: '',
  productMemo: '',
  contact: '',
  phone: ''
};

function urlValid(val) {
  if (!val) {
    return { result: true };
  }
  const reg = /(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-.,@?^=%&:/~+#]*[\w\-@?^=%&/~+#])?/
  if (!reg.test(val)) { 
    return { result: false, message: '请输入正确的网址', type: 'error' };
  }
  return { result: true };
}

function quantityValid(val) {
  if (val < 1) {
    return { result: false, message: '请输入正整数', type: 'error' };
  }
  return { result: true };
}

export default {
  name: 'add-wish-list',
  data() {
    return {
      visible: true,
      formData: { ...INITIAL_DATA },
      rules: {
        categoryIds: [
          { required: false, type: 'error', trigger: 'blur' }
        ],
        brand: [
          { required: false, type: 'error', trigger: 'blur' }
        ],
        quantity: [
          { required: false, type: 'error', trigger: 'blur' },
          { validator: quantityValid, trigger: 'blur' }
        ],
        phone: [
          { required: true, type: 'error', trigger: 'blur' }
        ],
        contact: [
          { required: true, type: 'error', trigger: 'blur' },
        ],
        productLink: [
          { validator: urlValid, trigger: 'blur' }
        ]
      },
      categoryList1: [],
      categoryList2: [],
      wishOptions: [],
      inputProps: {
        value: '',
      },
      disabledDate: {
        before: dayjs().subtract(1, 'day').format()
      },
      checkStrictly: false
    }
  },
  props: {
    editData: {
      type: Object,
      default: () => {}
    },
    closeNotShow: {
      type: Boolean,
      default: false
    }
  },
  created() {
    if (this.editData.id) {
      this.formData = {
        ...this.editData
      }
      if (!this.formData.orderTime) {
        this.formData.orderTime = ''
      }
      if(this.editData.categoryIds) {
        this.formData.categoryIds = this.editData.categoryIds.split(',')
      }
      if(this.editData.categoryNames) {
        this.inputProps.value = this.editData.categoryNames.replace(/,/g, ' / ')
      }
    } else {
      // 新建直接为true
      this.checkStrictly = true
      this.formData.contact = this.$store.state.User.userInfo.nickname
    }
    this.getRootCategoryList()
  },
  computed: {
    dialogTitle() {
      return `${this.editData.id ? '修改' : '添加'}心愿单`
    }
  },
  methods: {
    async load(node) {
      // 如果checkStrictly开始就是true，在点击级联时，categoryIds有两层或三层时会清掉变成一层
      this.$nextTick(() => {
        this.checkStrictly = true
      })
      if (node.level === 0) {
        const params = { parentCategoryId: node.value }
        const res = await getChildCategoryTreeList(params)
        if (res.code == 0) {
          this.categoryList2 = res.data || []
          return this.categoryList2.map(x => {
            return {
              label: x.categoryName,
              value: String(x.categoryId),
              children: node.level < 2
            }
          })
        }
      }
      if (node.level === 1) {
        const temp = this.categoryList2.find(x => x.categoryId == node.value) || {}
        return (temp ? temp.childCategoryList || [] : []).map(x => {
          return {
            label: x.categoryName,
            value: String(x.categoryId),
            children: node.level < 1
          }
        })
      }
      return []
    },
    handleChange(value, context) {
      const { node } = context;
      if (node) {
        const path = node.getPath();
        const labelPath = path.map((item) => item.label).join(' / ');
        this.inputProps.value = labelPath;
        this.$refs.form.clearValidate(['categoryIds'])
      } else {
        this.inputProps.value = ''
      }
    },
    // 查询大类
    async getRootCategoryList() {
      if (this.categoryList1.length > 0) {
        return
      }
      const res = await getRootCategoryList()
      if (res.code == 0) {
        this.categoryList1 = res.data || []
        this.wishOptions = (res.data || []).map(x => {
          return {
            label: x.categoryName,
            value: String(x.categoryId),
            children: true
          }
        })
      }
    },
    onSubmit({ validateResult, firstError }) {
      if (validateResult === true) {
        if (this.editData.id) {
          this.wishUpdate()
        } else {
          this.wishCreate()
        }
      } 
    },
    close() {
      this.formData = { ...INITIAL_DATA }
      this.$emit('closeDialog')
    },
    async wishCreate() {
      const params = { ...this.formData }
      if(params.categoryIds && params.categoryIds.length) {
        params.categoryIds = this.formData.categoryIds.join(',')
        params.categoryNames = this.inputProps.value.replace(/\s\/\s/g, ',')
      } else {
        params.categoryIds = ''
        params.categoryNames = ''
      }

      const res = await wishCreate(params)
      if (res.code === 0) {
        this.$message.success('添加心愿单成功')
        this.$emit('successOpt')
      } else {
        // this.$message.error('添加心愿单失败')
      }
    },
    async wishUpdate() {
      const params = { ...this.formData }
      if(params.categoryIds && params.categoryIds.length) {
        params.categoryIds = this.formData.categoryIds.join(',')
        params.categoryNames = this.inputProps.value.replace(/\s\/\s/g, ',')
      } else {
        params.categoryIds = ''
        params.categoryNames = ''
      }
      const res = await wishUpdate(params)
      if (res.code === 0) {
        this.$message.success('修改心愿单成功')
        this.$emit('successOpt')
      } else {
        // this.$message.error('修改心愿单失败')
      }
    }
  }
}
</script>
  
<style lang="less" scoped>
.alias-address {
  margin: 12px 0 0;

  .t-button {
    margin-right: 6px;
  }
}
.address-label {
  /deep/label::before {
    display: inline-block;
    margin-right: var(--td-comp-margin-xs);
    color: var(--td-error-color);
    line-height: var(--td-line-height-body-medium);
    content: "*";
  }
}
</style>
  