<template>
  <div class="after-sale-form">
    <t-form :data="formData" :rules="rules" ref="form"  @submit="onSubmit" labelWidth="130px">
      <t-form-item label="售后商品" name="skuName">
        <span>{{ orderItemData.skuName }}</span>
      </t-form-item>
      <t-form-item label="售后方式" name="way">
        <t-radio-group v-model="formData.way">
          <t-radio :value="10">仅退款</t-radio>
          <t-radio :value="20">退货退款</t-radio>
        </t-radio-group>
      </t-form-item>
      <t-form-item label="联系人" name="contact">
        <t-input v-model="formData.contact" :maxlength="50"></t-input>
      </t-form-item>
      <t-form-item label="电话" name="phone">
        <t-input v-model="formData.phone" :maxlength="50"></t-input>
      </t-form-item>
      <t-form-item label="售后原因" name="applyReason">
        <t-select v-model="formData.applyReason">
          <t-option v-for="(item,index) in reasonList" :key="index" :label="item.label" :value="item.value" />
        </t-select>
      </t-form-item>
      <t-form-item label="售后商品数量" name="count">
        <t-input-number :min="1" :max="maxRefundCount" v-model="formData.count" :allow-input-over-limit="false"></t-input-number>
      </t-form-item>
      <t-form-item label="退款金额" name="refundPrice">
        <t-input :value="comRefundPrice" label="￥" disabled></t-input>
      </t-form-item>
      <t-form-item label="退款说明" name="applyDescription">
        <t-textarea
          v-model="formData.applyDescription"
          placeholder="退款说明"
          name="applyDescription"
          :autosize="{ minRows: 3, maxRows: 5 }"
          :maxlength="300"
        />
      </t-form-item>
      <t-form-item label="图片上传">
        <t-upload 
          ref="uploadRef"
          :action="uploadFileUrl"
          v-model="files"
          :sizeLimit="{ size: 5, unit: 'MB', message: '图片大小不超过5MB' }"
          :autoUpload="true"
          :showImageFileName="true"
          @fail="handleFail"
          theme="image"
          tips='图片大小5M以内，最多只能上传 5 张图片'
          :headers="headers"
          :format-response="formatResponse"
          :before-upload="beforeUpload"
          multiple
          :max="5"
          accept="image/*"/>
      </t-form-item>
      <t-form-item style="margin-left: 200px">
        <t-space size="10px">
          <t-button class="footer-btn" theme="default" @click="cancel">取消</t-button>
          <t-button class="footer-btn" theme="primary" type="submit">提交</t-button>
        </t-space>
      </t-form-item>
    </t-form>
  </div>
</template>

<script>
import { createAfterSale } from '@/views/Center/afterSale/api'
import { getToken } from '@/base/cookie'
import { AFTER_SALE_REASON_LIST } from '@/utils/orderUtil'

export default {
  name: 'AfterSaleForm',
  props: {
    orderItemData: {
      required: true,
      default: () => {}
    },
    way: {
      default: () => 10
    }
  },
  data() {
    return {
      formData: {
        way: 10,
        count: 1,
        refundPrice: 0,
        applyReason: '',
        contact: '',
        phone: '',
        applyDescription: '',
        applyPicUrls: null
      },
      files: [],
      rules: {
        way: [
          { required: true,  message: '售后方式不能为空',type: 'error' }
        ],
        count: [
          { required: true,  message: '售后商品数量不能为空',type: 'error' },
          { min: 1,  message: '售后商品数量不能小于1',type: 'error' }
        ],
        contact: [
          { required: true,  message: '联系人不能为空',type: 'error' }
        ],
        phone: [
          { required: true,  message: '电话不能为空',type: 'error' }
        ],
        applyReason: [
          { required: true,  message: '售后原因不能为空',type: 'error' }
        ],
        applyDescription: [
          { required: true,  message: '售后说明不能为空',type: 'error' }
        ]
      },
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/infra/file/upload", // 请求地址
      headers: { Authorization: "Bearer " + getToken() }
    }
  },
  computed: {
    reasonList() {
      return AFTER_SALE_REASON_LIST.filter(item => item.way.includes(this.formData.way))
    },
    maxRefundCount() {
      if(this.orderItemData.count && this.orderItemData.afterSaleCount) {
        let c1 = this.orderItemData.count - this.orderItemData.afterSaleCount
        return c1 > 0 ? c1 : 1
      }
    
      return this.orderItemData.count || 0
    },
    comRefundPrice() {
      if(this.orderItemData.skuPrice) {
        return (this.orderItemData.skuPrice * this.formData.count).toFixed(2)
      }
      return 0
    }
  },
  watch: {
    'formData.way': {
      handler(val) {
        this.$emit('update:way', val);
      }
    }
  },
  mounted() {
    this.formData.way = this.way
  },
  methods: {
    onSubmit({ validateResult, firstError }) {
      if (validateResult === true) {
        this.doSubmit()
      } 
    },
    // 提示上传文件失败
    beforeUpload(file) {
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$message.error(`文件 ${file.name} 大小超过5MB，上传失败`);
      }
      return isLt5M;
    },
    copy2Form(afterSaleInfo) {
      Object.keys(this.formData).forEach(key => {
        this.formData[key] = afterSaleInfo[key]
      })
      if(afterSaleInfo.applyPicUrls) {
        this.files = afterSaleInfo.applyPicUrls.map(item => {
          return {
            url: item
          }
        })
      }
    },
    async doSubmit() {
      const params = Object.assign({}, this.formData)
      params.refundPrice = this.comRefundPrice
      params.orderItemId = this.orderItemData.id
      this.checkFiles()
      if(this.files.length > 0) {
        params.applyPicUrls = this.files.map(item => item.url)
      }

      const res = await createAfterSale(params)
      if (res.code === 0) {
        this.$message.success('提交成功')
        this.$emit("on-submit")
      }
    },
    handleFail({ file }) {
      this.$message.error(`文件 ${file.name} 上传失败`)
      this.checkFiles()
    },
    checkFiles() {
      let arr = []
      if(this.files) {
        arr = this.files.filter(item => !!item.url)
      }
      this.files = arr
    },
    formatResponse(res) {
      return { url: res.data };
    },
    cancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

</style>