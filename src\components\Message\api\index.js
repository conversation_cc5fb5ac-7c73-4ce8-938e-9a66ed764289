import request from '@/base/service'

const requestSystem = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_SYSTEM_API
    }
  })
}

// 获得单条消息
export function getMessage(params) {
  return requestSystem({
    url: '/notify-message/get',
    method: 'get',
    params
  })
}

// 获得消息分页
export function getMessagePage(params) {
  return requestSystem({
    url: '/notify-message/my-page',
    method: 'get',
    params
  })
}

// 获得未读消息总数
export function getUnreadCount(params) {
  return requestSystem({
    url: '/notify-message/get-unread-count',
    method: 'get',
    params
  })
}

// 获得未读消息列表
export function getUnreadList(params) {
  return requrequestSystemest({
    url: '/notify-message/get-unread-list',
    method: 'get',
    params
  })
}

// 更新消息状态为已读
export function updateReadStatus(data) {
  return requestSystem({
    url: '/notify-message/update-read',
    method: 'put',
    params: data
  })
}

// 更新全部消息状态为已读
export function updateAllReadStatus(data) {
  return requestSystem({
    url: '/notify-message/update-all-read',
    method: 'put',
    data
  })
}
