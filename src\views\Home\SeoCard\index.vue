<template>
  <div class="seo-card-container">
    <div class="seo-card" v-for="item in cardList" :key="item.id">
      <div class="seo-image" v-if="item.type === 10">
        <div :class="getStyleLayout(item.layout)">
          <div class="card-title">
            <img v-if="item.icon" :src="item.icon" class="title-icon" />
            <span>{{item.title}}</span>
          </div>
          <div class="card-content" @click="hanleImageClick(item)"> 
            <t-image :src="item.imageUrl" fit="cover" :style="{ width: '100%',cursor: 'pointer' }" />
          </div>
        </div>
      </div>
      <div class="seo-sku" v-if="item.type === 20">
        <div>
          <div :class="`title-between ${item.icon?'':'title-bg-gray'}`">
            <div class="card-title">
              <img v-if="item.icon" :src="item.icon" class="title-icon" />
              <span>{{ item.title }}</span>
            </div>
            <span class="more" @click="handleSkuMore(item)">查看更多 ></span>
          </div>
          <div class="card-content" v-if="item.imageUrl">
            <div class="section-left">
              <t-image :src="item.imageUrl" fit="cover" :style="{ width: '255px', height: '580px' }" />
            </div>
            <div class="section-right">
              <CardSku :tagIds="getTagIds(item)" :count="8"></CardSku>
            </div>
          </div>
          <div class="card-content" v-else>
            <CardSku :tagIds="getTagIds(item)" :count="10"></CardSku>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getSeoCardList } from '@/views/Home/api'
import CardSku from './components/card-sku'
import { mapState } from 'vuex'
export default {
  name: 'ProductSeoCard',
  components: { CardSku },
  data() {
    return {
      cardList: []
    }
  },
  computed: {
    ...mapState({
      homeConfig: state => state.Home.homeConfigData,
      configData: state => state.Home.configData
    }),
    cardIdList() {
      if(this.homeConfig && this.homeConfig.seoCardList) {
        return (this.homeConfig.seoCardList || []).filter(item => item.title).map(item => item.id)
      }
      return []
    }
  },
  created() {
    this.loadCardList()
  },
  methods: {
    readCache() {
      let key = `jcy-home-seocard-${this.configData.tenantId}`
      let cacheVal = window.localStorage.getItem(key)
      if(cacheVal) {
        try {
          this.cardList = JSON.parse(cacheVal)
        } catch (e) {}
      }
    },
    setCache(obj) {
      let key = `jcy-home-seocard-${this.configData.tenantId}`
      window.localStorage.setItem(key, JSON.stringify(obj))
    },
    loadCardList() {
      this.readCache()
      this.doLoadCardList()
    },
    async doLoadCardList() {
      let params = {
        ids: this.cardIdList.join(',')
      }
      let res = await getSeoCardList(params)
      this.cardList = res.data || []
      this.setCache(this.cardList)
    },
    getStyleLayout(layoutVal) {
      if(layoutVal === 10) {
        return 'layout-1column'
      } else if(layoutVal === 20) {
        return 'layout-2column'
      }
      return 'layout-1'
    },
    getCardContentObj(card) {
      if(card && card.content) {
        return JSON.parse(card.content)
      }
      return null
    },
    getTagIds(card) {
      if(card.type === 20) {
        let obj = this.getCardContentObj(card)
        console.log('seo-card: ', obj)
        if(obj && obj.tags) {
          return obj.tags.map(tag => tag.id)
        }
      }
      return null
    },
    hanleImageClick(card) {
      let obj = this.getCardContentObj(card)
      console.log('seo-card: ', obj)
      if(obj && obj.linkUrl) {
        window.open(obj.linkUrl, '_blank')
      }
    },
    handleSkuMore(card) {
      let tagIds = this.getTagIds(card)
      if(!tagIds) {
        return
      }
      let routePath = `/search?tagIds=${tagIds.join(',')}`
      let prefixPath = location.origin + process.env.VUE_APP_PATH_CONTEXT
      window.open(`${prefixPath}/#${routePath}`, '_blank')
      // this.$router.push(routePath);
    }
  }

}
</script>

<style lang="less">
.seo-card-container {
  width: 1200px;
  margin: 20px auto 10px;
  .seo-card {
    width: 100%;
    margin: 20px auto;
    padding: 0;
    background-color: #fff;
    .card-content {
      background-color: #fff;
    }
    .title-between {
      height: 45px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .title-bg-gray {
      background-color: #f5f6f8;
    }
    .card-title {
      padding: 10px 0;
      font-size: 20px;
      font-weight: 700;
      color: #333;
      display: flex;
      align-items: center;
    }
    .title-icon {
      width: 25px;
      height: 25px;
      margin-left: 5px;
    }
  }
  .seo-image {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .layout-1column {
      width: 100%;
    }
    .layout-2column {
      width: 49%;
    }
    .card-content {
      width: 100%;
      padding: 5px;
    }
  }
  .seo-sku {
    height: 625px;
    .more {
      color: #999;
      font-size: 14px;
      cursor: pointer;
      padding-right: 5px;
    }
    .card-content {
      margin-top: 0;
      background-color: #f5f6f8;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
    .section-right {
      width:945px;
      background-color: #fff;
    }
  }
}
</style>