import request from "@/base/service";

const requestT = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_TRADE_API,
    },
  });
};


// 提交售后单
export function createAfterSale(data) {
  return requestT({
    url: "/after-sale/create",
    method: "post",
    data,
  });
}

// 提交物流信息
export function saveDelivery(data) {
  return requestT({
    url: "/after-sale/delivery",
    method: "post",
    data,
  });
}

// 查询售后单
export function getAfterSaleDetail(params) {
  return requestT({
    url: "/after-sale/detail",
    method: "get",
    params,
  });
}

// 查询售后订单项
export function getOrderItemDetail(params) {
  return requestT({
    url: "/after-sale/order-item/detail",
    method: "get",
    params,
  });
}

// 取消售后
export function cancelAfterSale(params) {
  return requestT({
    url: "/after-sale/cancel",
    method: "delete",
    params,
  });
}
