<template>
  <t-watermark
  :watermark-content="{
      text: title,
    }"
    :width="120"
    :height="60"
    :y="120"
    :x="80"
  >
  <div class="error-wrapper">
    <t-loading size="40px" v-if="errorType === 'restarting'" text="服务正在维护或重启中..."></t-loading>
    <t-alert theme="error" v-if="errorType === 'server-error'"> 服务器发生异常，请联系管理员 </t-alert>
    <t-alert v-if="errorType === 'init-error'"> 商城基础配置未完成，请联系平台运营 </t-alert>
    <img src="./image/failed.png" />
    <t-button class="into" shape="round" variant="outline" @click="jumpHome">
      返回首页
    </t-button>
  </div>
</t-watermark>


</template>

<script>
import { mapState } from 'vuex'
export default {
name: 'ErrorPage',
data() {
  return {
    title: '武汉大学工会福利平台',
    loopCount: 0
  }
},
computed: {
  statusCode() {
    let code = this.$route.query.code
    if(code === 'undefined') {
      return null
    }

    return code
  },
  errorType() {
    if(this.statusCode === '503' || this.loopCount < 5 ) {
      return 'restarting'
    } else if(this.statusCode === '500') {
      return 'server-error'
    } 
    return 'init-error'
  }
},
created() {
  this.loopQuery()
},
methods: {
  loopQuery() {
    const interval = 10000
    let func = () => {
      this.jumpHome()
      if(location.hash && location.hash.indexOf('error-page') >= 0) {
        setTimeout(func, interval)
      }
    }
    setTimeout(func, interval)
  },
  jumpHome () {
    this.loopCount++
    this.$router.push("/home")
  }
}
}
</script>

<style lang="less" scoped>

.error-wrapper {
padding-bottom: 24px;
background-color: #f5f6f8;
display: flex;
flex-direction: column;
align-items: center;
padding-top: 48px;
height: 100vh!important;
.text {
  color: #333;
  margin: 24px 0;
  font-size: 24px;
  font-weight: 700;
}
}
</style>