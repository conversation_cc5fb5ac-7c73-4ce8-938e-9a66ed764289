<template>
  <div class="after-sale-delivery">
    <div v-if="editModel === 2" style="width: 400px;"> 
      <t-form :data="formData" :rules="rules" ref="form" @submit="onSubmit" >
        <t-form-item label="物流公司" name="logisticsName">
          <t-input v-model.trim="formData.logisticsName" :maxlength="50" placeholder="请输入物流公司名称"></t-input>
        </t-form-item>
        <t-form-item label="物流单号" name="logisticsNo">
          <t-input v-model.trim="formData.logisticsNo" :maxlength="50" placeholder="请输入物流单号"></t-input>
        </t-form-item>
        <t-form-item label="退货日期" name="deliveryTime">
          <t-date-picker v-model="formData.deliveryTime"></t-date-picker>
        </t-form-item>
        <t-form-item style="margin-left: 100px">
          <t-space size="10px">
            <t-button theme="primary" @click="changeEditModel(1)">取消</t-button>
            <t-button theme="primary" type="submit">提交</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </div>
    <div v-if="editModel === 1"> 
      <div class="status-info">
        <t-alert theme="success" message="卖家已经同意您的退货申请，请尽快填写物流信息">
          <template #operation>
            <span @click="changeEditModel(2)">开始填写物流信息</span>
          </template>
        </t-alert>
      </div>
    </div>
  </div>
</template>

<script>
import { saveDelivery } from '@/views/Center/afterSale/api'
export default {
  name: "AfterSaleDelivery",
  props: {
    orderItemData: {
      required: true,
      default: () => {}
    }
  },
  data() {
    return {
      formData: {},
      editModel: 1,
      rules: {
        logisticsName: [
          { required: true, message: '请输入物流公司名称', trigger: 'blur' }
        ],
        logisticsNo: [
          { required: true, message: '请输入物流单号', trigger: 'blur' }
        ],
        deliveryTime: [
          { required: true, message: '请选择退货日期', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    changeEditModel(val) {
      this.editModel = val
      if(this.editModel === 2) {
        if(!this.formData.deliveryTime) {
          this.formData.deliveryTime = new Date()
        }
      }
    },
    async onSubmit({ validateResult, firstError }) {
      if (validateResult === true) {
        let params = Object.assign({}, this.formData)
        params.no = this.orderItemData.afterSaleInfo.no
        let res = await saveDelivery(params)
        if(res.code === 0) {
          this.$message.success('提交成功')
          this.$emit("on-submit")
        }
      } 
    },
  }

}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.after-sale-delivery {
  margin-top: 20px;
}
</style>