<template>
  <t-dialog
    header="选择取消原因"
    body="对话框内容"
    :visible.sync="visible"
    :destroyOnClose="true"
    :closeOnOverlayClick="false"
    :footer="footer"
    class="cancel-dialog"
    @close="cancelDialog"
  >
    <t-alert theme="warning" class="alert">
      <template #message>
        订单取消成功后将无法恢复；拆单后取消订单，其他子单也将一并取消
      </template>
    </t-alert>
    <!-- <div class="type-wrapper">
      <div @click="checkType(1)" class="type-btn" :class="{ selected: checked === 1 }">
        未收到货
      </div>
      <div @click="checkType(2)" class="type-btn" :class="{ selected: checked === 2 }">
        已收到货
      </div>
    </div> -->
    {{ checkedReason }}
    <div class="reason-wrapper" v-if="checked === 1">
      <div class="reason-content">
        <div class="cut-line">
          <div class="line"></div>
          <div class="content">请告诉我们您的取消原因，我们会努力做的更好</div>
        </div>
        <div class="button-list">
          <div v-for="item in buttonList" :key="item" class="reason-btn" :class="{ selected: checkedReason === item }" @click="checkReason(item)">
            {{ item }}
          </div>
        </div>
      </div>
      <div class="btn-list">
        <t-button class="mr12" theme="default" variant="base" @click="cancelDialog">再想想</t-button>
        <t-button theme="default" variant="base" :disabled="!checkedReason" @click="submit">提交</t-button>
      </div>
    </div>
    <div class="reason-wrapper" v-if="checked === 2">
      <div class="reason-content">
        <div>
          若您对已收到的商品质量不满意，可对商品提交售后申请；
        </div>
        <div>
          提交申请后系统将自动为您确认收货，订单状态将自动变为“已完成”；
        </div>
      </div>
      <div class="btn-list">
        <t-button theme="default" variant="base" @click="afterSale">申请售后</t-button>
      </div>
    </div>
  </t-dialog>
</template>
  
<script>
export default {
  data() {
    return {
      footer: false,
      visible: false,
      checked: 1,
      orderStatus: ['审核中', '审核通过', '待发货', '已发货', '已完成', '已取消'],
      checkedReason: '不想要了',
      buttonList: ['不想要了', '地址信息填写错误', '配送时间问题', '商品降价', '商品错选/多选', '物流无跟踪记录', '商品无货', '货物破损已拒签', '未收到货', '价格高于其他平台', '没用/少用/错用优惠']
    }
  },
  created() {},
  methods: {
    showDialog() {
      this.visible = true
    },
    checkType(type) {
      this.checked = type
    },
    checkReason(item) {
      this.checkedReason = item
    },
    afterSales() {
      this.$message.info('申请售后')
    },
    cancelDialog() {
      this.checked = 1
      this.visible = false
      this.$emit('cancelDialog')
      this.checkedReason = '不想要了'
    },
    submit() {
      this.$emit('submit', this.checkedReason)
      this.visible = false
      this.checked = 1
      this.checkedReason = '不想要了'
    },
    afterSale() {
      this.$emit('afterSale')
    }
  }
}
</script>
  
<style lang="less" scoped>
.cancel-dialog {
  .alert {
    margin-bottom: 16px;
  }
  .type-wrapper {
    font-size: 14px;
    font-weight: 700;
    display: flex;
    margin-bottom: 16px;
    .type-btn {
      width: 201px;
      height: 36px;
      line-height: 36px;
      border: 1px solid #ddd;
      cursor: pointer;
      position: relative;
      text-align: center;
      &:first-child {
        margin-right: 12px;
      }
    }
  }
  .reason-wrapper {
    .reason-content {
      font-size: 12px;
      background-color: #f7f7f7;
      padding: 12px 16px;
      margin-bottom: 12px;
      .cut-line {
        position: relative;
        width: 100%;
        text-align: center;
        margin-bottom: 16px;

        .line {
          width: 100%;
          background-color: #e1e1e1;
          height: 1px;
          z-index: 1;
          position: absolute;
          top: 9px;
        }
        .content {
          position: relative;
          display: inline-block;
          background-color: #f7f7f7;
          padding: 0 4px;
          z-index: 11;
          line-height: 18px;
        }
      }

      .button-list {
        display: flex;
        flex-wrap: wrap;
        .reason-btn {
          position: relative;
          width: calc(50% - 12px);
          height: 30px;
          line-height: 30px;
          margin-bottom: 12px;
          border: 1px solid #ddd;
          text-align: center;
          cursor: pointer;
          &:nth-child(odd) {
            margin-right: 12px;
          }
        }
      }
    }
    .btn-list {
      display: flex;
      justify-content: center;
      .mr12 {
        margin-right: 12px;
      }
    }
  }
  .selected {
    border-color: #e1251b!important;
    border-width: 2px!important;
  }
  .type-btn::after, .reason-btn::after {
    content: "";
    display: none;
    width: 13px;
    height: 13px;
    position: absolute;
    right: 0;
    bottom: 0;
    background: url('@/views/Trade/images/choosed.png') no-repeat;
  }
  .type-btn.selected::after, .reason-btn.selected::after {
    display: block;
  }
}
</style>
