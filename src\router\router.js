export default [
  {
    path: "/mall/sso-link",
    name: 'SsoLink',
    component: () => import("@/views/Ssolink"),
    meta: { showFooter: false, title: "金采通", hiddenHeader: true },
  },
  {
    path: "/center",
    component: () => import("@/views/Center"),
    meta: { showFooter: true },
    children: [
      {
        path: "myorder",
        meta: { showFooter: true, title: "金采通-我的订单" },
        component: () => import("@/views/Center/myOrder"),
      },
      {
        path: "grouporder",
        meta: { showFooter: true },
        component: () => import("@/views/Center/groupOrder"),
      },
      {
        path: "collect",
        meta: { showFooter: true, title: "金采通-收藏商品" },
        component: () => import("@/views/Center/collect"),
      },
      {
        path: "address",
        meta: { showFooter: true, title: "金采通-我的地址" },
        component: () => import("@/views/Center/address"),
      },
      {
        path: "profile",
        meta: { showFooter: true, title: "金采通-个人信息" },
        component: () => import("@/views/Center/profile"),
      },
      {
        path: "password",
        meta: { showFooter: true, title: "金采通-修改密码" },
        component: () => import("@/views/Center/password"),
      },
      {
        path: "wishlist",
        meta: { showFooter: true, title: "金采通-心愿单" },
        component: () => import("@/views/Center/wishList"),
      },
      {
        path: "comment",
        meta: { showFooter: true, title: "金采通-评价" },
        component: () => import("@/views/Center/comment"),
      },
      {
        path: "points",
        meta: { showFooter: true, title: "金采云-积分管理" },
        component: () => import("@/views/Center/points"),
      },
      {
        path: "after-sale",
        meta: { showFooter: true, title: "金采通-售后" },
        component: () => import("@/views/Center/afterSale"),
      },
      { path: "", redirect: "myorder" },
    ],
  },
  {
    path: "/paysuccess",
    component: () => import("@/views/PaySuccess"),
    meta: { showFooter: true, hiddenHeader: false },
    beforeEnter: (to, from, next) => {
      if (!from.name || from.name === 'Pay') {
        next();
      } else {
        next('/home');
      }
    },
  },
  {
    path: "/paybridge",
    component: () => import("@/views/Pay/bridge"),
    meta: { showFooter: false, hiddenHeader: true  },
    beforeEnter: (to, from, next) => {
      if (from.name === 'Pay' || !from.name) {
        next();
      } else {
        next('/home');
      }
    }
  },
  {
    path: "/trade-success",
    component: () => import("@/views/TradeSuccess"),
    meta: { showFooter: true, title: "金采通-订单提交成功页" },
    beforeEnter: (to, from, next) => {
      if (['Trade', 'TradeNow'.includes(from.name)]) {
        next();
      } else {
        next("/home");
      }
    },
  },
  {
    path: "/approve",
    component: () => import("@/views/Approve"),
    meta: { showFooter: true, title: "金采通-订单采购页" },
  },
  {
    path: "/pay",
    name: 'Pay',
    component: () => import("@/views/Pay"),
    meta: { showFooter: true, hiddenHeader: true },
    beforeEnter: (to, from, next) => {
      if (['Trade', 'TradeNow'.includes(from.name)]) {
        next();
      } else {
        next('/home');
      }
    }
  },
  {
    path: "/trade",
    name: 'Trade',
    component: () => import("@/views/Trade"),
    meta: { showFooter: true, title: "金采通-订单结算页" },
  },
  // 立即采购
  {
    path: "/trade-now/:skuId",
    name: 'TradeNow',
    component: () => import("@/views/Trade"),
    meta: { showFooter: true, title: "金采通-订单结算页" },
  },
  // 购物车
  {
    path: "/shopcart",
    component: () => import("@/views/ShopCart"),
    meta: { showFooter: true, title: "金采通-采购车" },
  },
  // 订单详情页
  {
    path: "/orderDetail",
    component: () => import("@/views/Order"),
    meta: { showFooter: true, title: "金采通-订单详情页" },
  },
  {
    path: "/addcartsuccess",
    component: () => import("@/views/AddCartSuccess"),
    meta: { showFooter: true, title: "金采通-添加采购车成功" },
    name: "addcartsuccess",
  },
  // 商品详情页
  {
    path: "/detail/:supplierId/:skuId",
    component: () => import("@/views/Detail"),
    meta: { showFooter: true, title: "金采通-商品详情页" },
  },
  // 商品详情页
  {
    path: "/sku/:skuId",
    component: () => import("@/views/Detail"),
    meta: { showFooter: true, title: "金采通-商品详情页" },
  },
  // 商品对比页
  {
    path: "/compare",
    component: () => import("@/views/Compare"),
    meta: { showFooter: true, title: "金采通-商品对比页" },
  },
  // 店铺商详页
  {
    path: "/storeDetail/:supplierId/:skuId",
    component: () => import("@/views/Detail"),
    meta: { showFooter: true, title: "金采通-店铺商详页" },
  },
  // 首页
  {
    path: "/home",
    component: () => import("@/views/Home"),
    meta: { showFooter: true, title: "金采通-首页" },
    name: "home",
  },
  // 动态路由 搜索页
  {
    path: "/search/:keyword?",
    component: () => import("@/views/Search"),
    meta: { showFooter: true, title: "金采通-商品搜索页" },
    name: "search",
  },
  // 店铺搜索页
  {
    path: "/storeSearch/:keyword?",
    component: () => import("@/views/Search"),
    meta: { showFooter: true, title: "金采通-店铺搜索页" },
    name: "search",
  },
  // 供应商 商品列表页
  {
    path: "/supplier",
    component: () => import("@/views/Supplier"),
    meta: { showFooter: true, hiddenHeader: true, title: "金采通-供应商信息" },
    name: "supplier",
  },
  // 平台资讯
  {
    path: "/information",
    component: () => import("@/views/Information"),
    meta: { showFooter: true, title: "金采通-平台资讯" },
    name: "information",
  },
  // 平台资讯内容
  {
    path: "/information/content",
    component: () => import("@/views/Information/content"),
    meta: { showFooter: true, title: "金采通-通知公告" },
    name: "informationContent",
  },
  // 客服
  {
    path: "/customerservice",
    meta: { showFooter: true, title: "金采通-客服热线" },
    component: () => import("@/views/CustomerService"),
    name: "customerservice",
  },
  // CMS协议规则内容页1
  {
    path: "/static/rule",
    component: () => import("@/views/StaticPage/rule"),
    meta: { showFooter: false, hiddenHeader: true, title: "金采通-协议规则" },
  },
  // CMS协议规则内容页1
  {
    path: "/static/agreement",
    component: () => import("@/views/StaticPage/agreement"),
    meta: { showFooter: false, hiddenHeader: true, title: "金采通-协议规则" },
  },
  // 登录页用户
  {
    path: "/login",
    component: () => import("@/views/Loginunit"),
    meta: { showFooter: false, hiddenHeader: true, title: "金采通-登录页" },

  },
    // 用户认证
    {
      path: "/find-pass",
      component: () => import("@/views/UserForm"),
      meta: { showFooter: false, hiddenHeader: true, title: "金采通-用户认证" },
    },
    // 登录页
    {
      path: "/login---1",
      component: () => import("@/views/Login"),
      meta: { showFooter: false, hiddenHeader: true, title: "金采通-登录页" },
    },
  // 注册页
  {
    path: "/register",
    component: () => import("@/views/Register"),
    meta: { showFooter: false, title: "金采通-注册页" },
  },
  // 错误页面
  {
    path: "/error-page",
    component: () => import("@/views/ErrorPage"),
    meta: { showFooter: false, hiddenHeader: true, title: "金采通-错误提示页" },
  },
  { path: "/", redirect: "/home" },
];
