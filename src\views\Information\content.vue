<template>
    <div class="info-wrapper">
      <t-breadcrumb class="info-breadcrumb" :maxItemWidth="'300'">
        <t-breadcrumbItem :to="{ path: '/information'}">平台资讯</t-breadcrumbItem>
        <t-breadcrumbItem>{{contentObj.title}}</t-breadcrumbItem>
      </t-breadcrumb>
      <div class="rule-box">
        <h3 class="information-title">{{contentObj.title}}</h3>
        <h4 class="information-date">发布时间：{{contentObj.publishTime | formatDate('yyyy-MM-dd HH:mm:ss')}}</h4>
        <div class="rule-wrapper" v-if="contentObj.content" v-html="contentObj.content"></div>
      </div>
    </div>
  </template>
      
  <script>
  import { getContentById } from "@/views/Home/api";
  export default {
    name: 'StaticRule',
    data() {
      return {
        contentObj: {}
      };
    },
    methods: {
      async showContent(id) {
        let res = await getContentById(id)
        if (res.code !== 0 || !res.data) {
          this.$message.error("内容不存在");
          return;
        }
        this.contentObj = res.data
      }
    },
    created() {
      let cid = this.$route.query.cid
      if (cid) {
        this.showContent(cid)
      }
    }
  };
  </script>
      
  <style lang="less" scoped>
  @import "@/style/variables.less";
  
  .info-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #F5F6F8;
    .info-breadcrumb {
      width: 1200px;
      margin: 16px 0;
    }
    .rule-box {
      background: #fff;
      width: 1200px;
      min-height: 500px;
      padding: 0;
      margin-bottom: 30px;
      .information-title {
        padding: 40px 0 16px 0;
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        color: #333;
      }
      .information-date {
        font-size: 14px;
        text-align: center;
        color: #999;
        margin-bottom: 30px;
      }
      .rule-wrapper {
        padding: 0 120px 50px;
        box-sizing: border-box;
        /deep/img {
          max-width: 100%;
          object-fit: fill!important;
        }
      }
    }
  }
  </style>
      