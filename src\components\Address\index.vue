<template>
  <t-cascader v-model="region" value-type="full" :placeholder="placeholder" :load="load" @change="handleChange"
    :options="regionOptions" :input-props="inputProps" clearable />
</template>
  
<script>
import { mapState } from 'vuex'
import { getAreas } from './api'

export default {
  name: 'address-comp',
  data() {
    return {
      regionOptions: [],
      placeholder: '请选择所在地区',
      region: [],
      inputProps: {
        value: '',
      }
    }
  },
  created() {
    if (!this.defaultRegions || this.defaultRegions.length === 0) {
      // this.$store.dispatch('loadDefaultRegions')
    } else {
      this.$emit('changeAddress', this.defaultRegions, this.inputProps.value)
      this.getProvince()
    }
  },
  props: {
    notDefault: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      defaultRegions: state => state.defaultAddress.defaultRegions
    })
  },
  methods: {
    async getProvince() {
      const res = await getAreas({
        addressLevel: 'PROVINCE',
        areaId: ''
      })
      if (res.code === 0) {
        this.regionOptions = res.data.map(x => {
          return {
            label: x.areaName,
            value: String(x.areaId),
            children: true
          }
        })
        if (this.defaultRegions && this.defaultRegions.length > 0) {
          const province = this.regionOptions.find(x => parseInt(x.value) === this.defaultRegions[0])?.label
          let res = await getAreas({
            addressLevel: 'CITY',
            areaId: this.defaultRegions[0]
          })
          let inputValArr = []
          let city = '', county = '', town = '';
          inputValArr.push(province)
          if(this.defaultRegions[1]) {
            city = res.data.find(x => x.areaId == this.defaultRegions[1])?.areaName
            inputValArr.push(city)
            if(this.defaultRegions[2]) {
              res = await getAreas({
                addressLevel: 'COUNTRY',
                areaId: this.defaultRegions[1]
              })
              county = res.data.find(x => x.areaId === this.defaultRegions[2])?.areaName
              inputValArr.push(county)
              if(this.defaultRegions[3]) {
                res = await getAreas({
                  addressLevel: 'TOWN',
                  areaId: this.defaultRegions[2]
                })
                town = res.data.find(x => x.areaId === this.defaultRegions[3])?.areaName
                inputValArr.push(town)
              }
            }
          }
          this.inputProps.value = inputValArr.join(' / ')
          this.region = this.defaultRegions.map(x => String(x))
        }
      }
    },
    async load(node) {
      const list = ['PROVINCE', 'CITY', 'COUNTRY', 'TOWN']
      const res = await getAreas({
        addressLevel: list[node.level + 1],
        areaId: node.value
      })
      if (res.code === 0) {
        return res.data.map(x => {
          return {
            label: x.areaName,
            value: String(x.areaId),
            children: node.level === 0 || ( node.level === 1 && node.label.indexOf('区') < 0)
          }
        })
      }
    },
    handleChange(value, context) {
      const { node } = context;
      if (node) {
        const path = node.getPath();
        const labelPath = path.map((item) => item.label).join(' / ');
        this.inputProps.value = labelPath;
      } else {
        this.inputProps.value = ''
      }
      this.$emit('changeAddress', this.region, this.inputProps.value)
    }
  },
  watch: {
    defaultRegions: {
      handler: function (val, oldVal) {
        if (val && val.length > 0) {
          this.$emit('changeAddress', val, this.inputProps.value)
          this.getProvince()
        }
      },
      deep: true
    }
  }
}
</script>
  
<style lang="less" scoped>
</style>
  