<template>
  <div class="preferred">
    <div class="title">
      <div class="title-text">优选电商</div>
    </div>
    <div class="content">
      <div class="left">
        <div class="store">{{supplier.name}}</div>
        <t-button class="into" theme="primary" shape="round" variant="outline" @click="toStoreSearch">
          进店看看
        </t-button>
      </div>
      <div class="right" v-loading="loading">
        <t-tabs :value="activeIndex" @change="changeTab">
          <t-tab-panel :value="index" v-for="(item, index) in categoryList" :key="item.id">
            <template #label>
              <icon name="home" style="margin-right: 4px" /> {{ item.name }}
            </template>
          </t-tab-panel>
        </t-tabs>
        <div class="tab-content">
          <div v-for="(item,index) in activeProductList" :key="index" class="category" @click="jumpDetail(item)">
            <t-image :src="item.imageUrl" style="width: 120px; height: 120px"></t-image>
            <div class="content">
              <div class="item-title" :title="item.skuName">{{ item.skuName }}</div>
              <div v-if="item.salePrice != -1" class="price">￥ {{ item.salePrice | formatMoney }}
                <span class="market-price" v-if="enableMarketPrice() && item.marketPrice">￥{{ (item.marketPrice || 0) |
                  formatMoney }}</span>
              </div>
              <span v-else class="need-login" style="margin-top: 20px;; margin-bottom: 5px">登录后显示价格</span>
            </div>
          </div>
          <div v-if="productList[activeIndex] && !productList[activeIndex].length && !loading" class="nothing">
            <t-image fit="cover" class="image" :src="emptySrc"></t-image>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Icon } from 'tdesign-icons-vue';
import { goodsSearchSimpleList } from '@/views/Search/api'
import { mapState } from 'vuex'

export default {
  name: "Like",
  props: {
    config: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      productList: {},
      activeIndex: -1,
      imgSrc: require("./images/preferred.png"),
      emptySrc: require('@/assets/nothing.png'),
      loading: false
    };
  },
  computed: {
    ...mapState({
      supplierList: state => state.Home.supplierList,
      defaultRegions: state => state.defaultAddress.defaultRegions
    }),
    supplier() {
      return this.supplierList[0] || {}
    },
    categoryList() {
      if(this.config.bestSupplierCategorys) {
        return this.config.bestSupplierCategorys || []
      }
      return []
    },
    checkedCategory() {
      if(this.categoryList.length && this.activeIndex >= 0) {
        return this.categoryList[this.activeIndex]
      }
      return null
    },
    activeProductList() {
      if(this.productList[this.activeIndex]) {
        return this.productList[this.activeIndex] || []
      }

      return []
    }
  },
  components: {
    Icon
  },
  methods: {
    async getData() {
      let param1 = this.getCategoryIdParams()
      let activeIndex = this.activeIndex
      if(!param1.name) {
        return
      }
      this.loading = true
      let param = {
        sortType: 1,
        pageIndex: 1,
        pageSize: 12,
        supplierId: this.supplier.id,
        areaIds: this.defaultRegions.join(',')
      }
      
      param[param1.name] = param1.id
      const res = await goodsSearchSimpleList(param)
      if (res.code === 0) {
        this.$set(this.productList, activeIndex, res.data || [])
      }
      this.loading = false
    },
    getCategoryIdParams() {
      let category = this.checkedCategory
      if(!category && this.categoryList.length) {
        category = this.categoryList[0]
        this.activeIndex = 0
      }
      if(!category) {
        return {}
      }

      return {
        name: 'categoryId' + category.level,
        id: category.id
      }
    },
    jumpDetail(item) {
      this.$router.push(`/sku/${item.skuId}`)
    },
    changeTab(val) {
      this.activeIndex = val;
      console.log(this.productList)
      if (this.productList[val]) {
        return;
      }
      this.getData()
    },
    toStoreSearch() {
      this.$router.push('/storeSearch')
    }
  },
  created() {
    if(!this.categoryList.length) {
      return
    }
    let cateId = this.categoryList[0].id
    setTimeout(() => {
+      this.changeTab(cateId)
     }, 1500)
  }
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.preferred {
  width: 1200px;
  margin: 20px auto 10px;
  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .title-text {
      color: #333;
      font-size: 20px;
      font-weight: 700;
    }
  }

  .content {
    width: 100%;
    background-color: #fff;
    display: flex;

    .left {
      background-color: #edeae8;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 489px;
      min-width: 180px;

      .store {
        width: 140px;
        font-size: 14px;
        text-align: center;
        margin-top: 12px;
        margin-bottom: 20px;
      }
    }

    .right {
      position: relative;
      margin-left: 16px;
      padding: 0 20px;
      flex: 1;

      .tab-content {
        display: flex;
        flex-wrap: wrap;
        height: calc(100% - 48px);

        .category {
          width: 120px;
          margin: 12px 20px;
          cursor: pointer;

          .content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .item-title {
              margin-top: 3px;
              margin-bottom: 8px;
              height: 36px;
              line-height: 18px;
              font-size: 12px;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              overflow: hidden;
              &:hover {
                color: #e11b10;
              }
            }

            .price {
              font-size: 16px;
              line-height: 16px;
              color: #e11b10;
            }
          }
        }
        .nothing {
          display: flex;
          flex: 1;
          justify-content: center;
          /deep/.t-image__wrapper {
            height: 200px;
            margin-top: 80px;
          }
        }
      }

    }
  }
}</style>
