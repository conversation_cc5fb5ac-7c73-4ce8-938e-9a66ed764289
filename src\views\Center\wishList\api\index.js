import request from "@/base/service";

const requestP = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_PRODUCT_API,
    },
  });
};

// 查询心愿单列表
export function wishPage(params) {
  return requestP({
    url: "/wish/page",
    method: "get",
    params,
  });
}

// 新建心愿单
export function wishCreate(data) {
  return requestP({
    url: "/wish/create",
    method: "post",
    data,
  });
}

// 更新心愿单
export function wishUpdate(data) {
  return requestP({
    url: "/wish/update",
    method: "put",
    data,
  });
}

// 删除心愿单
export function wishDelete(id) {
  return requestP({
    url: `/wish/delete?id=${id}`,
    method: "delete"
  });
}
