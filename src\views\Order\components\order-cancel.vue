<template>
  <div>
    <cancel-dialog @submit="submitCancel" ref="dialog" @cancelDialog="cancelDialog"></cancel-dialog>
  </div>
</template>

<script>
import { cancelOrder } from '@/views/Trade/api'
import cancelDialog from '@/views/Order/components/cancelDialog'
export default {
  name: 'TradeOrderCancel',
  components: { cancelDialog },
  data() {
    return {
      cancelOrderNo: ''
    }
  },
  methods: {
    cancelOrder(order) {
      this.cancelOrderNo = order.no
      this.$refs.dialog.showDialog()
    },
    async submitCancel(cancelReason) {
      const res = await cancelOrder({
        orderNo: this.cancelOrderNo,
        cancelReason
      })
      if (res.code === 0) {
        this.$message.success('取消订单成功！')
        this.cancelOrderNo = ''
        this.$emit('on-cancel')
      }
    },
    cancelDialog() {
      this.cancelOrderNo = ''
    }
  }
}
</script>

<style>

</style>
