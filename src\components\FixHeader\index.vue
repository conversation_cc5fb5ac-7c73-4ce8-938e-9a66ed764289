<template>
  <div class="header-container" :class="{ hiddenComp: !showHome }">
    <div class="home-container">
      <div class="home-header" :class="{ 'center-header': notNav }">
        <div class="header-section-top">
          <div class="header-top">
            <div class="symbol" @click="jumpToHome" style="margin-bottom: 25px; margin-top:15px">
              <t-image 
              fit="contain"
              :src="styleConfigData.logoUrl" />
            </div>
          <div class="search">
            <div class="searchInput">
              <t-input
                v-model="query"
                size="large"
                placeholder="请输入商品名称或商品编码"
                @enter="searchItem"
              />
              <div @click="searchItem" class="searchBtn">搜索</div>
            </div>
              <t-badge :count="cartCount">
                <t-button
                  @click="jumpToCart"
                  class="shoppingcar"
                  shape="round"
                  variant="outline"
                >
                  <!-- <CartIcon
                    style="font-size: 18px;"
                    slot="icon"
                  /> -->
                  <div class="shop-content">
                    <img :src="cartUrl" />
                    购物车
                  </div>
                </t-button>
              </t-badge>
            </div>
          </div>

          <div class="nav" v-if="!notNav">
            <div class="button-like" v-if="checkTab === '/home'">
              <span class="icon"></span>
              <span class="text">全部商品分类</span>
            </div>
            <t-button class="nav-item nav-builtin" size="small" theme="primary" :class="{'checkTab': checkTab === '/home'}" @click="jumpToHome">首页</t-button>
            <!-- <t-button class="nav-item" size="small" theme="primary" @click="jumpToNo" v-if="false">场景馆</t-button> -->
            <t-button class="nav-item nav-builtin" v-if="dropDownList.length <= 1" size="small" :class="{'checkTab': checkTab === '/storeSearch'}" @click="jumpToStore" theme="primary">优选电商</t-button>
            <t-dropdown :options="dropDownList" @click="clickHandler" v-if="dropDownList && dropDownList.length > 1" trigger="click" placement="bottom" :maxHeight="350">
              <t-button class="nav-item nav-builtin" size="small" :class="{'checkTab': checkTab === '/storeSearch'}" theme="primary">优选电商</t-button>
            </t-dropdown>
            <!-- <t-button class="nav-item nav-builtin" size="small" theme="primary" @click="jumpToNo" v-if="false">网上超市</t-button>
            <t-button class="nav-item nav-builtin" size="small" theme="primary" @click="jumpToNo" v-if="false">竞价&询价</t-button> -->
            <!-- <t-button class="nav-item nav-builtin" size="small" theme="primary" :class="{'checkTab': checkTab.includes('/information')}" @click="jumpToInfo">平台资讯</t-button> -->
            <!-- <t-button class="nav-item nav-builtin" size="small" :class="{'checkTab': checkTab === '/center/wishlist'}" @click="jumpToWishList" theme="primary">心愿单</t-button> -->
            <t-button class="nav-item nav-builtin" size="small" :class="{'checkTab': checkTab === '/customerservice'}" @click="jumpToCustomer" theme="primary">客服热线</t-button>
            <t-button class="nav-item nav-builtin" size="small" :class="{'checkTab': checkTab === '/center/points'}" @click="jumpToPoints" theme="primary">我的积分</t-button>
            <!-- <t-button class="nav-item nav-builtin" size="small"  theme="primary" @click="jumpToZC">一体化平台</t-button -->
            <!-- <div class="nav-item nav-builtin" @click="jumpToNo">商家入住</div> -->
            <div v-if="extraMenuList" id="extra-menu-container">
              <t-button class="nav-item" size="small" v-for="(item,index) in extraMenuList" :key="index" @click="jumpExtra(item)" theme="primary">{{item.name}}</t-button>
            </div>
          </div>
        </div>

        <!-- 类别、轮播 -->
        <div class="header-section-nav" v-if="showHome">
          <TypeNav ></TypeNav>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { CartIcon } from "tdesign-icons-vue";
import { mapState } from 'vuex'

export default {
  name: "HomeHeader",
  data() {
    return {
      goSrc: require("@/assets/supplier-register.png"),
      cartUrl: require("./images/shopcart.png"),
      query: "",
    };
  },
  components: {
    CartIcon,
  },
  created() {

  },
  computed: {
    ...mapState({
      configData: state => state.Home.configData,
      userInfo: state => state.User.userInfo,
      homeConfigData: state => state.Home.homeConfigData,
      supplierList: state => state.Home.supplierList,
      cartCount: state => state.User.cartCount,
      styleConfigData: state => state.Home.styleConfigData,
      supplierList: state => state.Home.supplierList
    }),
    dropDownList() {
      return this.supplierList.map(item => {
        return {
          value: item.id,
          content: item.name
        }
      })
    },
    showHome() {
      return this.$route.path === "/home";
    },
    notNav() {
      return (
        this.$route.path.includes("center") ||
        this.$route.path.includes("order")
      ) && false;
    },
    checkTab() {
      return this.$route.path;
    },
    extraMenuList() {
      let arr = this.homeConfigData.extraMenuList || []
      arr = arr.filter(x => x.position === 1)
      arr.sort((a,b) => a.sort - b.sort)
      return arr
    }
  },
  methods: {
    clickHandler(data) {
      this.$router.push(`/supplier?id=${data.value}`);
    },
    // 跳转到首页
    jumpToHome() {
      this.$router.push("/home");
    },
    supplierAdd() {
      this.$message.warning("供应商入驻暂未开放，敬请期待");
    },
    // 跳转到购物车
    jumpToCart() {
      this.$router.push("/shopcart");
    },
    // 搜索商品、店铺
    searchItem() {
      let encodedKeyword = encodeURIComponent(this.query);
      this.$router.push(`/search?keyword=${encodedKeyword}`);
    },
    jumpToStore() {
      this.$router.push("/storeSearch?isSelected=1");
    },
    jumpToPoints() {
      let ctxPath = process.env.VUE_APP_PATH_CONTEXT;
      window.open(location.origin + ctxPath + "/#/center/points", "_blank");
    },
    jumpToWishList() {
      let ctxPath = process.env.VUE_APP_PATH_CONTEXT;
      window.open(location.origin + ctxPath + "/#/center/wishlist", "_blank");
    },
    jumpToCustomer() {
      this.$router.push("/customerservice");
    },
    jumpToInfo() {
      this.$router.push("/information");
    },
    jumpToZC() {
      window.open("https://zc2.whu.edu.cn", "_blank");
    },
    jumpToNo() {
      this.$message.warning("功能还在开发中，敬请期待");
    },
    jumpExtra(obj) {
      let target = obj.isOutUrl ? '_blank' : '_self'
      window.open(obj.url, target)
    },
    ajustExtraMenuSort() {
      if(this.extraMenuList && this.extraMenuList.length) {
        let builtInavItems = document.getElementsByClassName('nav-builtin')
        let extraMenuParent = document.getElementById('extra-menu-container')
        if(!extraMenuParent) {
          return
        }
        if(extraMenuParent.getAttribute('sorted')) {
          return
        }
        let sort = this.extraMenuList[0].sort
        if(sort < builtInavItems.length && sort >= 1) {
          let moveIndex = sort
          let moveTarget = builtInavItems[sort - 1]
          moveTarget.parentNode.insertBefore(extraMenuParent, moveTarget);
        } 
        extraMenuParent.setAttribute('sorted', '1')
      }
    }
  },
  watch: {
    $route: {
      handler(newVal, oldVal) {
        this.query = this.$route.query.keyword || "";
      },
    },
    extraMenuList() {
      this.ajustExtraMenuSort()
    }
  }
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.header-container {
  position: relative;
  width: 100%;
  .home-container {
    width: 100%;
    .home-header {
      width: 100%;
      height: 100%;
      background: var(--td-bg-color-1);
      margin: 0 auto;
      margin-top: 25px;
      .header-section-top {
        width: 100%;
        height: 100%;
        border-bottom: 2px solid #F02C1C;
        background-color: #fff;
      }
      .header-section-nav {
        width: 100%;
        height: 100%;
        background-color: #89858517 !important;
      }
      .header-top {
        width: 1200px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        .symbol {
          width: 587px;
          height: 54px;
          line-height: 54px;
          cursor: pointer;
          /deep/.t-image__wrapper {
            background-color: transparent;
          }
        }
        .search {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 60px;
          .searchInput {
            width: 355px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid @primary-color;
            border-top-right-radius: 40px;
            border-bottom-right-radius: 40px;
            /deep/.t-input {
              border-radius: 0;
              border: 0;
              .t-input__inner {
                &::placeholder {
                  color: #808080;
                }
              }
            }
            .searchBtn {
              width: 60px;
              height: 40px;
              line-height: 40px;
              cursor: pointer;
              padding: 0 12px;
              border-top-right-radius: 18px;
              border-bottom-right-radius: 18px;
              color: #fff;
              letter-spacing: 0;
              background-color: @primary-color;
            }
          }
          .shoppingcar {
            width: 109px;
            height: 40px;
            margin-left: 30px;
            border: 1px solid #f22e00;
            color: #f22e00;
            .shop-content {
              display: flex;
              align-items: center;
              justify-content: center;
              img {
                width: 16px;
                height: 16px;
                margin-right: 5px;
              }
            }
          }
        }
        .right-wrap {
          width: 25%;
          text-align: right;
          .image {
            cursor: pointer;
            width: 200px;
            height: 60px;
            margin-left: 100px;
          }
        }
      }

      .nav {
        width: 1200px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .button-like {
          display: flex;
          height: 40px;
          align-items: center;
          padding: 8px 12px;
          width: 190px;
          background-color: #f22e00;
        }
        .icon {
          width: 20px; /* 调整图标宽度 */
          height: 20px; /* 调整图标高度 */
          background-image: url("../../assets/Frame.png");
          background-size: cover;
          margin-right: 8px;
        }

        .text {
          font-size: 14px;
          color: #fff;
        }
        .nav-item {
          height: 40px;
          line-height: 40px;
          padding: 0 20px;
          font-size: 15px;
          border: 0;
          border-radius: 0;
          letter-spacing: 2px;
          color: var(--td-bg-color-6);
          background-color: var(--td-bg-color-1);
        }
        .checkTab {
          background-color: var(--td-bg-color-1);
          color: @primary-color;
        }
      }
    }
  }
}

.hiddenComp {
  height: 136px !important;
  background: #fff !important;
}

.center-header {
  height: 70px;
}

// 大学的主题
#college-wrap {
  .header-container {
    height: 953px;
    background: var(--home-bg);
    border-bottom: none;
    .home-container {
      background: linear-gradient(
        to right,
        var(--td-brand-color-11),
        var(--td-brand-color-12)
      );
      border-bottom: none;
      .home-header {
        .searchInput {
          border: 1px solid #fff;
          border-top-right-radius: 40px;
          border-bottom-right-radius: 40px;
          /deep/.t-input {
            border-radius: 0;
            border: 0;
            background-color: var(--td-brand-color-11);
            .t-input__inner {
              color: #fff;
              &::placeholder {
                color: #fff;
              }
            }
          }
        }
        .searchBtn {
          color: var(--td-brand-color-7) !important;
          background-color: var(--td-bg-color-1) !important;
        }
        .shoppingcar {
          background-color: var(--td-brand-color-11);
        }
        .nav {
          .nav-item {
            background-color: var(--td-brand-color-11);
            color: var(--td-bg-color-1);
          }
          .checkTab {
            background-color: var(--td-bg-color-1);
            color: var(--td-brand-color-11);
          }
        }
      }
    }
  }
  .hiddenComp {
    .home-container {
      background: var(--td-brand-color-12)!important;
      .home-header {
        .nav {
          // .nav-item {
          //   background-color: #41a29b;
          // }
          .checkTab {
            background-color: var(--td-bg-color-1);
          }
        }
      }
    }
  }
}
</style>
<<<<<<< HEAD
=======
<style lang="less">
@import "@/style/variables.less";
.t-badge--circle, .t-badge--round {
  background-color: @secondary-color !important;
}
</style>
>>>>>>> master
