import request from '@/base/service'

const requestBase = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_PRODUCT_API
    }
  })
}

const requestC = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_TRADE_API
    }
  })
}

// 查询单个商品的详细信息，包含商品售卖单位、重量、产地、包装清单、主图、规格参数等 【废弃】
// export function getSkuDetailInfo(params) {
//   return requestBase({
//     url: '/vopgoods/getSkuDetailInfo',
//     method: 'get',
//     params
//   })
// }

// 查询单个商品的详细信息，包含商品售卖单位、重量、产地、包装清单、主图、规格参数 、查询商品上架、详情图片、可售、权益等
export function getSkuDetailInfo(params) {
  return requestBase({
    url: '/sku/getSkuDetailInfo',
    method: 'get',
    params
  })
}

// 获取兄弟商品
export function getBrotherSkus(params) {
  return requestBase({
    url: '/sku/getBrotherSkus',
    method: 'get',
    params
  })
}

// 查询京东单个商品的库存状态，价格，轮播图等
export function getSkuStockInfo4JD(params) {
  return requestBase({
    url: '/sku/getSkuStockInfo',
    method: 'get',
    params
  })
}

// 查询单个商品同品目销量排行榜列表
export function getSkuSuggestList(params) {
  return requestBase({
    url: '/sku/getSkuSuggestList',
    method: 'get',
    params
  })
}

// 查询单个商品的预计可送达时间
export function getSkuPredictPromise(params) {
  return requestBase({
    url: '/vopgoods/getSkuPredictPromise',
    method: 'get',
    params
  })
}

// 查询商品上架、详情图片、可售、权益等 【废弃】
export function getAssociationSkuDetailInfo(params) {
  return requestBase({
    url: '/vopgoods/getAssociationSkuDetailInfo',
    method: 'get',
    params
  })
}

// 批量查询商品库存状态等
export function getNewStockById(data) {
  return requestBase({
    url: '/vopgoods/getNewStockById',
    method: 'post',
    data
  })
}

// 查询商品库存状态等
export function queryGoodsStockInfo(params) {
  return requestBase({
    url: '/vopgoods/queryGoodsStockInfo',
    method: 'get',
    params
  })
}

// 收藏商品
export function addToCollect(params) {
  return requestC({
    url: '/collect/addToCollect',
    method: 'post',
    params
  })
}

// 取消收藏商品
export function cancelCollect(params) {
  return requestC({
    url: '/collect/cancelCollect',
    method: 'post',
    params
  })
}

// 分页查询收藏商品
export function getCollectPage(params) {
  return requestC({
    url: '/collect/getCollectPage',
    method: 'get',
    params
  })
}

// 查询是否收藏
export function queryCollectStatus(data) {
  return requestC({
    url: '/collect/queryCollectStatus',
    method: 'post',
    data
  })
}

// 查询是否收藏
export function skuPriceCompare(data) {
  return requestBase({
    url: '/sku/skuPriceCompare',
    method: 'post',
    data
  })
}
