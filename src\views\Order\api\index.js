import request from '@/base/service'

const requestT = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_TRADE_API
    }
  })
}

// 根据订单号查询物流信息
export function queryLogicticsInfo(params) {
  return requestT({
    url: '/vopAfterSales/queryLogicticsInfo',
    method: 'get',
    params
  })
}

// 查询订单配送信息
export function queryDeliveryInfo(params) {
  return requestT({
    url: '/vopAfterSales/queryDeliveryInfo',
    method: 'get',
    params
  })
}

// 确认收货（#118 不用这个）
// export function confirmReceiveByOrder(data) {
//   return requestT({
//     url: '/vopAfterSales/confirmReceiveByOrder',
//     method: 'post',
//     data
//   })
// }

// 确认收货
export function confirmReceiveByOrder(data) {
  return requestT({
    url: '/order/receiveOrder',
    method: 'post',
    data
  })
}

// 查询售后服务组件URL
export function getAfterSaleComponentUrl(params) {
  return requestT({
    url: '/vopComponent/getAfterSaleComponentUrl',
    method: 'get',
    params
  })
}

// 查询订单项固资系统跳转链接
export function getAssetsLink(params) {
  return requestT({
    url: '/order/get-assets-link',
    method: 'get',
    params
  })
}
