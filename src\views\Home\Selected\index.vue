<template>
  <div class="selected">
    <div class="title">
      <div class="title-text">精选好物</div>
      <div class="more" @click="jumpToStore">查看更多 ></div>
    </div>
    <div class="content" v-loading="loading">
      <div class="selected-left">
        <t-button
          :class="{ 'active-btn': activeIndex === index }"
          class="left-btn"
          v-for="(item, index) in categoryList"
          :key="item.name"
          shape="round"
          @mouseover="mouseOver(item,index)"
          @click="todoJump(item)"
          >{{ item.name }}</t-button
        >
      </div>
      <div class="selected-right">
        <div v-for="(item,index) in activeProductList" :key="index" class="category" @click="jumpDetail(item)">
          <t-image :src="item.imageUrl" style="width: 110px; height: 110px"></t-image>
          <div style="width: calc(100% - 118px);margin-left: 8px;">
            <div class="item-title" :title="item.skuName">{{ item.skuName }}</div>
            <div v-if="item.salePrice != -1" class="price">￥ {{ item.salePrice | formatMoney }}
              <span class="market-price" v-if="enableMarketPrice() && item.marketPrice">￥{{ (item.marketPrice || 0) |
                formatMoney }}</span>
            </div>
	          <span v-else class="need-login" style="margin-top: 20px;; margin-bottom: 5px">登录后显示价格</span>
            <div class="from">京东</div>
          </div>
        </div>
        <div v-if="activeProductList.length === 0 && !loading" class="nothing">
          <t-image fit="cover" class="image" :src="emptySrc"></t-image>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { goodsSearchSimpleList } from '@/views/Search/api'
import { mapState } from 'vuex'

export default {
  name: "Like",
  props: {
    config: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      productList: {},
      emptySrc: require('@/assets/nothing.png'),
      activeIndex: -1,
      loading: false
    };
  },
  computed: {
    ...mapState({
      defaultRegions: state => state.defaultAddress.defaultRegions
    }),
    categoryList() {
      if(this.config.bestProductCategorys) {
        return this.config.bestProductCategorys || []
      }
      return []
    },
    checkedCategory() {
      if(this.categoryList.length && this.activeIndex >= 0) {
        return this.categoryList[this.activeIndex]
      }
      return null
    },
    activeProductList() {
      if(this.productList[this.activeIndex]) {
        return this.productList[this.activeIndex] || []
      }

      return []
    }
  },
  methods: {
    jumpToStore() {
      this.$router.push("/storeSearch?isSelected=1");
    },
    // 保留两位小数
    saveTwoDecimal(val = 0) {
      return (Math.round(val * 100) / 100).toFixed(2)
    },
    async getData() {
      let param1 = this.getCategoryIdParams()
      let activeIndex = this.activeIndex
      if(!param1.name) {
        return
      }
      this.loading = true
      let param = {
        sortType: 1,
        pageIndex: 1,
        pageSize: 6,
        areaIds: this.defaultRegions.join(',')
      }
      
      param[param1.name] = param1.id
      const res = await goodsSearchSimpleList(param)
      if (res.code === 0) {
        this.$set(this.productList, activeIndex, res.data || [])
      }
      this.loading = false
    },
    todoJump(item) {
      let query = {}
      query['categoryId' + item.level] = item.id
      this.$router.push({
        path: '/storeSearch',
        query: query
      })
    },
    getCategoryIdParams() {
      let category = this.checkedCategory
      if(!category && this.categoryList.length) {
        category = this.categoryList[0]
        this.activeIndex = 0
      }
      if(!category) {
        return {}
      }

      return {
        name: 'categoryId' + category.level,
        id: category.id
      }
    },
    jumpDetail(item) {
      this.$router.push(`/sku/${item.skuId}`)
    },
    mouseOver(item, i) {
      this.activeIndex = i;
      if (this.productList[i]) {
        return;
      }
      this.getData()
    }
  },
  created() {
    this.mouseOver(0)
  }
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.selected {
  width: 1200px;
  margin: 20px auto 10px;
  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .title-text {
      color: #333;
      font-size: 20px;
      font-weight: 700;
    }
    .more {
      color: #999;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .content {
    position: relative;
    display: flex;
    height: 270px;
    .selected-left {
      height: 270px;
      overflow-y: auto;
      overflow-x: hidden;
      background-color: #fff;
      border-radius: 4px;
      padding: 20px 32px;
      .left-btn {
        width: 136px;
        height: 37px;
        display: block;
        margin-top: 16px;
        background-color: #f5f6f8;
        border-color: #fff;
        color: #333;
        &.active-btn {
          background-color: @primary-color!important;
          border-color: #fff !important;
          color: #fff;
        }
      }
    }
    .selected-right {
      background-color: #fff;
      flex: 1;
      margin-left: 16px;
      padding: 20px;
      display: flex;
      flex-wrap: wrap;
      .category {
        display: flex;
        width: 288px;
        cursor: pointer;
        .item-title {
          margin-top: 3px;
          margin-bottom: 8px;
          height: 36px;
          line-height: 18px;
          font-size: 12px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          &:hover {
            color: #e11b10;
          }
        }
        .price {
          font-size: 16px;
          line-height: 16px;
          color: #e11b10;
          margin-bottom: 18px;
        }
        .from {
          margin-top: 27px;
          color: #666;
          font-size: 12px;
          &:hover {
            color: #e11b10;
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
@import "@/style/variables.less";

.t-tabs__bar {
  background-color: @primary-color;
}
</style>
