<template>
  <div class="school-wrapper">
    <div class="title">
      <div class="title-text">校园风采</div>
    </div>
    <div class="content">
      <t-image-viewer v-for="(item, index) in advPositionList" :key="item.imgUrl" :default-index="index" :images="advImages" style="height:129px;">
        <template #trigger="{ open }">
          <div class="tdesign-demo-image-viewer__ui-image" >
            <img alt="test" :src="item.imgUrl" class="tdesign-demo-image-viewer__ui-image--img" />
            <div class="tdesign-demo-image-viewer__ui-image--hover" @click="open">
              <span><browse-icon size="1.4em" /> 预览</span>
            </div>
          </div>
        </template>
      </t-image-viewer>
    </div>
  </div>
</template>

<script>
import { getAdvPositionList } from '@/views/Home/api'
import { BrowseIcon } from 'tdesign-icons-vue';
export default {
  name: "School",
  data() {
    return {
      advPositionList: [],
      advImages: [],
      imageList: [
        require('@/views/Home/School/images/1.jpg'),
        require('@/views/Home/School/images/2.jpg'),
        require('@/views/Home/School/images/3.jpg'),
        require('@/views/Home/School/images/4.jpg'),
        require('@/views/Home/School/images/5.jpg')
      ]
    };
  },
  components: {
    BrowseIcon,
  },
  created() {
    this.loadAdvPositionList()
  },
  methods: {
    async loadAdvPositionList() {
      const res = await getAdvPositionList({terminalType: 1, bizType: 10})
      if (res.code === 0) {
        this.advPositionList = res.data || []
        this.advImages = this.advPositionList.map(item => item.imgUrl)
      } 
    },
    jumpLink(item) {
      if (item.link) {
        window.open(item.link, '_blank')
      }
    }
  }
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.school-wrapper {
  width: 1200px;
  margin: 20px auto 10px;
  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .title-text {
      color: #333;
      font-size: 20px;
      font-weight: 700;
    }
    .more {
      color: #999;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .content {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    gap: 15px;
    padding: 10px;
    overflow-x: auto;
    .tdesign-demo-image-viewer__ui-image {
      flex-shrink: 0;
      width: 224px;
    }
  }

}
</style>
