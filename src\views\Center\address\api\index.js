import request from '@/base/service'

const requestBase = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_MEMBER_API
    }
  })
}

// 更新用户收件地址
export function updateAddress(data) {
  return requestBase({
    url: '/address/update',
    method: 'post',
    data
  })
}

// 获取省市区地址
export function getAreas(data) {
  return requestBase({
    url: '/address/getAreas',
    method: 'post',
    data
  })
}

// 创建用户收件地址
export function createAddress(data) {
  return requestBase({
    url: '/address/create',
    method: 'post',
    data
  })
}

// 获得用户收件地址列表
export function getList(params) {
  return requestBase({
    url: '/address/list',
    method: 'get',
    params
  })
}

// 获得默认的用户收件地址
export function getDefaultAddress(params) {
  return requestBase({
    url: '/address/get-default',
    method: 'get',
    params
  })
}

// 删除用户收件地址
export function deleteAddress(params) {
  return requestBase({
    url: '/address/delete',
    method: 'post',
    params
  })
}
