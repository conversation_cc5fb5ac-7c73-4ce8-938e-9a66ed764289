export const saveObject = (cacheKey, object) => {
  if(!cacheKey) {
    console.error('cacheKey is null or empty')
    return
  }
  if(!object || !object.id) {
    console.error('object is null or object has no id')
    return
  }
  cacheKey = `ca-${  cacheKey }`
  let cacheVal = localStorage.getItem(cacheKey)
  if(cacheVal) {
    cacheVal = JSON.parse(cacheVal)
    if(!cacheVal || !cacheVal.length) {
      cacheVal = []
    }
  } else {
    cacheVal = []
  }

  const persistObj = cacheVal.find(item => item.id === object.id)
  if(persistObj) {
    persistObj.updateTime = new Date().getTime()
  } else {
    object.updateTime = new Date().getTime()
    cacheVal.push(object)
  }

  localStorage.setItem(cacheKey, JSON.stringify(cacheVal))
}

export const getObjects = (cacheKey) => {
  if(!cacheKey) {
    console.error('cacheKey is null or empty')
    return
  }
  cacheKey = `ca-${  cacheKey }`
  let cacheVal = localStorage.getItem(cacheKey)
  if(cacheVal) {
    cacheVal = JSON.parse(cacheVal)
    if(!cacheVal || !cacheVal.length) {
      cacheVal = []
    }
  } else {
    cacheVal = []
  }
  cacheVal.sort((a,b) => b.updateTime - a.updateTime)
  if(cacheVal.length > 30) {
    return cacheVal.slice(0, 30)
  }

  return cacheVal
}