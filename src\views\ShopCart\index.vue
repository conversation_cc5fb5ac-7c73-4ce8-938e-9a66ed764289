<template>
  <div class="cart" v-loading="loading">
    <div class="cart-header">
      <div class="title">全部商品 {{ totalCount }}</div>
      <div class="address">
        <div style="white-space: nowrap;">配送至：</div>
        <Address style="width: 400px" @changeAddress="changeAddress"></Address>
      </div>
    </div>
    <div class="cart-main">
      <div class="cart-th">
        <div class="cart-th1">勾选</div>
        <div class="cart-th2">商品</div>
        <div class="cart-th3">单价（元）</div>
        <div class="cart-th4">数量</div>
        <div class="cart-th5">小计（元）</div>
        <div class="cart-th6">操作</div>
      </div>
      <div v-if="cartInfoList.length > 0">
        <div v-for="(shop, shopIndex) in cartInfoList" :key="shop.supplierId">
          <div class="cart-shop">
            <span>{{shop.supplierName}}</span>
          </div>
          <div class="cart-body">
            <div class="cart-list" v-for="(cart, index) in shop.skuItems" :key="cart.id">
              <div class="cart-list-con1">
                <t-checkbox :disabled="!cart.canPurchase" :checked="cart.selected" @change="updateChecked(cart, $event)"></t-checkbox>
              </div>
              <div class="cart-list-con2">
                <t-image v-if="cart.picUrl" class="image" :src="cart.picUrl"></t-image>
                <div @click="jumpToDetail(cart.supplierId, cart.skuId)" class="item-msg">{{ cart.skuName }}</div>
              </div>
              <div class="cart-list-con4">
                <span class="price">{{ cart.notCanPurchaseReason || `￥${saveTwoDecimal(cart.skuPrice)}`}}</span>
              </div>
              <div class="cart-list-con5">
                <t-input-number :min="cart.lowestBuy || 1" :max="99999" :allowInputOverLimit="false" @change="changeNum(cart)" v-model="cart.count" style="width:170px;"/>
                <template v-if="!cart.canPurchase">
                  <div v-if="cart.stock" style="margin-top: 4px;">
                    <div v-if="$bus.isStockValid(cart.stock.stockStateType)">{{ cart.stock.stockStateType | stockStateText }}</div>
                    <div v-else>无货</div>
                  </div>
                </template>
                <div v-else style="margin-top: 4px;">
                  <div>{{ cart.notCanPurchaseReason }}</div>
                </div>
              </div>
              <div class="cart-list-con6">
                <span class="sum">￥{{ saveTwoDecimal(cart.skuPrice * cart.count) }}</span>
              </div>
              <div class="cart-list-con7">
                <div class="btn" @click="deleteCartById(cart, shopIndex, index)">删除</div>
                <div class="btn" v-if="!isCollect" @click="toCollect('addToCollect', cart)">移到收藏</div>
                <div class="btn" v-if="isCollect" @click="toCollect('cancelCollect', cart)">移出收藏</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="cartInfoList.length === 0" class="nothing-wrapper">
        <t-image class="nothing" fit="cover" :src="imgSrc"></t-image>
        <div>暂无商品</div>
      </div>
    </div>
    <div class="cart-tool">
      <div class="select-all">
        <t-checkbox :disabled="allCheckDisabled" :checked="isAllChecked && cartInfoList.length != 0" :indeterminate="indeterminate" :onChange="updateAllCartChecked">全选</t-checkbox>
      </div>
      <div class="option">
        <div class="btn" @click="deleteAllCheckedCart">删除选中的商品</div>
        <!-- <div class="btn">移到我的关注</div>
        <div class="btn">清除下柜商品</div> -->
      </div>
      <div class="money-box">
        <div class="chosed">已选择 <span>{{ selectedCount }}</span>件商品</div>
        <div class="sumprice">总价（不含运费）：</div>
        <div class="summoney">￥{{ saveTwoDecimal(totalPrice) }}</div>
        <div class="sumbtn" @click="jumpTo" :class="{'disabled': selectedCount === 0}">
          结算
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDetail, deleteCart, updateCount, updateSelected, checkMix } from '@/views/ShopCart/api'
import { addToCollect, cancelCollect } from '@/views/Detail/api'
import { mapState } from 'vuex'

export default {
  name: 'ShopCart',
  data() {
    return {
      imgSrc: require("assets/nothing.png"),
      cartInfoList: [],
      totalCount: 0,
      loading: false,
      selectedCount: 0,
      totalPrice: 0,
      regions: [],
      cartAllItems: [],
      isCollect: false
    }
  },
  methods: {
    // 保留两位小数
    saveTwoDecimal(val = 0) {
      return (Math.round(val * 100) / 100).toFixed(2)
    },
    async getDetail() {
      this.loading = true
      const res = await getDetail({
        provinceId: this.regions[0],
        cityId: this.regions[1],
        countyId: this.regions[2],
        townId: this.regions[3] || null
      })
      if (res.code === 0) {
        this.setData(res.data)
      }
      this.loading = false
    },
    async changeNum(item) {
      if (item.count <= 0 || !item.count) {
        this.$nextTick(() => {
          this.$set(item, 'count', 1)
        })
        return
      }
      this.loading = true
      try {
        const res = await updateCount({
          skuId: item.skuId,
          count: item.count,
          area: {
            provinceId: this.regions[0],
            cityId: this.regions[1],
            countyId: this.regions[2],
            townId: this.regions[3] || null
          }
        })
        if (res.code === 0) {
          this.changeShopCartData()
        } else {
          // TODO
          // {"code": 1011000003,"data": null,"msg": "该商品库存不足"}
        }
      } finally {
        this.loading = false
      }
    },
    setData(data) {
      this.cartInfoList = data.shopItems
      this.changeShopCartData()
      this.$store.dispatch('loadCartCount')
    },
    changeShopCartData() {
      let totalCount = 0, totalPrice = 0, selectedCount = 0
      this.cartAllItems = []
      this.cartInfoList.forEach(shop => {
        shop.skuItems.forEach(item => {
          // canPurchase为false 说明商品有问题不能购买
          if (item.canPurchase) {
            this.cartAllItems.push(item)
            totalCount += item.count
            if (item.selected) {
              totalPrice += Number(this.saveTwoDecimal(item.skuPrice * item.count))
              selectedCount += item.count
            }
          }
        })
      })
      this.$nextTick(() => {
        this.totalCount = totalCount
        this.selectedCount = selectedCount
        this.totalPrice = totalPrice
        this.$store.dispatch('loadCartCount')
      })
    },
    checkCartMix() {
      return checkMix().then(res => {
        if (res.code === 0) {
          if(res.data !== true){
            this.$message.error('福利商品和扶贫商品不能混合结算')
          }
          else {
            return true
          }
        } else {
          this.$message.error(res.msg || '检查购物车商品失败')
          return false
        }
        return false
      })
    },
    jumpToDetail(supplierId, skuId) {
      this.$router.push(`/sku/${skuId}`)
    },
    jumpTo() {
      if (this.selectedCount === 0) {
        return;
      }
      this.checkCartMix().then(res => {
        if (res) {
          this.$router.push('/trade')
        }
      })
    },
    deleteAllCheckedCart() {
      if(!this.haveSelected) {
        this.$message.info("请先选中商品再进行此操作")
        return
      }
      this.deleteCart(this.cartAllItems.filter(x => x.selected).map(x => x.skuId).join(',')).then(res => {
        this.getDetail()
      })
    },
    deleteCartById(item, shopIndex, index) {
      this.deleteCart(item.skuId).then(res => {
        let items = this.cartInfoList[shopIndex].skuItems
        items.splice(index, 1)
        if(!items.length) {
          this.cartInfoList.splice(shopIndex, 1)
        }

        this.changeShopCartData()
      })
    },
    async deleteCart(skuIds = []) {
      this.loading = true
      const res = await deleteCart({ skuIds })
      if (res.code === 0) {
        // this.setData(res.data)
      } else {
        this.$message.error("删除商品失败")
      }
      this.loading = false
    },
    async updateChecked(cart, val) {
      this.loading = true
      cart.selected = val
      const res = await updateSelected({
        skuIds: [cart.skuId],
        selected: val
      })
      if (res.code === 0) {
        this.changeShopCartData()
      }
      this.loading = false
    },
    changeAddress(val = []) {
      this.regions = val
      this.getDetail()
    },
    async updateAllCartChecked(val) {
      this.loading = true
      const skuIds = []
      this.cartAllItems.forEach(item => {
        if (item.canPurchase) {
          item.selected = val
          skuIds.push(item.skuId)
        }
      })
      if(!skuIds.length) return
      const res = await updateSelected({
        skuIds: skuIds,
        selected: val
      })
      if (res.code === 0) {
        this.changeShopCartData()
      }
      this.loading = false
    },
    async toCollect(method, item) {
      let res = null
      if (method === 'addToCollect') {
        res = await addToCollect({
          skuId: item.skuId,
          supplierId: item.supplierId
        })
      } else {
        res = await cancelCollect({
          skuId: item.skuId,
          supplierId: item.supplierId
        })
      }
      if (res.code === 0) {
        this.$message.success('操作成功')
        this.getDetail()
      } else {
        this.$message.error('操作失败')
      }
    }
  },
  computed: {
    ...mapState({
      defaultRegions: state => state.defaultAddress.defaultRegions
    }),
    isAllChecked() {
      return this.cartAllItems.every(item => {
        return item.selected
      })
    },
    allCheckDisabled() {
      if(!this.cartInfoList.length) {
        return true
      }
      let flag = true
      this.cartInfoList.forEach(shop => {
        let canLength = shop.skuItems.filter(item => item.canPurchase).length
        if(canLength > 0) {
          flag = false
          return
        }
      })
      return flag
    },
    haveSelected() {
      return this.cartAllItems.filter(item => item.selected).length > 0
    },
    indeterminate() {
      let num = 0
      this.cartAllItems.forEach(item => {
        if (item.selected) {
          num++
        }
      })
      return num > 0 && num < this.cartAllItems.length
    }
  }
}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";
.cart {
  position: relative;
  width: 1200px;
  margin: 0 auto 16px;
  min-height: 500px;
  .cart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 32px;
    margin-top: 12px;
    margin-bottom: 6px;
    .title {
      font-size: 16px;
      color: @primary-color;
      font-weight: 600;
    }
    .address {
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }

  .cart-main {
    min-height: 360px;
    .cart-th {
      background: #f5f5f5;
      border: 1px solid #ddd;
      padding: 10px;
      overflow: hidden;
      display: flex;

      .cart-th1 {
        width: 10%;

        input {
          vertical-align: middle;
        }

        span {
          vertical-align: middle;
        }
      }

      .cart-th2 {
        width: 40%;
      }

      .cart-th3,
      .cart-th4,
      .cart-th5,
      .cart-th6 {
        width: 12.5%;
      }
    }

    .cart-shop {
      margin: 15px 0 0;
      padding: 5px 0;
      font-weight: 500;
    }

    .cart-body {
      margin: 0 0 15px;
      border: 1px solid #ddd;

      .cart-list {
        padding: 10px;
        border-bottom: 1px solid #ddd;
        overflow: hidden;
        display: flex;
        align-items: center;
        &:last-child {
          border-bottom: none;
        }

        .cart-list-con1 {
          width: 10%;
        }

        .cart-list-con2 {
          width: 40%;
          display: flex;
          align-items: center;

          .image {
            width: 82px;
            height: 82px;
          }

          .item-msg {
            width: 240px;
            margin-left: 12px;
            cursor: pointer;
            &:hover {
              color: @primary-color;
            }
          }
        }

        .cart-list-con4 {
          width: 10%;
        }

        .cart-list-con5 {
          width: 17%;
          text-align: center;

          .mins {
            border: 1px solid #ddd;
            border-right: 0;
            color: #666;
            width: 6px;
            text-align: center;
            padding: 8px;
          }

          input {
            border: 1px solid #ddd;
            width: 40px;
            height: 33px;
            text-align: center;
            font-size: 14px;
          }

          .plus {
            border: 1px solid #ddd;
            border-left: 0;
            color: #666;
            width: 6px;
            text-align: center;
            padding: 8px;
          }
        }

        .cart-list-con6 {
          width: 10%;

          .sum {
            font-size: 16px;
          }
        }

        .cart-list-con7 {
          width: 13%;
          color: #666;
          display: flex;
          align-items: center;
        }
      }
    }
    .nothing-wrapper {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      margin: 12px 0;

      .nothing {
        width: 360px;
        height: 200px;
        background-color: #fff;
      }
    }
  }

  .cart-tool {
    overflow: hidden;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .select-all {
      padding: 10px;
      overflow: hidden;
      display: flex;
      align-items: center;
      cursor: pointer;
      &:hover {
        color: @primary-color;
      }
    }

    .option {
      padding: 10px;
      overflow: hidden;
      color: #666;
      display: flex;
      align-items: center;
    }

    .money-box {
      margin-left: auto;
      display: flex;
      align-items: center;

      .chosed {
        line-height: 26px;
        padding: 0 10px;
      }

      .sumprice {
        margin-left: 12px;
      }
      .summoney {
        color: @primary-color;
        font-size: 16px;
      }

      .sumbtn {
        position: relative;
        cursor: pointer;
        width: 96px;
        height: 52px;
        line-height: 52px;
        margin-left: 24px;
        color: #fff;
        text-align: center;
        font-size: 18px;
        font-family: 'Microsoft YaHei';
        background: @primary-color;
        overflow: hidden;
        &.disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }
      }
    }
  }
  .btn {
    margin-right: 12px;
    cursor: pointer;
    &:hover {
      color: @primary-color;
    }
  }
}
</style>
