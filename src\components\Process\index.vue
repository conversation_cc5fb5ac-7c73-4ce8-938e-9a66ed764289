<template>
  <div class="process-wrapper">
    <div class="header-wrapper">
      <span class="header">
        <t-image class="header-icon" :src="image1"></t-image>
        <span class="header-text">网上直采流程</span>
      </span>
      <dl class="tips">
        <dd class="tips-container" v-for="(item,index) in list" :key="index">
          <span class="tips-icon" :class="`tips-icon${index + 1}`"></span>
          <span class="tip-text">{{ item }}</span>
        </dd>
      </dl>
    </div>
    <div class="content-wrapper">
      <ul class="ip-list">
        <li v-for="(item, index) in ipList1" :key="index">
          <a href="javascript:void(0)">
            <span class="ipl-item">
              <span class="ipl-show" :class="`item-${index + 1}`"></span>
              <span class="ipl-hide" :class="`item-${index + 1}`"></span>
            </span>
            <span class="ipl-tit">{{ item }}</span>
          </a>
        </li>
      </ul>
      <ul class="ip-list opposite">
        <li v-for="(item, index) in ipList2" :key="index">
          <a href="javascript:void(0)">
            <span class="ipl-item">
              <span class="ipl-show" :class="`item-${index + 7}`"></span>
              <span class="ipl-hide" :class="`item-${index + 7}`"></span>
            </span>
            <span class="ipl-tit">{{ item }}</span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</template>


<script>
export default {
  name: "Process",
  data() {
    return {
      list: ['采购人操作', '院部领导操作', '电商（供应商操作）', '财务操作'],
      ipList1: ['采购人登录', '挑选商品', '加入购物车', '提交订单', '选择项目经费卡', '院系部门审批'],
      ipList2: ['财务集中支付', '供应商申请结算', '资产建档', '确认收货', '电商接单发货', '财务审批'],
      image1: require('./images/process-1.png'),
      image2: require('./images/process-2.png')
    };
  },
  created() {},
  methods: {}
};
</script>

<style lang="less" scpoed>
@import "@/style/variables.less";

.process-wrapper {
  width: 1200px;
  margin: 24px auto 0;
  background-color: #ffffff;
  position: relative;
  .header-wrapper {
    background-color: #ecf3ef;
    border-top: 2px solid @primary-color;
    padding-right: 20px;
    height: 48px;
    .header {
      background-color: @primary-color;
      float: left;
      width: 200px;
      height: 46px;
      color: #FFF;
      font-size: 0px;
      line-height: 44px;
      text-align: center;
      .header-icon {
        display: inline-block;
        vertical-align: middle;
        width: 24px;
        height: 24px;
        background-color: #018d94;
      }
      .header-text {
        display: inline-block;
        vertical-align: middle;
        font-size: 20px;
        line-height: 20px;
        margin-left: 10px;
      }
    }
    .tips {
      float: right;
      margin-top: 16px;
      .tips-container {
        float: left;
        margin-left: 30px;
        font-size: 0px;
        .tips-icon {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          display: inline-block;
          vertical-align: middle;
        }
        .tips-icon1 {
          background-color: #FFBC1D;
        }
        .tips-icon2 {
          background-color: #EA5E45;
        }
        .tips-icon3 {
          background-color: #B38AD1;
        }
        .tips-icon4 {
          background-color: #8FB42E;
        }
        .tips-icon5 {
          background-color: #8FB42E;
        }
        .tip-text {
          color: #646464;
          font-size: 12px;
          line-height: 12px;
          margin-left: 8px;
          display: inline-block;
          vertical-align: middle;
        }
      }
    }
  }
  .content-wrapper {
    text-align: center;
    font-size: 0px;
    padding: 20px;
    .ip-list {
      display: inline-block;
      li {
        float: left;
        position: relative;
        margin-left: 50px;
        &:not(:first-child)::before {
          content: "";
          display: inline-block;
          position: absolute;
          background-image: url("./images/process-2.png");
          background-size: cover;
          top: 29px;
          left: -36px;
          line-height: 20px;
          height: 20px;
          width: 20px;
          height: 20px;
        }
        a {
          display: block;
          padding: 0 7px;
          &:link, &:hover {
            text-decoration: none;
          }
          &:link, &:visited {
            color: #323232;
            outline: none;
            -webkit-transition: all .3s ease-in-out;
            -moz-transition: all .3s ease-in-out;
            transition: all .3s ease-in-out;
          }
          .ipl-item {
            width: 72px;
            height: 72px;
            float: none;
            margin: 0 auto;
            display: block;
            overflow: hidden;
            position: relative;
            .ipl-show {
              opacity: 1;
              transform: rotateX(0deg) rotateY(0deg);
              transform-style: preserve-3d;
              backface-visibility: hidden;
            }
            .ipl-hide {
              position: absolute;
              top: 0px;
              left: 0px;
              width: 100%;
              height: 100%;
              opacity: 0;
              transform: rotateX(-180deg);
              transform-style: preserve-3d;
              backface-visibility: hidden;
            }
            .ipl-show.item-1 {
              background-position: 0 0;
            }
            .ipl-show.item-2 {
              background-position: 9.09091% 0;
            }
            .ipl-show.item-3 {
              background-position: 18.18182% 0;
            }
            .ipl-show.item-4 {
              background-position: 27.27273% 0;
            }
            .ipl-show.item-5 {
              background-position: 36.36364% 0;
            }
            .ipl-show.item-6 {
              background-position: 45.45455% 0;
            }
            .ipl-show.item-7 {
              background-position: 54.54546% 0;
            }
            .ipl-show.item-8 {
              background-position: 63.63637% 0;
            }
            .ipl-show.item-9 {
              background-position: 72.72728% 0;
            }
            .ipl-show.item-10 {
              background-position: 81.81819% 0;
            }
            .ipl-show.item-11 {
              background-position: 90.9091% 0;
            }
            .ipl-show.item-12 {
              background-position: 100% 0;
            }

            .ipl-hide.item-1 {
              background-position: 0 100%;
            }
            .ipl-hide.item-2 {
              background-position: 9.09091% 100%;
            }
            .ipl-hide.item-3 {
              background-position: 18.18182% 100%;
            }
            .ipl-hide.item-4 {
              background-position: 27.27273% 100%;
            }
            .ipl-hide.item-5 {
              background-position: 36.36364% 100%;
            }
            .ipl-hide.item-6 {
              background-position: 45.45455% 100%;
            }
            .ipl-hide.item-7 {
              background-position: 54.54546% 100%;
            }
            .ipl-hide.item-8 {
              background-position: 63.63637% 100%;
            }
            .ipl-hide.item-9 {
              background-position: 72.72728% 100%;
            }
            .ipl-hide.item-10 {
              background-position: 81.81819% 100%;
            }
            .ipl-hide.item-11 {
              background-position: 90.9091% 100%;
            }
            .ipl-hide.item-12 {
              background-position: 100% 100%;
            }

            span {
              display: block;
              width: 72px;
              height: 72px;
              text-align: center;
              background-image: url(assets/mdw4_1.png);
              background-repeat: no-repeat;
              background-size: 1200%;
              -webkit-transition: all .3s ease-in-out;
              -moz-transition: all .3s ease-in-out;
              transition: all .3s ease-in-out;
            }
          }
          .ipl-tit {
            display: block;
            width: 120px;
            color: #323232;
            font-size: 14px;
            line-height: 14px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            margin-top: 15px;
          }
        }
      }
      li:first-child {
        margin-left: 0;
      }
    }
    .opposite {
      padding-top: 50px;
      position: relative;
      li:last-child::after {
        content: "";
        display: inline-block;
        position: absolute;
        background-image: url("./images/process-2.png");
        background-size: cover;
        transform: rotate(90deg);
        top: -30px;
        right: 58px;
        line-height: 20px;
        height: 20px;
        width: 20px;
        height: 20px;
      }
      li::before {
        transform: rotate(180deg);
      }
    }
  }
}
</style>
