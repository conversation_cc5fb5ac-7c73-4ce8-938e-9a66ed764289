<template>
  <div class="profile-wrapper">
    <div class="user-info">
      <div class="user-wrap">
        <div class="title">
          <span>个人信息</span>  
        </div>
        <t-row :gutter="16">
          <t-col :span="3"><div>姓名：</div></t-col>
          <t-col :span="9"><div>{{ userInfo.nickname || '--' }}</div></t-col>
          <t-col :span="3"><div>工号：</div></t-col>
          <t-col :span="9"><div>{{ userInfo.userNo || '--' }}</div></t-col>
          <t-col :span="3"><div>电话：</div></t-col>
          <t-col :span="9"><div>{{ userInfo.mobile || '--' }}</div></t-col>
          <t-col :span="3"><div>部门编号：</div></t-col>
          <t-col :span="9"><div>{{ userInfo.deptCode || '--' }}</div></t-col>
          <t-col :span="3"><div>部门名称：</div></t-col>
          <t-col :span="9"><div>{{ userInfo.deptName || '--' }}</div></t-col>
          <t-col :span="3"><div>二级工会：</div></t-col>
          <t-col :span="9"><div>{{ userInfo.orgName || '--' }}</div></t-col>
        </t-row>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'Center-Profile',
  data() {
    return {

    }
  },
  computed: {
    userInfo() {
      return this.$store.state.User.userInfo
    }
  },
  methods: {

  }
}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.profile-wrapper {
  width: 83.33%;
  .user-info {
    background-color: #fff;
    display: flex;
    border: 1px solid #E9EBF2;
    border-radius: 4px;
    .btn {
      margin-left: 16px;
      width: 160px;
    }
    .user-wrap {
      flex: 1;
      padding: 24px;
      border-right: 1px solid #E9EBF2;
      &:last-child {
        border-right: none;
      }
      .title {
        font-size: 16px;
        height: 32px;
        line-height: 32px;
      }
    }
  }
}
</style>
