<template>
  <div class="seo-card-sku"> 
    <div class="sku-block" v-for="item in productList" :key="item.skuId" @click="jumpDetail(item)">
      <div class="save">省{{ (item.marketPrice - item.salePrice) | formatMoney(0) }}元</div>
      <div class="sku-img">
        <t-image :src="item.imageUrl" fit="fill" :style="{ width: '170px',height: '170px' }"></t-image>
      </div>
      <div class="content">
        <div class="item-title" :title="item.skuName">{{ item.skuName }}</div>
        <div v-if="item.salePrice != -1" class="price price-cust-block">
          <div class="sale-price">
            <div>福利价</div>
            <div>￥{{ item.salePrice | formatMoney }}</div>
          </div>
          <div class="market-price" v-if="enableMarketPrice() && item.marketPrice">
            <div>市场价</div>
            <div>￥{{ (item.marketPrice || 0) | formatMoney }}</div>
          </div>
        </div>
	      <span v-else class="need-login" style="margin-top: 20px;; margin-bottom: 5px">登录后显示价格</span>
      </div>
      <div class="tag-supplier" v-if="enableSupName()">{{ item.supplierName }}</div>
    </div>
    <div v-if="productList.length === 0 && !loading" class="nothing" style="height: 580px;">
      <t-image fit="cover" class="image" :src="emptySrc"></t-image>
    </div>
  </div>
</template>

<script>
import { goodsSearchSimpleList } from '@/views/Search/api'
import { mapState } from 'vuex'
export default {
  name: 'SeoCard-Sku',
  props: {
    tagIds: {
      type: Array,
      default: () => null
    },
    count: {
      type: Number,
      default: () => 10
    }
  },
  data() {
    return {
      emptySrc: require('@/assets/nothing.png'),
      loading: false,
      productList: []
    }
  },
  computed: {
    ...mapState({
      defaultRegions: state => state.defaultAddress.defaultRegions
    })
  },
  mounted() {
    this.getData()
  },
  methods: {
    async getData() {
      if(!this.tagIds || !this.tagIds.length) {
        return
      } 
      this.loading = true
      let params = {
        pageIndex: 1,
        pageSize: this.count,
        sortType: 1,
        areaIds: this.defaultRegions.join(','),
        tagIds: this.tagIds.join(',')
      }

      const res = await goodsSearchSimpleList(params)
      this.loading = false
      if (res.code === 0) {
        let list = res.data || []
        this.productList = list || []
      }
    },
    jumpDetail(item) {
      this.$router.push(`/sku/${item.skuId}`)
    }
  }
}
</script>

<style lang="less" scoped>
.seo-card-sku {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  .sku-block {
    background-color: #fff;
    height: 280px;
    width: 225px;
    margin: 0 0 10px;
    position: relative;
    .save {
      position: absolute;
      background: url("@/views/Home/Fuli/image/rectange.png") no-repeat;
      background-size: 100%;
      top: 15px;
      left: 0;
      width: 86px;
      line-height: 20px;
      text-align: center;
      color: #fff;
      z-index: 10;
      font-size: 0.9em;
    }
    .sku-img {
      padding: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
    .item-title {
      width: 100%;
      height: 32px;
      margin: 5px 0;
      padding: 0 10px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      color: #333;
      &:hover {
        color: #e11b10;
      }
    }
    .price {
      font-size: 18px;
      color: #e11b10;
    }
    .price-cust-block {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 5px 10px;
      font-size: 1em;
    }
  }
}
</style>