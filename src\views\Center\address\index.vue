<template>
  <div class="address-wrapper">
    <t-button class="add-address" @click="addAddress">新增收货地址</t-button>
    <div class="item-wrap" v-for="item in addressList" :key="item.id">
      <close-icon class="close-icon" @click="closeAddress(item)" />
      <div class="fixed">
        <t-button class="mr12" size="small" v-if="!item.defaulted" variant="text" theme="primary" @click="defaultClick(item)">设为默认</t-button>
        <t-button size="small" variant="text" theme="primary" @click="edit(item)">编辑</t-button>
      </div>
      <div class="title">{{item.name}} {{ item.provinceName }} <span v-if="item.defaulted" class="default">默认地址</span></div>
      <t-form style="width: calc(100% - 200px);">
        <t-form-item label="收货人：">
          {{item.name}}
        </t-form-item>
        <t-form-item label="所在地区：">
          {{ item.provinceName }}{{ item.cityName }}{{ item.countyName }}{{ item.townName }}
        </t-form-item>
        <t-form-item label="地址：">
          {{item.consigneeAddress}}
        </t-form-item>
        <t-form-item label="手机：">
          {{item.mobile.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2")}}
        </t-form-item>
        <t-form-item label="固定电话：">
          {{item.telephone}}
        </t-form-item>
        <t-form-item label="电子邮箱：">
          {{item.email}}
        </t-form-item>
      </t-form>
    </div>
    <div class="nothing" v-if="addressList.length === 0 && !loading">
      <t-image fit="cover" class="image" :src="emptySrc"></t-image>
      <div class="text">您还没有收货地址哦~</div>
    </div>
    <add-address v-if="visible" @successOpt="successOpt" @closeDialog="closeDialog" :defaultRegions="defaultRegions" :editData="formData"></add-address>
  </div>
</template>

<script>
import { CloseIcon } from "tdesign-icons-vue";
import { getList, deleteAddress, updateAddress } from '@/views/Center/address/api'
import addAddress from './add.vue'

export default {
  name: 'address-form',
  data() {
    return {
      visible: false,
      loading: true,
      regionOptions: [],
      formData: {},
      addressList: [],
      defaultRegions: [],
      emptySrc: require('@/assets/nothing.png')
    }
  },
  components: {
    CloseIcon,
    addAddress
  },
  async created() {
    this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      const res = await getList()
      if (res.code === 0) {
        this.addressList = res.data
      }
      this.loading = false
    },
    closeDialog() {
      this.visible = false
    },
    successOpt() {
      this.getList()
      this.closeDialog()
    },
    addAddress() {
      this.formData = {}
      this.defaultRegions = []
      this.visible = true
    },
    closeAddress(item) {
      let mydialog = this.$dialog({
        header: '提示',
        body: '您确定要删除该收货地址吗？',
        onConfirm: () => {
          this.deleteAddress(item)
          mydialog.hide()
        },
        onClose: () => {
          mydialog.hide()
        }
      });
    },
    async deleteAddress(item) {
      const res = await deleteAddress({
        id: item.id
      })
      if (res.code === 0) {
        this.$message.success('删除地址成功')
        this.getList()
      } else {
        this.$message.error('删除地址失败')
      }
    },
    async defaultClick(item) {
      const res = await updateAddress({
        ...item,
        defaulted: 1
      })
      if (res.code === 0) {
        this.$message.success('设置默认地址成功')
        this.getList()
      } else {
        this.$message.error('设置默认地址失败')
      }
    },
    edit(item) {
      this.formData = item
      this.defaultRegions = [item.provinceId, item.cityId, item.countyId, item.townId || null]
      this.visible = true
    }
  }
}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.address-wrapper {
  float: right;
  width: 83.33%;

  .add-address {
    margin-bottom: 24px;
  }

  .item-wrap {
    position: relative;
    border: 1px solid #E9EBF2;
    padding: 12px;
    line-height: 22px;
    margin-bottom: 16px;
    .close-icon {
      position: absolute;
      top: 12px;
      right: 12px;
      width: 24px;
      height: 24px;
      font-size: 16px;
      padding: 4px;
      cursor: pointer;
    }
    .fixed {
      position: absolute;
      bottom: 12px;
      right: 12px;
      height: 24px;
      padding: 4px;
      display: flex;
      align-items: center;
      cursor: pointer;
      .mr12 {
        margin-right: 12px;
      }
    }
    /deep/.t-form__item {
      margin-bottom: 0;
      height: 24px;
      line-height: 24px;
      .t-form__label {
        min-height: 24px;
        line-height: 24px;
      }
      .t-form__controls {
        min-height: 24px;
        line-height: 24px;
        .t-form__controls-content {
          min-height: 24px;
          line-height: 24px;
        }
      }
    }

    .title {
      margin-bottom: 12px;
      font-weight: 600;

      .default {
        background-color: orange;
        color: #fff;
        margin-left: 12px;
        padding: 0 4px;
      }
    }
  }
  .no-address {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .image {
      width: 400px;
    }
    .text {
      margin-top: 60px;
      font-size: 24px;
    }
  }
}

.alias-address {
  margin: 12px 0 0;

  .t-button {
    margin-right: 6px;
  }
}</style>
