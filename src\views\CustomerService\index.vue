<template>
  <div class="wishlist-wrapper">
    <div class="wishlist-content" v-loading="loading">
      <div class="title">
        <h3>客服热线</h3>
      </div>
      <div class="customer-wrapper">
        <div v-for="item in list" :key="item.id" class="item">
          <t-image
            :src="item.logoUrl || defaultSupplierLogoSrc"
            fit="contain"
            :style="{ width: '180px', height: '90px', background: '#fff' }"
            shape="round"
          />
          <div class="content">
            <div class="item-title">
              {{ item.fullName }}
            </div>
            <div class="info" style="margin-top: 24px;">
              <div class="info-item">
                <User1Icon
                  class="icon-cls"
                  slot="icon"
                />
                {{ item.serviceAgent || '--' }}
              </div>
              <div class="ml12 info-item">
                <MobileIcon
                  class="icon-cls"
                  slot="icon"
                />
                {{ item.servicePhone || '--' }}
              </div>
            </div>
            <div class="info">
              <div class="info-item">
                <LogoWechatStrokeIcon
                  class="icon-cls"
                  slot="icon"
                />
                {{ item.serviceWechatId || '--' }}
              </div>
              <div class="ml12 info-item">
                <LogoQqIcon
                  class="icon-cls"
                  slot="icon"
                />
                {{ item.serviceQqId || '--' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="list.length > 0" style="margin:20px 0">
        <t-pagination class="pagination" :total="total" :showPageNumber="true" :showPageSize="false"
          showPreviousAndNextBtn :totalContent="false" @current-change="changePage" />
      </div>
    </div>
  </div>
</template>

<script>
import { User1Icon, MobileIcon, LogoWechatStrokeIcon, LogoQqIcon } from "tdesign-icons-vue";
import { getServiceList } from '@/views/CustomerService/api';

export default {
  components: {
    User1Icon,
    MobileIcon,
    LogoWechatStrokeIcon,
    LogoQqIcon
  },
  data() {
    return {
      imageUrl: require('./image/noProduct.png'),
      defaultSupplierLogoSrc: require('@/assets/defaultSupplierLogo.png'),
      list: [],
      total: 0,
      loading: false,
      queryParams: {
        pageNo: 1,
        pageSize: 10
      }
    }
  },
  methods: {
    async loadList() {
      this.loading = true
      try {
        const res = await getServiceList(this.queryParams)
        this.list = res.data.list
        this.total = res.data.total
      } finally {
        this.loading = false
      }
    }, 
    changePage(page) {
      this.queryParams.pageNo = page
      this.loadList()
    }
  },
  async created() {
    this.loadList()
  }
}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";
.wishlist-wrapper {
  width: 1200px;
  margin: 0 auto 16px;

  //订单部分
  .wishlist-content {
    margin: 20px;
    color: #666;

    //标题
    .title {
      margin-bottom: 22px;
      border: 1px solid #ddd;
      h3 {
        padding: 12px 10px;
        font-size: 16px;
        background-color: #f1f1f1;
      }
    }
    .customer-wrapper {
      background-color: #fff;
      margin-top: 20px;
      display: flex;
      flex-wrap: wrap;
      gap: 24px;
      .item {
        width: calc(50% - 12px);
        display: flex;
        padding: 16px 12px;
        border: 1px solid #c9cdd4;
        border-radius: 4px;
      }
      .content {
        display: flex;
        font-size: 14px;
        flex-direction: column;
        justify-content: center;
        margin-left: 15px;
        .item-title {
          font-weight: 600;
          font-size: 16px;
          color: #1d2129;
        }
        .info {
          color: #4e5969;
          display: flex;
          margin-top: 6px;
          .info-item {
            width: 150px;
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            .icon-cls {
              font-size: 18px;
              margin-right: 8px;
              color: @primary-color;
            }
          }
        }
        .ml12 {
          margin-left: 12px;
        }
      }
    }
  }
}
</style>
