<template>
  <div class="paysuccess">

    <div class="success">
      <h3>
        <img src="./images/right.png" width="48" height="48">
        恭喜您，支付成功啦！
      </h3>
      <div class="paydetail">
        <p class="button">
          <t-button class="btn-item" @click="jumpDetail">查看订单</t-button>
          <t-button class="btn-item" @click="jumpGoods">继续购物</t-button>
        </p>
      </div>
    </div>

  </div>
</template>

<script>
  export default {
    name: 'PaySuccess',
    computed: {
      orderNo() {
        return this.$route.query.no
      },
      oType() {
        return this.$route.query.oType
      }
    },
    methods: {
      jumpDetail() {
        if(this.oType !== '1' && this.orderNo) {
          this.$router.push({
            path: '/orderDetail',
            query: {
              no: this.orderNo
            }
          })
          return
        }
        this.$router.push('/center/myorder')
      },
      jumpGoods() {
        this.$router.push('/')
      }
    }
  }
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

  .paysuccess {
    margin: 50px auto;
    padding: 25px;
    width: 1200px;
    min-height: 500px !important;

    .success {
      width: 500px;
      margin: 0 auto;
      h3 {
        margin: 20px 0;
        font-weight: 700;
        font-size: 20px;
        line-height: 30px;
        img {
          max-width: 100%;
          vertical-align: middle;
          border: 0;
          margin-right: 14px;
        }
      }

      .paydetail {
        margin-left: 26px;
        font-size: 15px;
        .button {
          margin: 30px 0;
          line-height: 26px;
          .btn-item {
            margin: 10px;
          }
        }
      }
    }
  }
</style>