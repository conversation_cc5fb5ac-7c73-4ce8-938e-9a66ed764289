stages:
  - sonarscan #代码检测
  - deploy #项目编译
deploy:
  stage: deploy
  only:
    - master
  tags:
    - frontend #对应runner tag 不要修改
  variables:
    VERSION: '' #版本号
    IMAGES: $CI_PROJECT_ROOT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME #容器名称
    IMAGES_NAME_PRE: $docker_url/$CI_PROJECT_ROOT_NAMESPACE/$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME:v1.0.0
  cache:
    paths: #缓存以下文件
    - node_modules/
  script: #根据当前分支名称编译脚本
    - TIME=$(date +%Y%m%d%H%M%S) #date和+要有一个空格，TIME
    - echo "你好, ${GITLAB_USER_LOGIN}!" #打印当前推送人员名称
    - echo "当前是${CI_COMMIT_BRANCH}分支" #打印当前分支名称
    - npm install #安装依赖
    - echo "npm run build:${CI_COMMIT_REF_NAME}" #输出运行脚本
    - npm run build:$CI_COMMIT_REF_NAME #构建项目
    - echo "容器名称:${IMAGES}"; #输出容器名称
    - "if docker images | grep ${IMAGES} | awk '{ print $1}';then
    docker rmi $(docker images | grep ${IMAGES} | awk '{ print $3}') -f ;
    fi"
    #根据分支判断创建镜像的名称版本
    - VERSION=${IMAGES_NAME_PRE}
    fi"
    - echo "镜像地址:${VERSION}";
    - docker build -t ${VERSION} .;
    - docker push ${VERSION} ;
