<template>
  <div class="swiper-container">
    <div class="swiper-wrapper">
      <div class="swiper-slide" :style="{ left: leftMove }">
        <div
          class="image"
          v-for="(slide, index) in sliderPicUrls"
          :key="slide + index"
          @mouseover="changeActive(index)"
          :class="{ active: currentIndex == index }"
        >
          <t-image
            style="width: 58px; height: 58px"
            :src="slide"
            loading="加载中..."
            :key="index"
          ></t-image>
        </div>
      </div>
    </div>
    <!-- 当前位置最多展示五条 -->
    <div class="swiper-button-next" :class="moveIndex < sliderPicUrls.length - 5 ? 'button' : 'disabled-btn'" @click.stop="next">
      <chevron-right-icon style="color: #bbb" />
    </div>
    <div class="swiper-button-prev" :class="moveIndex !== 0 ? 'button' : 'disabled-btn'" @click.stop="prev">
      <chevron-left-icon style="color: #bbb" />
    </div>
  </div>
</template>

<script>
import { ChevronLeftIcon, ChevronRightIcon } from "tdesign-icons-vue";

export default {
  name: "ImageList",
  data() {
    return {
      currentIndex: 0,
      moveIndex: 0,
      leftMove: 0,
    };
  },
  components: {
    ChevronLeftIcon,
    ChevronRightIcon,
  },
  props: ["sliderPicUrls"],
  methods: {
    changeActive(index) {
      this.currentIndex = index;
      this.$bus.$emit("changeIndex", index);
    },
    prev() {
      if (this.moveIndex === 0) {
        return;
      }
      this.moveIndex--;
      this.leftMove = `${this.moveIndex * -70}px`;
    },
    next() {
      if (this.moveIndex >= this.sliderPicUrls.length - 5) {
        return;
      }
      this.moveIndex++;
      this.leftMove = `${this.moveIndex * -70}px`;
    },
  },
};
</script>

<style lang="less" scoped>
.swiper-container {
  position: relative;
  padding: 0 24px;

  clear: both;
  overflow: hidden;
  margin-top: 16px;

  .swiper-wrapper {
    position: relative;
    width: 360px;
    height: 62px;
    overflow: hidden;

    .swiper-slide {
      position: absolute;
      width: 999999px;
      height: 80px;
      transition: all 0.5s;

      .image {
        border: 1px solid #E9EBF2;
        width: 60px;
        height: 60px;
        display: inline-block;
        margin-right: 12px;
        cursor: pointer;
        &.active {
          border: 1px solid #e11b10;
        }
      }
    }
  }
  .swiper-button-next {
    right: 0;
  }
  .swiper-button-prev {
    left: 0;
  }
  .swiper-button-next,
  .swiper-button-prev {
    position: absolute;
    box-sizing: border-box;
    width: 20px;
    height: 62px;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 100;

    &::after {
      font-size: 12px;
    }
  }
  .button {
    background-color: #fff;
    border: 1px solid #E9EBF2;
    cursor: pointer;
    &:hover {
      border-color: #e11b10;
    }
  }
  .disabled-btn {
    background-color: #f5f5f5;
    border-color: #e5e5e5;
    cursor: not-allowed;
  }
}
</style>
