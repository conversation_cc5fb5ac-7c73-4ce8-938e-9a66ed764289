<template>
  <div>
    <div class="fixed-tools"> 
      <t-button theme="primary" size="" @click="toCustomerService">
        <!-- <t-icon name="service" size="20px" />  -->
        客服
      </t-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HomeTools',
  methods: {
    toCustomerService() {
      this.$router.push('/customerservice')
    }
  }

}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.fixed-tools {
  position: fixed;
  border: 0;
  padding: 0;
  line-height: 1.6;
  cursor: pointer;
  z-index: 300;
  bottom: 45%;
  right: 24px;
  display: block;
  color: @back-top-color;
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.15);
  &:hover {
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.25);
  }
}

</style>