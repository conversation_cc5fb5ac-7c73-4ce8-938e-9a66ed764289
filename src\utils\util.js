
import router from "@/router"
import store from "@/store"
import { getSsoLogoutInfo, getSsoLogoutInfoV2 } from '@/components/Header/api'
import { sm2 } from "sm-crypto"

/**
 * 登录跳转处理
 * type: 1-内部登录
 *       2-自动判断
 * @param {*} type
 */
export const toLogin = (type = 2, backPath = '') => {
  store.commit('Clear')
  const configData = store.state.Home.configData || {}
  backPath = backPath || router.currentRoute.fullPath
  if(type === 1 || configData.loginType === 1) {
    router.push({ path: '/login', query: {redirect: backPath}});
  } else {
    window.location.href = appendRedirect4LoginUrl(configData.loginUrl, backPath)
  }
}

/**
 * 退出跳转处理, 根据登录方式自动判断退出方式
 * @param {*} type
 */
export const toLogout = () => {
  store.commit('Clear')
  let loginWay = sessionStorage.getItem('loginWay') || 'account'
  if(loginWay === 'account') {
    console.log('logout...')
    router.push("/home");
    return
  }

  const configData = store.state.Home.configData || {}
  let logoutFunc = null
  let lgoutParams = {
    redirect: router.currentRoute.path
  }
  if(configData.logoutType === 20) {
    logoutFunc = getSsoLogoutInfo
  } else if(configData.logoutType === 25) {
    logoutFunc = getSsoLogoutInfoV2
  } else {
    return
  }
  logoutFunc(lgoutParams).then(res => {
    if(res.code === 0) {
      window.location.href = res.data
    }
  })
}


/**
 * SSO-CAS登录地址处理，增加redirect
 * @param {*} originUrl
 * @returns
 */
export const appendRedirect4LoginUrl = (originUrl, extraPath) => {
  let url = new URL(originUrl)
  let serviceUrl = url.searchParams.get('service')
  if(!serviceUrl) {
    return originUrl
  }
  let decodedServiceUrl = decodeURIComponent(serviceUrl);
  let extra = ''
  if(decodedServiceUrl.indexOf('?') < 0) {
    extra += '?'
  } else {
    extra += '&'
  }
  extra += 'redirect=' + encodeURIComponent(extraPath)
  url.searchParams.set('service', serviceUrl + extra)
  console.log('url2=', url.href)
  return url.href
}

export const formatDateTime = (date, format) => {
  const o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 小时
    'H+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
    a: date.getHours() < 12 ? '上午' : '下午', // 上午/下午
    A: date.getHours() < 12 ? 'AM' : 'PM', // AM/PM
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }

  return format;
}

export const showFieldEnable = (name) => {
  const configData = store.state.Home.configData || {}
  return configData.productField && configData.productField.indexOf(name) >= 0
}

export const enableSupName = () => {
  return showFieldEnable('list-supplier')
}

export const enableMarketPrice = () => {
  return showFieldEnable('market-price')
}

export const encryptSm2 = (string) => {
  const cipherMode = 1;
  const publicKey =
    "04090D509CE405A6E0E9809547BA179BFB990DFECA4EE164152C41DC325E48C93BB4B82222F43AE168E887CFCC3686BF7B5F22E86D415514A6F1FF015D7BE20FA0";
  return sm2.doEncrypt(string, publicKey, cipherMode);
};

export const decryptSm2 = (string, privateKey) => {
  const cipherMode = 1;
  return sm2.doDecrypt(string, privateKey, cipherMode);
};
