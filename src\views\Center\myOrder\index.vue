<template>
  <div class="order-right" v-loading="loading">
    <div class="order-content">
      <div class="title">
        <h3>我的订单</h3>
      </div>
      <t-tabs v-model="form.status" @change="change" style="margin-bottom: 12px;">
        <t-tab-panel v-for="item in orderStatusList" :value="item.value" :label="item.label2" :key="item.label"></t-tab-panel>
      </t-tabs>
      <div class="chosetype" v-if="orderList.length > 0">
        <table>
          <thead>
            <tr>
              <th width="65%">订单详情</th>
              <th width="7%">收货人</th>
              <th width="11%">金额</th>
              <th width="7%">状态</th>
              <th>操作</th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="orders" v-if="orderList.length > 0">
        <table class="order-item" v-for="order in orderList" :key="order.id">
          <thead>
            <tr>
              <th colspan="5">
                <span class="ordertitle">
                  {{ order.submitTime | formatDate('yyyy-MM-dd HH:mm:ss') }}
                  <!-- 展示用no，查询用id -->
                  <span style="margin-left: 24px;"> 订单编号：{{ order.no }} </span>
                  <span class="pull-right delete" v-if="canDelete(order)"><img @click="deleteOrder(order)" src="../images/delete.png" /></span>
                </span>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(orderItem, index) in order.items" :key="orderItem.id">
              <td width="65%">
                <div class="typographic">
                  <img class="image" :src="orderItem.picUrl" />
                  <div class="block-text skuName" @click="toProductDetail(order.supplierId, orderItem)">
                    <div style="color:rgb(138, 135, 135);">{{ order.supplierName }}</div>
                    <div>{{ orderItem.skuName }}</div>
                  </div>
                  <div class="service">x{{ orderItem.count }}</div>
                  <div>
                    <div v-if="canAfterSale(order, orderItem)" class="service cursorPointer" @click="toAfterSale(order, orderItem)">{{ getAfterSaleBtnText(order, orderItem) }}</div>
                    <div v-if="configData.productCommentSwitch && canComment(orderItem)" class="service cursorPointer" @click="toComment(order, orderItem)">我要评价</div>
                    <div v-if="configData.productCommentSwitch && orderItem.commented" class="service">已评价</div>
                    <!-- <div v-if="canAssetGo(order, orderItem)" class="service cursorPointer" @click="toAssetsSys(orderItem)">建档通道</div> -->
                  </div>
                </div>
              </td>
              <td :rowspan="order.items.length" v-if="index === 0" width="7%" class="center">{{ order.receiverName }}
              </td>
              <td :rowspan="order.items.length" v-if="index === 0" width="11%" class="center">
                总金额 ¥{{ order.orderPrice | formatMoney }}
              </td>
              <td :rowspan="order.items.length" v-if="index === 0" width="7%" class="center">
                <div class="status">
                  <div :class="{'complete': order.status === 8 }">{{ order | orderStatusInfo }}</div>
                  <span v-if="order.status === 1 && !order.payed"> 待支付</span>
                  <span v-if="order.status === 1 && order.payed"> 已支付</span>
                  <span v-if="order.status === 1 && order.auditStatus !== 1">{{ order.auditStatus | orderAuditStatusInfo }}</span>
                </div>
              </td>
              <td :rowspan="order.items.length" v-if="index == 0" width="12%" class="center">
                <div class="order" @click="toOrderDetail(order.no)">订单详情</div>
                <div class="order" v-if="canCancel(order)" @click="cancelOrder(order)">取消订单</div>
                <div class="order" v-if="needPay(order)" @click="toPay(order)">去支付</div>
                <!-- <div class="order" v-if="canApprove(order)" @click="toApprove(order)">提交采购</div> -->
                <div class="order" v-if="canReceive(order, orderItem)" @click="receiveOrder(order)">确认收货</div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="choose-order" v-if="orderList.length > 0">
        <t-pagination class="pagination" :total="total" :showPageNumber="true" :showPageSize="false"
          showPreviousAndNextBtn :totalContent="false" @current-change="changePage" />
      </div>
      <div class="nothing" v-if="orderList.length === 0 && !loading">
        <t-image fit="cover" class="image" :src="emptySrc"></t-image>
        <div class="text">暂无数据</div>
      </div>
    </div>
    <order-cancel-dialog @on-cancel="getOrderPage" ref="dialog"></order-cancel-dialog>
  </div>
</template>

<script>
import { getOrderPage, getOrderStatusStats } from '@/views/Trade/api'
import { ORDER_STATUS_LIST } from '@/utils/orderUtil'
import OrderCancelDialog from '@/views/Order/components/order-cancel'
import { orderMixins } from '@/views/Order/components/orderMixin.js'
export default {
  name: 'CenterOrder',
  mixins: [ orderMixins ],
  components: { OrderCancelDialog },
  data() {
    return {
      emptySrc: require('@/assets/nothing.png'),
      page: 1,
      orderList: [],
      total: 0,
      options: [
        {label: '订单编号', value: 'no'},
        {label: '用户编号', value: 'userId'},
        {label: '用户手机号', value: 'userMobile'},
        {label: '收货人姓名', value: 'receiverName'},
        {label: '收货人手机号', value: 'receiverMobile'}
      ],
      form: {
        status: ''
      },
      loading: true,
      orderStatusList: []
    }
  },
  created() {
    this.buildStatusTabs()
    this.getOrderPage()
  },
  methods: {
    buildStatusTabs() {
      let firstOpt = [{ label: '全部', value: '' }]
      let list = ORDER_STATUS_LIST.filter(item => ![4,5,7].includes(item.value))
      this.orderStatusList = firstOpt.concat(list)
      this.orderStatusList.forEach(status => {
        status.label2 = status.label
      })
    },
    change() {
      this.page = 1
      this.getOrderPage()
    },
    async getOrderPage() {
      const params = {
        pageNo: this.page,
        pageSize: 10
      }
      if (this.form.status) {
        params.status = this.form.status
      }
      this.loading = true
      const res = await getOrderPage(params)
      if (res.code === 0 && res.data) {
        this.orderList = res.data.list || []
        this.total = Number(res.data.total)
      } else {
        this.orderList = []
        this.total = 0
      }
      this.loading = false
      this.loadOrderStatusStats()
    },
    async loadOrderStatusStats() {
      let res = await getOrderStatusStats()
      if(res.data) {
        let needCountStatus = [45,6,7,2,3]
        this.orderStatusList.forEach(status => {
          status.label2 = status.label
          if(needCountStatus.includes(status.value)) {
            let statusStat = res.data.find(stat => stat.status === status.value)
            if(statusStat && statusStat.count) {
              status.count = statusStat.count
              status.label2 = `${status.label}(${status.count})`
            }
          }
        })
        console.log('orderStatusList-----', this.orderStatusList)
        this.$forceUpdate()
      }
    },
    cancelOrder(order) {
      this.$refs.dialog.cancelOrder(order)
    },
    changePage(page) {
      this.page = page
      this.getOrderPage()
    },
    refresh() {
      this.getOrderPage()
    },
    afterDelete() {
      this.getOrderPage()
    }
  }
}
</script>

<style lang="less" scoped>
//右边
.order-right {
  width: 83.33%;

  //订单部分
  .order-content {
    margin: 0 20px;
    color: #666;

    //标题
    .title {
      margin-bottom: 22px;
      border: 1px solid #ddd;

      h3 {
        padding: 12px 10px;
        font-size: 16px;
        background-color: #f1f1f1;

      }
    }
    .search-form {
      margin: 24px 0;
      display: flex;
      flex-wrap: wrap;
      background-color: #f1f1f1;
      padding: 12px 10px
    }

    //表头
    .chosetype {
      margin-bottom: 16px;
      color: #666;

      table {
        border: 1px solid #e6e6e6;
        border-collapse: separate;
        border-radius: 2px;
        width: 100%;
        max-width: 100%;
        border-spacing: 0;

        th {
          padding: 6px 8px;
          color: #666;
          font-weight: 700;
          vertical-align: bottom;
          background-color: #f4f4f4;
          line-height: 18px;
          font-size: 12px;
          text-align: left;
        }
      }
    }

    // 表单内容
    .orders {
      font-size: 12px;

      a {
        &:hover {
          color: #4cb9fc;
        }
      }

      table {
        border: 1px solid #e6e6e6;
        border-collapse: collapse;
        border-radius: 2px;
        width: 100%;
        margin-bottom: 18px;
        max-width: 100%;

        th {
          padding: 6px 8px;
          line-height: 18px;
          text-align: left;
          vertical-align: bottom;
          background-color: #f4f4f4;
          font-size: 12px;
          color: #666;

          .pull-right {
            float: right;
            cursor: pointer;

            img {
              margin-right: 10px;
              vertical-align: middle;
            }
          }
        }

        td {
          font-size: 12px;
          color: #666;
          padding: 6px 8px;
          line-height: 18px;
          text-align: left;
          vertical-align: middle;
          border: 1px solid #e6e6e6;

          &.center {
            text-align: center;
          }

          .typographic {
            display: flex;
            align-items: center;

            .image {
              width: 100px;
              height: 100px;
              margin-right: 10px;
            }

            .block-text {
              width: 250px;
            }

            .skuName {
              cursor: pointer;
              &:hover {
                color: #e4393c;
              }
            }

            .service {
              min-width: 80px;
              max-width: 250px;
              color: #666;
              text-align: center;
            }
            .cursorPointer {
              cursor: pointer;
            }
          }
        }
        .status {
          color: #1d2129;
          font-size: 12px;
          .complete {
            color: #86909C;
          }
        }
        .order {
          cursor: pointer;
          &:hover {
            color: #e4393c;
          }
        }
      }
    }
  }
}
</style>
