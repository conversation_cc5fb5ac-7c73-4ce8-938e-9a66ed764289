import request from '@/base/service'

const requestP = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_PAY_API
    }
  })
}

// 查询支付渠道列表
export function getChannelList(params) {
  return requestP({
    url: `/channel/get-enable-code-list`,
    method: 'get',
    params: params
  })
}

// 提交支付单
export function submitPayOrder(data) {
  return requestP({
    url: `/order/submit`,
    method: 'post',
    data: data
  })
}
