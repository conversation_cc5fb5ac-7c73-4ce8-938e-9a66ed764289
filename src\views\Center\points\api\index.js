import request from '@/base/service'
import qs from 'qs'

const requestBase = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_MEMBER_API
    }
  })
}


// 获取个人账户余额信息
export function getMyAccount(params) {
  return requestBase({
    url: '/user-account/get',
    method: 'get',
    params
  })
}

// 获取个人账户充值记录分页
export function getRechargePage(params) {
  return requestBase({
    url: '/user-account/recharge/page',
    method: 'get',
    params,
    paramsSerializer: params => qs.stringify(params, { arrayFormat: "repeat" })
  })
}

// 获取个人账户使用记录分页
export function getUsagePage(params) {
  return requestBase({
    url: '/user-account/usage/page',
    method: 'get',
    params,
    paramsSerializer: params => qs.stringify(params, { arrayFormat: "repeat" })
  })
}
