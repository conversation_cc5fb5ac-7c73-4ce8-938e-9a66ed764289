<template>
  <div class="footer-wrapper">
    <div class="bidding footer-item">
      <div class="title">
        <div class="title-text">竞价&询价公告</div>
        <div class="more">查看更多 ></div>
      </div>
      <div class="content-block">
        <t-tabs v-model="value" class="tabs-wrapper">
          <t-tab-panel value="first">
            <template #label>
              <span class="tabs-name">最新需求</span>
            </template>
          </t-tab-panel>
        </t-tabs>
        <div class="list">
          <div class="nothing-wrapper">
            <t-image class="nothing" fit="cover" :src="imgSrc"></t-image>
          </div>
        </div>
      </div>
    </div>
    <div class="information footer-item">
      <div class="title">
        <div class="title-text">行业资讯</div>
        <div class="more" @click="toInformation">查看更多 ></div>
      </div>
      <div class="content-block">
        <t-tabs v-model="value1" class="tabs-wrapper">
          <t-tab-panel value="first">
            <template #label>
              <span class="tabs-name">最新资讯</span>
            </template>
          </t-tab-panel>
        </t-tabs>
        <div class="list">
          <div v-for="item in latestList" :key="item.id" @click="toInfo(item)" class="notice-box">
            <div class="notice-content">
              <div class="dot"></div>
              <div class="overflow-text">{{ item.title }}</div>
            </div>
            <div class="notice-date">{{ item.publishTime | formatDate }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { queryContentList } from '@/views/Home/api'
export default {
  name: "HomeFooter",
  data() {
    return {
      value: "first",
      value1: "first",
      imgSrc: require("assets/nothing.png"),
      latestList: []
    };
  },
  created() {
    this.loadLatestList()
  },
  methods: {
    toInfo(item) {
      this.$router.push(`/information/content?cid=${item.id}`);
    },
    toInformation() {
      this.$router.push('/information');
    },
    async loadLatestList() {
      let params = {
        pageSize: 6,
        categoryType: 20
      }
      let res = await queryContentList(params)
      if(res.code === 0) {
        this.latestList = res.data.list || []
      } 
    },
  }
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.footer-wrapper {
  margin-top: 15px;
  width: 1200px;
  margin: 35px auto 0;
  height: 380px;
  display: flex;
  justify-content: space-between;
  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .title-text {
      color: #333;
      font-size: 20px;
      font-weight: 700;
    }
    .more {
      color: #999;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .footer-item {
    width: 590px;
    height: 380px;
    .content-block {
      width: 100%;
      height: 337px;
      background-color: #fff;
      .tabs-wrapper {
        margin-bottom: 16px;
      }
      .tabs-name {
        color: @primary-color;
        font-size: 16px;
      }
      .list {
        display: flex;
        flex-direction: column;
        height: calc(100% - 50px);
        .notice-box {
          font-size: 14px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 10px;
          margin-top: 12px;
          .notice-content {
            display: flex;
            align-items: center;
            height: 22px;
            line-height: 22px;
            cursor: pointer;
            .dot {
              background-color: @primary-color;
              width: 6px;
              height: 6px;
              border-radius: 50%;
              margin-right: 8px;
            }
            .overflow-text {
              max-width: 430px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
        .nothing-wrapper {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;

          .nothing {
            width: 360px;
            height: 200px;
            background-color: #fff;
          }
        }
      }
    }
  }
}
</style>
