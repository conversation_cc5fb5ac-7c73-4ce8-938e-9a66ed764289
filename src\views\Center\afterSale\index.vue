<template>
  <div class="after-sale-wrapper">
    <div class="title">
      <div class="title-progress"> 
        <t-steps :defaultCurrent="stepStatus" readonly>
  `       <t-step-item title="买家申请售后" :content="afterSaleInfo.createTime | formatDateTime"></t-step-item>
          <t-step-item title="卖家处理售后申请" :content="afterSaleInfo.auditTime | formatDateTime"></t-step-item>
          <t-step-item title="买家退货" :content="afterSaleInfo.deliveryTime | formatDateTime" v-if="afterSaleWay === 20"></t-step-item>
          <t-step-item title="售后完成" :content="afterSaleInfo.refundTime | formatDateTime"></t-step-item>
        </t-steps>`
      </div>
    </div>
    <div class="content-wrap">
      <div class="order-section">
        <div class="title">订单详情</div>
        <div class="product-info">
          <img style="width: 100px;height: 100px;" :src="orderItemData.picUrl" alt="" />
          <div class="name">{{orderItemData.skuName}}</div>
        </div>
        <div class="sp-line"></div>
        <div class="order-detail"> 
          <div class="detail-item">
            <div class="item-title">供应商：</div>
            <div class="item-value">{{orderItemData.supplierName}}</div>
          </div>
          <div class="detail-item">
            <div class="item-title">订单号：</div>
            <div class="item-value">{{orderNo}}</div>
          </div>
          <div class="detail-item">
            <div class="item-title">单价：</div>
            <div class="item-value">￥{{orderItemData.skuPrice | formatMoney}}</div>
          </div>
          <div class="detail-item">
            <div class="item-title">数量：</div>
            <div class="item-value">{{orderItemData.count}}</div>
          </div>
          <div class="detail-item">
            <div class="item-title">商品总价：</div>
            <div class="item-value">￥{{orderItemData.skuTotalPrice | formatMoney}}</div>
          </div>
        </div>
      </div>
      <div class="content">
        <after-sale-form ref="afterSaleForm" :orderItemData="orderItemData" v-if="showForm" @on-submit="handleSubmitSuccess" :way.sync="way"></after-sale-form>
        <after-sale-detail ref="afterSaleDetail" :orderItemData="orderItemData" v-if="showDetail" @on-update="handleSubmitSuccess" @on-copy="copy2Form"></after-sale-detail>
        <after-sale-delivery ref="afterSaleDetail" :orderItemData="orderItemData" @on-submit="handleSubmitSuccess" v-if="afterSaleStatus === 20"></after-sale-delivery>
      </div>
    </div>
  </div>
</template>

<script>
import { getOrderItemDetail } from '@/views/Center/afterSale/api'
import AfterSaleForm from './components/form'
import AfterSaleDetail from './components/detail'
import AfterSaleDelivery from './components/delivery'

export default {
  components: { AfterSaleForm, AfterSaleDetail, AfterSaleDelivery},
  data() {
    return {
      orderNo: '',
      way: 10,
      mode: 0,
      orderItemId: null,
      orderItemData: {},
    }
  },
  computed: {
    afterSaleInfo() {
      return this.orderItemData.afterSaleInfo || {}
    },
    afterSaleWay() {
      if(this.afterSaleInfo.id) {
        return this.afterSaleInfo.way
      } else {
        return this.way
      }
    },
    afterSaleStatus() {
      return this.afterSaleInfo.status || ''
    },
    showForm() {
      return !this.afterSaleInfo.id || this.mode !==0
    },
    showDetail() {
      return this.afterSaleInfo.id && this.mode ===0
    },
    stepStatus() {
      if(!this.afterSaleInfo.id) {
        return 0
      }
      if([10,62].includes(this.afterSaleStatus)) {
        return 1
      }
      if(this.afterSaleInfo.way === 20 && [20,30,63,40].includes(this.afterSaleStatus)) {
        return 2
      }
      if([50].includes(this.afterSaleStatus)) {
        return 3
      }

      return 0
    }
  },
  created() {
    this.orderNo = this.$route.query.no
    this.orderItemId = this.$route.query.itemId
    this.getOrderItemDetail()
  },
  methods: {
    async getOrderItemDetail() {
      const res = await getOrderItemDetail({
        orderNo: this.orderNo,
        orderItemId: this.orderItemId
      })
      if (res.code === 0) {
        this.orderItemData = res.data
      }
    },
    handleSubmitSuccess() {
      this.mode = 0
      this.getOrderItemDetail()
    },
    copy2Form(type) {
      if(type === 2) {
        this.mode = 1
        return
      }
      if([62,63].includes(this.afterSaleInfo.status)) {
        this.orderItemData.afterSaleInfo.id = undefined
        this.orderItemData.afterSaleInfo.createTime = undefined
        this.orderItemData.afterSaleInfo.auditTime = undefined
        this.orderItemData.afterSaleInfo.deliveryTime = undefined
        this.orderItemData.afterSaleInfo.refundTime = undefined
        this.$nextTick(() => {
          this.$refs.afterSaleForm.copy2Form(this.afterSaleInfo)
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.after-sale-wrapper {
  width: 83.33%;
  .title {
    width: 100%;
    text-align: center;
  }
  .title-progress {
    margin: 30px 20px;
  }
  .content-wrap {
    display: flex;
    // height: 440px;
    .order-section {
      width: 240px;
      border-right: 1px solid #ccc;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: start;
      padding: 0 12px;
      .name {
        margin-top: 12px;
      }
      .title {
        font-size: 1.1em;
        margin: 10px auto;
        padding: 10px;
        border-bottom: 1px solid #e8e8e8;
      }
      .product-info {
        margin: 10px auto;
        padding: 10px;
      }
      .sp-line {
        margin: 14px 0;
        overflow: hidden;
        height: 1px;
        width: 100%;
        line-height: 1px;
        font-size: 1px;
        background: #e8e8e8;
      }
      .order-detail {
        margin: 5px 0px;
        width: 100%;
        .detail-item {
          font-size: 0.9em;
          margin: 5px auto;
          .item-title {
            display: inline-block;
            width: 70px;
            margin-right: 5px;
            color: #9c9c9c;
            text-align: justify;
            text-align-last: justify;
          }
          .item-value {
            display: inline-block;
            margin-left: 5px;
          }
        }
      }
    }
    .content {
      flex: 1;
      padding-left: 12px;
      margin-bottom: 20px;
    }
    .footer-btn {
      margin-top: 20px;
      width: 150px;
    }
  }
  .image-pics {
    width: 48px;
    height: 48px;
    margin-right: 8px;
    object-fit: cover;
  }
  /deep/.t-upload__card-name {
    width: 108px;
  }
}
</style>
