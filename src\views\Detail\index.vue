<template>
  <div class="detail">
    <!-- 主要内容区域 -->
    <section class="con">
      <!-- 导航路径区域 -->
      <div class="conPoin" v-if="categoryNames.length">
        <span v-for="(name, index) in categoryNames" :key="name" @click="jumpCategory(index)"
          style="cursor: pointer;">{{ name }}</span>
      </div>
      <!-- 主要内容区域 -->
      <div class="mainCon" v-loading="loading">
        <!-- 左侧放大镜区域 -->
        <div class="previewWrap" v-if="detailData.skuId || detailData.skuInnerId">
          <!--放大镜效果-->
          <Zoom :sliderPicUrls="sliderPicUrls" />
          <!-- 小图列表 -->
          <ImageList :sliderPicUrls="sliderPicUrls" />
          <div style="display: flex;">
            <div class="collect" v-if="collectList[0] != detailData.skuId" @click="toCollect('addToCollect')">
              <heart-icon />
              <div class="collect-text">收藏</div>
            </div>
            <div class="collect" v-else @click="toCollect('cancelCollect')">
              <heart-filled-icon />
              <div class="collect-text">已收藏</div>
            </div>
            <div class="collect compare" @click="toCompare">
              <swap-icon />
              <div v-if="detailData.salePrice != -1" class="collect-text">对比</div>
              <div v-else class="collect-text">登录后对比</div>
            </div>
          </div>
        </div>
        <!-- 右侧选择区域布局 -->
        <Info v-if="detailData.skuId || detailData.skuInnerId" :detailData="detailData"
          :brotherSkuSpecs="brotherSkuSpecs"></Info>
      </div>
    </section>

    <!-- 内容详情页 -->
    <Product v-if="detailData.skuId || detailData.skuInnerId" :detailData="detailData" :suggestGoodsList="suggestGoodsList"></Product>
    <Compare v-show="showCompare" @compareHidden="compareHidden" ref="compare"></Compare>
  </div>
</template>

<script>
import ImageList from "./components/ImageList";
import Zoom from "./components/Zoom";
import Info from "./components/Info";
import Product from "./components/Product";
import Compare from './components/Compare'
import { HeartIcon, HeartFilledIcon, SwapIcon } from "tdesign-icons-vue";
import { getSkuDetailInfo, getBrotherSkus, getSkuStockInfo4JD, getSkuSuggestList, addToCollect, cancelCollect, queryCollectStatus } from '@/views/Detail/api'
import { getCategoryNamePath } from '@/views/Home/api'
import { mapState } from 'vuex'
import { toLogin } from "@/utils/util";

export default {
  name: "Detail",
  data() {
    return {
      detailData: {},
      brotherSkuSpecs: [],
      stockInfo: {},
      categoryNames: [],
      collectList: [],
      skuId: null,
      supplierId: null,
      loading: false,
      showCompare: JSON.parse(localStorage.getItem('showCompare')),
      sliderPicUrls: [],
      suggestGoodsList: [],
    };
  },
  components: {
    HeartIcon,
    HeartFilledIcon,
    SwapIcon,
    Info,
    Product,
    ImageList,
    Compare,
    Zoom,
  },
  computed: {
    ...mapState({
      defaultRegions: state => state.defaultAddress.defaultRegions
    }),
    // sliderPicUrls() {
    //   return this.detailData.sliderPicUrls || [];
    // }
  },
  methods: {
    async getCategoryData() {
      let ids = this.detailData.category.split(';')
      if (!ids.length) {
        return
      }
      let res = await getCategoryNamePath({ ids: ids });
      if (res.code === 0) {
        this.categoryNames = res.data.split("/") || []
      }
    },
    jumpCategory(index) {
      let ids = this.detailData.category.split(';')
      if (!ids.length) {
        return
      }
      let id = ids[index]
      if (parseInt(id) < 5000000) {
        return;
      }
      let name = 'categoryId' + (index + 1)
      this.$router.push({
        path: `/storeSearch?${name}=${id}`,
      })
    },
    async init() {
      this.skuId = this.$route.params.skuId
      if(isNaN(this.skuId)) {
        this.$message.error('商品SKU参数异常，请联系运营')
        return
      }

      this.loading = true
      await Promise.all([
        getSkuDetailInfo({
          skuId: this.skuId,
          queryExtSet: 2 // PC端
        }),
        getSkuStockInfo4JD({
          skuId: this.skuId
        })
      ]).then((res) => {
        if (res[0].code === 0 && res[1].code === 0) {
          // 商品详情
          this.detailData = res[0].data
          // 商品轮播图和价格
          let stockInfo = res[1].data
          if (stockInfo) {
            Object.assign(this.detailData, stockInfo)
          }
          this.stockInfo = stockInfo
          if (!this.detailData.isJD) {
            if (this.sliderPicUrls.length == 0) {
              this.sliderPicUrls = this.detailData.sliderPicUrls
            }
          } else {
            this.sliderPicUrls = this.detailData.sliderPicUrls
          }
        }
        this.loading = false
        this.loadBrotherSkuSpec()
        this.getCategoryData()
        this.querySkuSuggestList()
        this.queryCollectStatus([this.detailData.skuId])
      })
    },
    async loadBrotherSkuSpec() {
      let res = await getBrotherSkus({
        skuId: this.skuId,
        supplierId: this.supplierId,
      })
      //同规格sku
      this.brotherSkuSpecs = res.data
      if (!this.detailData.isJD) {
        this.brotherSkuSpecs.forEach(item => {
          item.specValues.forEach(value => {
            if (value.imagePath != null
              && value.skuIdList.includes(this.detailData.skuId)
              && !this.sliderPicUrls.includes(value.imagePath)) {
              this.sliderPicUrls.unshift(value.imagePath)
            }
          })
        })
      }
    },
    async querySkuSuggestList() {
      let ids = this.detailData.category.split(';')
      let param = {
        skuId: this.$route.params.skuId,
        supplierId: this.$route.params.supplierId,
        queryExtSet: 2, // PC端
        categoryId1: ids[0] || '',
        categoryId2: ids[1] || '',
        categoryId3: ids[2] || '',
        areaIds: this.defaultRegions.join(',')
      }
      let res = await getSkuSuggestList(param)
      if (res.code === 0) {
        var result = res.data || []
        if (result && result.length > 0) {
          this.suggestGoodsList = result.slice(0, 5)
        }
      }
    },
    async queryCollectStatus(skuIds) {
      const res = await queryCollectStatus({ skuIds })
      if (res.code === 0) {
        this.collectList = res.data
      }
    },
    async toCollect(method) {
      const item = this.detailData
      let res = null
      if (method === 'addToCollect') {
        res = await addToCollect({
          skuId: item.skuId,
          supplierId: item.supplierId
        })
      } else {
        res = await cancelCollect({
          skuId: item.skuId,
          supplierId: item.supplierId
        })
      }
      if (res.code === 0) {
        this.$message.success('操作成功')
        this.queryCollectStatus([this.detailData.skuId])
      } else {
        this.$message.error('操作失败')
      }
    },
    toCompare() {
      if (this.detailData.salePrice === -1) {
        toLogin()
        return
      }
      localStorage.setItem('showCompare', true)
      this.showCompare = true
      this.$nextTick(() => {
        this.$refs.compare.addItem({
          skuName: this.detailData.skuName || '',
          skuId: this.detailData.skuId,
          salePrice: this.detailData.salePrice || '',
          imageUrl: this.detailData.imagePath || '',
          supplierId: this.supplierId || '',
          sliderPicUrls: this.detailData.sliderPicUrls || []
        })
      })
    },
    compareHidden() {
      this.showCompare = false
    }
  },
  created() {
    this.init()
  },
  watch: {
    $route: {
      handler(newVal, oldVal) {
        this.init()
      },
    },
  }
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.detail {
  position: relative;
  padding-bottom: 24px;
  background-color: #fff;
  border-bottom: 1px solid #E9EBF2;

  .con {
    width: 1200px;
    margin: 15px auto 0;

    .conPoin {
      padding: 9px 15px 9px 0;

      &>span+span:before {
        content: "/\00a0";
        padding: 0 5px;
        color: #ccc;
      }
    }

    .mainCon {
      overflow: hidden;
      margin: 5px 0 0;
      display: flex;
      min-width: 1000px;
      min-height: 400px;

      .previewWrap {
        width: 400px;
        position: relative;

        .collect {
          max-width: 120px;
          cursor: pointer;
          display: flex;
          font-size: 20px;
          color: #e11b10;
          margin-top: 10px;
          align-items: center;

          .collect-text {
            color: #333;
            margin-left: 4px;
            font-size: 14px;

            &:hover {
              color: #e11b10;
            }
          }
        }

        .compare {
          margin-left: 8px;
        }
      }
    }
  }
}
</style>
