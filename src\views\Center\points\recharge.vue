<template>
  <div class="recharge-wrapper">
    <t-form :data="queryForm" ref="queryForm" layout="inline" @reset="onReset" @submit="search">
      <t-form-item label="充值时间">
        <t-date-range-picker v-model="queryForm.rechargeTime" mode="date" style="width: 300px;" clearable/>
      </t-form-item>
      <t-form-item label="到期时间">
        <t-date-picker v-model="queryForm.expireDate" mode="date" style="width: 160px;" clearable/>
      </t-form-item>
      <t-form-item>
        <t-space size="10px">
          <t-button theme="primary" type="submit">查询</t-button>
          <t-button theme="default" variant="base" type="reset">重置</t-button>
        </t-space>
      </t-form-item>
    </t-form>
    <t-table class="table-wrap" hover v-loading="tableLoading" ref="table" bordered row-key="index" :data="tableData" :columns="columns">
      <template #scoreType="{ row }">
        <span>{{ row.scoreType == 0 ? '福利积分' : '扶贫积分' }}</span>
      </template>
      <template #rechargeTime="{ row }">
        <span>{{ row.rechargeTime | formatDate }}</span>
      </template>
      <template #effectiveDate="{ row }">
        <span>{{ row.effectiveDate | formatDate }}</span>
      </template>
      <template #expireDate="{ row }">
        <span>{{ row.expireDate | formatDate }}</span>
      </template>
      <template #rechargeAmount="{ row }">
        <span>{{ row.rechargeAmount| formatMoney(2) }}</span>
      </template>
      <template #useAmount="{ row }">
        <span>{{ row.rechargeAmount - row.remainAmount | formatMoney(2) }}</span>
      </template>
      <template #remainAmount="{ row }">
        <span>{{ row.remainAmount | formatMoney(2) }}</span>
      </template>
      <template #operation="{ row }">
        <t-link theme="primary" hover="color" @click="clickHandler(row)">
          积分消费列表
        </t-link>
      </template>
    </t-table>
    <div>
      <t-pagination class="pagination" :total="total" :showPageNumber="true" :showPageSize="false"
        showPreviousAndNextBtn :totalContent="false" @current-change="changePage" />
    </div>
  </div>
</template>

<script>
import * as api from '@/views/Center/points/api'
export default {
  data() {
    return {
      tableLoading: false,
      queryForm: {
        pageNo: 1,
        pageSize: 10,
        rechargeTime: [],
        expireDate: undefined
      },
      total: 0,
      tableData: [],
      columns: [
        { colKey: 'serial-number', width: 60, title: '序号' },
        { colKey: 'scoreType', title: '积分类型', width: 90 },
        { colKey: 'rechargeAmount', title: '充值积分', width: 100 },
        { colKey: 'useAmount', title: '使用积分', width: 100 },
        { colKey: 'remainAmount', title: '剩余积分', width: 100 },
        { colKey: 'rechargeTime', title: '充值时间', width: 120 },
        { colKey: 'effectiveDate', title: '开始时间', width: 120 },
        { colKey: 'expireDate', title: '到期时间', width: 120 },
        { colKey: 'operation', title: '操作', width: 120, fixed: 'right' },
      ],
    }
  },
  created() {
    this.search()
  },
  methods: {
    search() {
      this.queryForm.pageNo = 1
      this.loadList()
    },
    changePage(page) {
      this.queryForm.pageNo = page
      this.loadList()
    },
    loadList() {
      let params = Object.assign({}, this.queryForm)
      let rechargeTime = params.rechargeTime
      if(rechargeTime && rechargeTime.length) {
        params.rechargeTime = [rechargeTime[0] + ' 00:00:00', rechargeTime[1] + ' 23:59:59']
      }
      if(params.expireDate) {
        params.expireDate = params.expireDate + ' 23:59:59'
      } else {
        delete params.expireDate
      }
      api.getRechargePage(params).then(res => {
        if(res.code === 0) {
          this.total = res.data.total
          this.tableData = res.data.list || []
        }
      })
    },
    onReset() {
      this.queryForm = {
        date: [],
        delayDate: ''
      }
    },
    clickHandler(row) {
      this.$emit('jumpTo', {
        row,
        status: 2
      })
    }
  }
}
</script>

<style lang="less" scoped>
.recharge-wrapper {
  .table-wrap {
    margin-top: 24px;
    margin-bottom: 12px;
  }
}
</style>
