<template>
  <div class="consumpotion-wrapper">
    <t-form :data="queryForm" ref="queryForm" layout="inline" @reset="onReset" @submit="search">
      <t-form-item label="订单编号">
        <t-input v-model="queryForm.orderNo" placeholder="请输入订单编号" style="width: 160px;" clearable/>
      </t-form-item>
      <t-form-item label="退款时间">
        <t-date-range-picker v-model="queryForm.createTime" mode="date" style="width: 300px;" clearable/>
      </t-form-item>
      <t-form-item>
        <t-space size="10px">
          <t-button theme="primary" type="submit">查询</t-button>
          <t-button theme="default" variant="base" type="reset">重置</t-button>
        </t-space>
      </t-form-item>
    </t-form>
    <t-table class="table-wrap" hover v-loading="tableLoading" ref="table" bordered row-key="index" :data="tableData" :columns="columns">
      <template #scoreType="{ row }">
        <span>{{ row.scoreType == 0 ? '福利积分' : '扶贫积分' }}</span>
      </template>
      <template #useAmount="{ row }">
        <span>{{ row.useAmount | formatMoney(2) }}</span>
      </template>
      <template #useTime="{ row }">
        <span>{{ row.useTime | formatDateTime }}</span>
      </template>
    </t-table>
    <div>
      <t-pagination class="pagination" :total="total" :showPageNumber="true" :showPageSize="false"
        showPreviousAndNextBtn :totalContent="false" @current-change="changePage" />
    </div>
  </div>
</template>

<script>
import * as api from '@/views/Center/points/api'
export default {
  data() {
    return {
      tableLoading: false,
      queryForm: {
        pageNo: 1,
        pageSize: 10,
        createTime: [],
        orderNo: ''
      },
      total: 0,
      tableData: [],
      columns: [
        { colKey: 'serial-number', width: 80, title: '序号' },
        { colKey: 'orderNo', title: '订单编号', width: 400 },
        { colKey: 'scoreType', title: '积分类型' },
        { colKey: 'useAmount', title: '退款积分' },
        { colKey: 'useTime', title: '退款时间', width: 200 }
      ],
    }
  },
  methods: {
    search() {
      this.queryForm.pageNo = 1
      this.loadList()
    },
    changePage(page) {
      this.queryForm.pageNo = page
      this.loadList()
    },
    loadList() {
      let params = this.queryForm
      params.useType = 2
      let createTime = params.createTime
      if(createTime && createTime.length) {
        params.createTime = [createTime[0] + ' 00:00:00', createTime[1] + ' 23:59:59']
      }
      if(!params.orderNo) {
        delete params.orderNo
      } 
      api.getUsagePage(params).then(res => {
        if(res.code === 0) {
          this.total = res.data.total
          this.tableData = res.data.list || []
        }
      })
    },
    onReset() {
      this.queryForm = {
        createTime: [],
        orderNo: ''
      }
    },
    init(row) {
      if(row) this.queryForm.orderNo = row.orderNo
      this.search()
    }
  }
}
</script>

<style lang="less" scoped>
.consumpotion-wrapper {
  .table-wrap {
    margin-top: 24px;
    margin-bottom: 12px;
  }
}
</style>
