<template>
  <t-dialog attach="body" top="10vh" :visible.sync="visible" width="700px" header="心愿单详情" :onCancel="close" :closeOnOverlayClick="false"
    :onClose="close" :footer="false" :destroyOnClose="true">
    <t-form :data="formData" ref="form">
      <t-form-item label="品目" name="categoryIds">
        {{ inputProps.value || '--' }}
      </t-form-item>
      <t-form-item label="品牌" name="brand">
        {{ formData.brand || '--' }}
      </t-form-item>
      <t-form-item label="型号" name="model">
        {{ formData.model || '--' }}
      </t-form-item>
      <t-form-item label="数量" name="quantity">
        {{ formData.quantity }}
      </t-form-item>
      <t-form-item label="下单时间" name="orderTime">
        {{ formData.orderTime ? dayjs(formData.orderTime).format('YYYY-MM-DD') : '--' }}
      </t-form-item>
      <t-form-item label="产品链接" name="productLink">
        <t-link :href="formData.productLink" target="_blank" v-if="formData.productLink ">{{ formData.productLink }}</t-link>
        <span v-else>--</span>
      </t-form-item>
      <t-form-item label="参数描述" name="productMemo">
        {{ formData.productMemo  || '--'}}
      </t-form-item>
      <t-form-item label="联系人" name="contact">
        {{ formData.contact  || '--'}}
      </t-form-item>
      <t-form-item label="联系电话" name="phone">
        {{ formData.phone || '--' }}
      </t-form-item>
      <t-form-item label="回复内容" name="replyContent">
        {{ formData.replyContent || '--' }}
       </t-form-item>
    </t-form>
  </t-dialog>
</template>
  
<script>
import dayjs from 'dayjs';

const INITIAL_DATA = {
  categoryIds: [],
  categoryNames: '',
  brand: '',
  model: '',
  quantity: 1,
  orderTime: '',
  productLink: '',
  productMemo: '',
  contact: '',
  phone: ''
};

export default {
  name: 'detail-wish-list',
  data() {
    return {
      visible: true,
      formData: { ...INITIAL_DATA },
      categoryList1: [],
      categoryList2: [],
      wishOptions: [],
      inputProps: {
        value: '',
      },
      disabledDate: {
        before: dayjs().subtract(1, 'day').format()
      },
      dayjs
    }
  },
  props: {
    editData: {
      type: Object,
      default: () => {}
    }
  },
  created() {
    this.formData = {
      ...this.editData,
      categoryIds: this.editData.categoryIds.split(',')
    }
    this.inputProps.value = this.editData.categoryNames.replace(/,/g, ' / ')
  },
  methods: {
    close() {
      this.$emit('closeDialog')
    }
  }
}
</script>
  
<style lang="less" scoped>
</style>
  