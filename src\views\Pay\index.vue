<template>
  <div class="pay-main">
    <div class="pay-container">
      <div class="checkout-tit">
        <h4 class="tit-txt">
          <span class="success-icon"></span>
          <span class="success-info">订单提交成功，请您及时付款，以便尽快为您发货~~</span>
        </h4>
        <div class="paymark">
          <div class="left">
            <div>请您在提交订单</div>
            <!-- <div class="orange time">2小时</div> -->
            <div>后尽快完成支付，超时订单会自动取消。订单号：</div>
            <div>{{ orderInfo.no }}</div>
          </div>
          <div class="right">
            <div class="lead">应付金额：</div><div class="orange money">￥{{ orderInfo.payPrice || 0 | formatMoney }}</div>
          </div>
        </div>
      </div>
      <div class="checkout-info">
        <h4>重要说明：</h4>
        <ol>
          <li>商城支付平台目前支持以下支付方式。</li>
          <li>其它支付渠道正在调试中，敬请期待。</li>
        </ol>
      </div>
      <div class="checkout-steps" v-if="inited">
        <div class="step-tit">
          <h4>支付平台</h4>
        </div>
        <div class="step-cont">
          <ul class="payType">
            <li v-for="ch in channelList" :key="ch" :class="channelCode === ch ? 'selected' : ''" @click="channelCode = ch"><img :src="getChannelImg(ch)" height="60"></li>
          </ul>
        </div>
        <div class="hr"></div>
        <div class="submit">
          <a class="btn" @click="open" v-if="payValid">立即支付</a>
          <a class="btn" @click="goBack" v-if="!payValid">支付无效</a>
        </div>
      </div>
    </div>

    <t-dialog
      :visible="tipVisible"
      :closeBtn="false"
      header="支付等待提示"
      :confirmBtn="{
        content: '已完成支付',
        variant: 'base',
      }"
      :cancelBtn="{
        content: '离开',
        variant: 'outline',
      }"
      @confirm="onConfirm"
      @cancel="onCancel"
    >
    <div slot="body" class="flex-center">
      <t-loading v-if="statusProgress === 0" text="支付中..." size="small"></t-loading>
      <t-space v-if="statusProgress > 0" direction="vertical" align="center" :size="10">
        <div v-if="statusProgress === 100">支付成功</div>
        <t-progress theme="circle" :percentage="statusProgress" :status="'success'"></t-progress>
      </t-space>
    </div>
  </t-dialog>

  </div>
</template>

<script>
import QRCode from 'qrcode'
import { getDetailOrder, getPayOrder, checkOrderPayed } from '@/views/Trade/api'
import * as api from '@/views/Pay/api'
export default {
  name: 'Pay',
  data() {
    return {
      inited: false,
      tipVisible: false,
      statusProgress: 0,
      orderInfo: {},
      payInfo: {},
      channelList: [],
      channelCode: '',
      timer: null,
      code: '',
      payValid: false
    }
  },
  computed: {
    orderNo() {
      return this.$route.query.no
    },
    oType() {
      return this.$route.query.oType
    }
  },
  created() {
    this.getOrderInfo()
    this.getPayInfo()
  },
  methods: {
    async getOrderInfo() {
      if(!this.orderNo) {
        this.$message.error('必选参数为空')
        return
      }
      let res = await getDetailOrder({orderNo: this.orderNo})
      if(res.code === 0) {
        this.orderInfo = res.data
        this.payValid = this.validatePayable()
      }
    },
    async getPayInfo() {
      if(!this.orderNo) {
        this.$message.error('必选参数为空')
        return
      }
      let res = await getPayOrder({orderNo: this.orderNo})
      if(res.code === 0) {
        this.payInfo = res.data
        this.getChannelList()
      }
    },
    async getChannelList() {
      if(!this.payInfo.appCode) {
        this.$message.error('必选参数为空')
        return
      }
      let res = await api.getChannelList({appCode: this.payInfo.appCode})
      this.inited = true
      if(res.code === 0) {
        this.channelList = res.data
      }
    },
    getChannelImg(channel) {
      return require(`./images/pay_${channel}.png`)
    },
    validatePayable() {
      if(![5,7].includes(this.orderInfo.paymentMethod)) {
        this.$message.error('订单不支持此支付方式')
        return false
      }
      if(this.orderInfo.payed) {
        this.$message.error('订单已经支付完成')
        return false
      }
      if(!this.orderInfo.payPrice) {
        this.$message.error('订单待支付金额为0')
        return false
      }

      return true
    },
    goBack() {
      window.history.go(-1)
    },
    async open() {
      if(!this.channelCode) {
        this.$message.error('请选择支付方式')
        return false
      }
      // 转到支付中间页
      let ctxPath = process.env.VUE_APP_PATH_CONTEXT
      let uri = `/#/paybridge?payOrderId=${this.payInfo.payOrderId}&channelCode=${this.channelCode}&orderNo=${this.orderNo}&payPrice=${this.orderInfo.payPrice}&oType=${this.oType}`
      let url = location.origin + ctxPath + uri
      window.open(url, '_blank')
      this.tipVisible = true
      this.intervalCheckPayed()
    },
    intervalCheckPayed() {
      let func = () => {
        if(!this.orderNo) {
          return
        }
        checkOrderPayed({orderNo: this.orderNo}).then(res => {
          if(!res.data) {
            setTimeout(func, 2000)
          } else {
            this.jumpSuccess()
          }
        })
      }
      setTimeout(func, 5000)
    },
    jumpSuccess() {
      let animateProgress = () => {
        this.statusProgress += 10
        if(this.statusProgress < 100) {
          setTimeout(animateProgress, 100)
        } else {
          setTimeout(() => {
            this.tipVisible = false
            this.$router.push({
              path: '/paysuccess',
              query: {
                no: this.orderNo,
                oType: this.oType
              }
            })
          }, 1000)
        }
      }
      setTimeout(animateProgress, 100)
    },
    async onConfirm() {
      let res = await checkOrderPayed({orderNo: this.orderNo})
      if(res.data) {
        this.jumpSuccess()
      } else {
        this.$message.info('订单支付处理中，请稍后')
        this.$router.push('/center/myorder')
      }
    },
    onCancel() {
      this.tipVisible = false
      this.$router.push('/')
    }
  }
}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.pay-main {
  margin-bottom: 20px;
  .pay-container {
    margin: 0 auto;
    width: 1200px;
    a:hover {
      color: #4cb9fc;
    }
    .orange {
      color: #e1251b;
    }
    .money {
      font-size: 18px;
    }
    .zfb {
      color: #e1251b;
      font-weight: 700;
    }
    .checkout-tit {
      padding: 10px;
      .tit-txt {
        font-size: 14px;
        line-height: 21px;

        .success-icon {
          width: 30px;
          height: 30px;
          display: inline-block;
          background: url(./images/icon.png) no-repeat 0 0;
        }
        .success-info {
          padding: 0 8px;
          line-height: 30px;
          vertical-align: top;
        }
      }
      .paymark {
        overflow: hidden;
        line-height: 26px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left, .right {
          display: flex;
          align-items: center;
        }
      }
    }

    .checkout-info {
      padding-left: 25px;
      padding-bottom: 15px;
      margin-bottom: 10px;
      border: 2px solid #e1251b;

      h4 {
        margin: 9px 0;
        font-size: 14px;
        line-height: 21px;
        color: #e1251b;
      }
      ol {
        padding-left: 25px;
        list-style-type: decimal;
        line-height: 24px;
        font-size: 14px;
      }
      ul {
        padding-left: 25px;
        list-style-type: disc;
        line-height: 24px;
        font-size: 14px;
      }
    }

    .checkout-steps {
      border: 1px solid #ddd;
      padding: 25px;

      .hr {
        height: 1px;
        background-color: #ddd;
      }

      .step-tit {
        line-height: 36px;
        margin: 15px 0;
      }

      .step-cont {
        margin: 0 10px 12px 20px;
        ul {
          font-size: 0;

          li {
            margin: 10px;
            display: inline-block;
            padding: 5px 20px;
            border: 1px solid #ddd;
            cursor: pointer;
          }
          .selected {
            border: 2px solid #de0b0bad;
          }
        }
      }
    }

    .submit {
      text-align: center;
      .btn {
        display: inline-block;
        padding: 15px 45px;
        margin: 15px 0 10px;
        font: 18px "微软雅黑";
        font-weight: 700;
        border-radius: 0;
        background-color: @primary-color;
        border: 1px solid @primary-color;
        color: #fff;
        text-align: center;
        vertical-align: middle;
        cursor: pointer;
        user-select: none;
        text-decoration: none;
      }
    }
  }
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>