<template>
  <div>
    <div v-if="configData.footerSwitch && comName">
      <keep-alive>
        <component :is="comName"></component>
      </keep-alive>
    </div>
  </div>
</template>

<script>
import FooterJcy from './footer-jcy'
import FooterWhu from './footer-whu'
import FooterScuec from './footer-scuec'

export default {
  components: {
    FooterJcy, FooterWhu, FooterScuec
  },
  computed: {
    configData() {
      return this.$store.state.Home.homeConfigData || {};
    },
    comName() {
      let footerCode = this.configData.footerCode
      if (footerCode === 'jcy') {
        return 'FooterJcy'
      }
      if (footerCode === 'whu') {
        return 'FooterWhu'
      }
      if (footerCode === 'scuec') {
        return 'FooterScuec'
      }

      return ''
    }
  }
};
</script>
