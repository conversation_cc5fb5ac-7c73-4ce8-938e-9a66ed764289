<template>
  <header class="header">
    <div class="container">
      <div class="loginList">
        <p>欢迎来到{{title}}！</p>
        <p v-if="!nickname">
          <span>请</span>
          <span class="register" @click="login">登录</span>
          <!-- <router-link to="/login">登录</router-link> -->
          <!-- /
          <router-link to="register" class="register">免费注册</router-link> -->
        </p>
        <div v-else>
          <span>{{ nickname }}</span>
          <span class="register" @click="logOut">退出登录</span>
        </div>
      </div>
      <div class="typeList">
        <div @click="clickItem(1)" class="item">
          <icon name="home" />
          首页
        </div>
        <div @click="clickItem(2)" class="item" v-if="false">
          <icon name="desktop" />
          工作台
        </div>
        <div @click="clickItem(21)" class="item">
          <icon name="mobile" />
          移动端
        </div>
        <div @click="clickItem(3)" class="item">
          <icon name="view-module" />
          我的订单
        </div>
        <div @click="clickItem(4)" class="item" v-if="false">
          <icon name="notification" />
          消息
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { Icon } from 'tdesign-icons-vue';
import { toLogin, toLogout } from '@/utils/util'

export default {
  name: "Header",
  data() {
    return {};
  },
  components: {
    Icon
  },
  methods: {
    logOut() {
      this.$store.commit('SetCartCount', 0)
      toLogout()
    },
    login() {
      toLogin()
    },
    clickItem(type) {
      let ctxPath = process.env.VUE_APP_PATH_CONTEXT
      if (type === 1) {
        this.$router.push('/home')
      } else if (type === 2) {
        window.open(location.origin + ctxPath + '/#/center/myorder', '_blank')
      } else if (type === 3) {
        window.open(location.origin + ctxPath + '/#/center/myorder', '_blank')
      } else if (type === 4) {
        this.$message.warning("功能还在开发中，敬请期待");
      } else if (type === 21) {
        window.open(location.origin + ctxPath + '/h5', '_blank')
      } 
    }
  },
  computed: {
    nickname() {
      const userInfo = this.$store.state.User.userInfo
      if (userInfo) {
        return userInfo.nickname
      }
      return ''
    },
    title() {
      return this.$store.state.Home.configData.title || '金采通'
    }
  }
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.header {
  width: 100%;
  height: 40px;
  background-color: #fff;;
  border-bottom: 1px solid #999999;
  .container {
    width: 1200px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;
    font-size: 14px;
    .loginList {
      display: flex;
      align-items: center;
      .register {
        cursor: pointer;
        margin-left: 4px;
        color: var(--td-brand-color-7);
        background-color: #fff;
        max-width: 70px;
        padding: 0 4px;
        display: inline-block;
      }
    }
    .typeList {
      display: flex;
      align-items: center;
      .item {
        margin-left: 24px;
        height: 40px;
        line-height: 40px;
        cursor: pointer;
      }
    }
  }

}

:root[theme-mode='110'] {
  .header {
    background-color: var(--td-brand-color-7);
    color: #fff;
    border-bottom: none;
  }
}
</style>
