import request from './request'

export const reqGetSearchInfo = (params) => {
  return request({
    method: 'post',
    url: '/list',
    data: params
  })
}

export const reqGoodsInfo = (skuId) => {
  return request({
    method: 'get',
    url: `/item/${skuId}`,
  })
}

export const reqAddOrUpdateShopCart = (skuId,skuNum) => {
  return request({
    method: 'post',
    url: `/cart/addToCart/${skuId}/${skuNum}`,
  })
}

export const reqCartList = () => {
  return request({
    method: 'get',
    url:`/cart/cartList`
  })
}

export const reqDeleteCartById = (skuId) => {
   return request({
    method: 'delete',
    url:`/cart/deleteCart/${skuId}`
  })
}

export const reqUpdateCheckedById = ({ skuId, isChecked }) => {
  return request({
    method: 'get',
    url: `/cart/checkCart/${skuId}/${isChecked}`
  })
}

export const reqGetCode = (phone) => {
  return request({
    method: 'get',
    url: `/user/passport/sendCode/${phone}`
  })
}

export const reqUserRegister = (data) => {
  return request({
    method: 'post',
    url: '/user/passport/register',
    data
  })
}

export const reqUserLogOut = () => {
  return request({
    method: 'get',
    url: '/user/passport/logout'
  })
}

export const reqAddressInfo = () => {
  return request({
    method: 'get',
    url: '/user/userAddress/auth/findUserAddressList'
  })
}

export const reqOderInfo = () => {
  return request ({
    method: 'get',
    url: '/order/auth/trade'
  })
}

export const reqSubmitOrder = (tradeNo, data) => {
  return request({
    method: 'post',
    url: `/order/auth/submitOrder?tradeNo=${tradeNo}`,
    data
  })
}

export const reqPayInfo = (orderId) => {
  return request({
    method:'get',
    url: `/payment/weixin/createNative/${orderId}`
  })
}

export const reqPayStatus = (orderId) => {
  return request({
    method: 'get',
    url: `/payment/weixin/queryPayStatus/${orderId}`
  })
}

export const reqOrderList = ({page, limit}) => {
  return request({
    method: 'get',
    url: `/order/auth/${page}/${limit}`
  })
}