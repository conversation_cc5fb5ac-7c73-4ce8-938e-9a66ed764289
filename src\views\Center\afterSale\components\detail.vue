<template>
  <div class="after-sale-detail">
    <div class="as-section"> 
      <div class="title">
        <div>售后详情</div>
        <div v-if="orderItemData.supplierType !== 1"> 
          <t-popconfirm  content="确认取消吗" :onConfirm="cancelApply">
            <t-button v-if="[10,20].includes(afterSaleInfo.status) && orderItemData.orderStatus !== 9" class="footer-btn" theme="default">取消申请</t-button> 
          </t-popconfirm>
          <t-button v-if="[62,63].includes(afterSaleInfo.status) && orderItemData.orderStatus !== 9" class="footer-btn" theme="default" @click="cotinueApply(1)">继续申请</t-button>
          <t-button v-if="[50].includes(afterSaleInfo.status) && orderItemData.canAfterSale" class="footer-btn" theme="default" @click="cotinueApply(2)">继续申请</t-button> 
        </div>
      </div>
      <div class="as-detail"> 
        <div class="as-detail-item">
          <div class="item-title">售后商品：</div>
          <div class="item-value">{{ afterSaleInfo.spuName }}</div>
        </div>
        <div class="as-detail-item">
          <div class="item-title">售后方式：</div>
          <div class="item-value">{{ afterSaleInfo.way | afterSaleWayInfo }}</div>
        </div> 
        <div class="as-detail-item">
          <div class="item-title">联系人：</div>
          <div class="item-value">{{ afterSaleInfo.contact }}</div>
        </div> 
        <div class="as-detail-item">
          <div class="item-title">电话：</div>
          <div class="item-value">{{ afterSaleInfo.phone }}</div>
        </div> 
        <div class="as-detail-item">
          <div class="item-title">售后原因：</div>
          <div class="item-value">{{ afterSaleInfo.applyReason | afterSaleReasonInfo }}</div>
        </div>
        <div class="as-detail-item">
          <div class="item-title">售后商品数量：</div>
          <div class="item-value">{{ afterSaleInfo.count }}</div>
        </div>
        <div class="as-detail-item">
          <div class="item-title">退款金额：</div>
          <div class="item-value">￥{{ afterSaleInfo.refundPrice | formatMoney }}</div>
        </div>
        <div class="as-detail-item">
          <div class="item-title">售后说明：</div>
          <div class="item-value">{{ afterSaleInfo.applyDescription }}</div>
        </div>
        <div class="as-detail-item">
          <div class="item-title">补充图片：</div>
          <div class="item-value">
            <t-image-viewer v-for="(img, index) in afterSaleInfo.applyPicUrls" :key="img" :default-index="index" :images="afterSaleInfo.applyPicUrls">
              <template #trigger="{ open }">
                <div class="tdesign-demo-image-viewer__ui-image tdesign-demo-image-viewer__base">
                  <img alt="test" :src="img" class="tdesign-demo-image-viewer__ui-image--img" />
                  <div class="tdesign-demo-image-viewer__ui-image--hover" @click="open">
                    <span><browse-icon size="1.4em" /> 预览</span>
                  </div>
                </div>
              </template>
            </t-image-viewer>
          </div>
        </div>

        <template v-if="afterSaleInfo.logisticsName">
        <div class="as-detail-item">
          <div class="item-title">物流公司：</div>
          <div class="item-value">{{ afterSaleInfo.logisticsName }}</div>
        </div>
        <div class="as-detail-item">
          <div class="item-title">物流单号：</div>
          <div class="item-value">{{ afterSaleInfo.logisticsNo }}</div>
        </div>
        <div class="as-detail-item">
          <div class="item-title">发货时间：</div>
          <div class="item-value">{{ afterSaleInfo.deliveryTime | formatDate }}</div>
        </div>
        </template>

        <div class="status-memo">
          <t-alert v-if="afterSaleInfo.status === 62" theme="warning" title="卖家拒绝了您的售后申请" :message="`拒绝原因：${afterSaleInfo.auditReason}`"></t-alert>
          <t-alert v-if="afterSaleInfo.status === 63" theme="warning" title="卖家拒绝收货" :message="`拒绝原因：${afterSaleInfo.receiveReason}`"></t-alert>
          <t-alert v-if="afterSaleInfo.status === 10" theme="info" title="售后状态" :message="`等待卖家审核`"></t-alert>
          <t-alert v-if="afterSaleInfo.status === 30" theme="info" title="售后状态" :message="`等待卖家收货`"></t-alert>
          <t-alert v-if="afterSaleInfo.status === 40" theme="info" title="售后状态" :message="`等待平台退款`"></t-alert>
          <t-alert v-if="afterSaleInfo.status === 50" theme="success" title="售后状态" :message="`已经完成`"></t-alert>
        </div>

      </div>
    </div>

    
  </div>
</template>

<script>
import { cancelAfterSale } from '@/views/Center/afterSale/api'
import { BrowseIcon } from 'tdesign-icons-vue';
export default {
  name: "AfterSaleDetail",
  components: {
    BrowseIcon,
  },
  props: {
    orderItemData: {
      required: true,
      default: () => {}
    }
  },
  data() {
    return {

    }
  },
  computed: {
    afterSaleInfo() {
      return this.orderItemData.afterSaleInfo || {}
    }
  },
  methods: {
    async cancelApply() {
      let params = {
        no: this.afterSaleInfo.no
      }
      let res = await cancelAfterSale(params)
      if(res.code === 0) {
        this.$message.success('取消申请成功')
        this.$emit("on-update")
      }
      
    },
    cotinueApply(type) {
      this.$emit('on-copy', type)
    }
  }

}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";
.after-sale-detail {
  .as-section {
    flex-direction: column;
    align-items: center;
    justify-content: start;
    padding: 0 12px;
    .title {
      font-size: 1.1em;
      padding: 8px;
      border-bottom: 1px solid #e8e8e8;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .as-detail {
      margin: 5px 0px;
      width: 100%;
      .as-detail-item {
        font-size: 1em;
        margin: 8px auto;
        display: flex;
        .item-title {
          width: 120px;
          margin-right: 5px;
          color: #9c9c9c;
          text-align: justify;
          text-align-last: justify;
        }
        .item-value {
          margin-left: 5px;
          width: 80%;
          word-break: break-all;
        }
      }
      .status-memo {
        margin: 10px auto 20px;
        width: 100%;
        word-break: break-all;
      }
    }
  }
}

.tdesign-demo-image-viewer__ui-image {
  width: 100%;
  height: 100%;
  display: inline-flex;
  position: relative;
  justify-content: center;
  align-items: center;
  border-radius: var(--td-radius-small);
  overflow: hidden;
}

.tdesign-demo-image-viewer__ui-image--hover {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: var(--td-text-color-anti);
  line-height: 22px;
  transition: 0.2s;
}

.tdesign-demo-image-viewer__ui-image:hover .tdesign-demo-image-viewer__ui-image--hover {
  opacity: 1;
  cursor: pointer;
}

.tdesign-demo-image-viewer__ui-image--img {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  cursor: pointer;
  position: absolute;
}

.tdesign-demo-image-viewer__ui-image--footer {
  padding: 0 16px;
  height: 56px;
  width: 100%;
  line-height: 56px;
  font-size: 16px;
  position: absolute;
  bottom: 0;
  color: var(--td-text-color-anti);
  background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0) 100%);
  display: flex;
  box-sizing: border-box;
}

.tdesign-demo-image-viewer__ui-image--title {
  flex: 1;
}

.tdesign-demo-popup__reference {
  margin-left: 16px;
}

.tdesign-demo-image-viewer__ui-image--icons .tdesign-demo-icon {
  cursor: pointer;
}

.tdesign-demo-image-viewer__base {
  width: 160px;
  height: 160px;
  margin: 10px;
  border: 2px solid var(--td-bg-color-secondarycontainer);
  border-radius: var(--td-radius-medium);
}
</style>