<template>
  <div class="loading-lock">
    <t-loading v-if="loading" text=" 自动登录中，请稍后..." :indicator="true"></t-loading>
    <div v-if="noPermission" style="font-size: 24px;color: #1d2129;text-align: center;margin-top: 48px;">
      抱歉，您无权限访问此页面
    </div>
  </div>
</template>
  
<script>
import { ssoLogin } from '@/views/Ssolink/api'
import { setToken } from '@/base/cookie'

export default {
  name: 'SsoLink',
  data() {
    return {
      loading: false,
      noPermission: false,
    }
  },
  methods: {
    loginPage(data) {
      this.loading = true
      ssoLogin(data).then(res => {
        if(res.code === 0) {
          sessionStorage.setItem('loginWay', 'sso')
          setToken(res.data)
        } else {
          this.$message.error("自动登录失败");
        }
        setTimeout(() => {
          this.loading = false
          let toPath = data.redirect || "/home";
          this.$router.push(toPath);
        }, 1000)
      }).catch(() => {
        this.$message.error("登录失败，自动跳至首页");
        this.$router.push('/home');
      })
    }
  },
  computed: {},
  created() {
    const uid = this.$route.query.uid
    const tcode = this.$route.query.tcode
    const etoken = this.$route.query.etoken || this.$route.query.token
    const redirect = this.$route.query.redirect
    const etype = this.$route.query.etype
    if (!tcode || !etoken) {
      this.noPermission = true
    } else {
      this.loginPage({
        uid,
        tcode,
        etoken,
        redirect,
        etype
      })
    }
  }
}
</script>
<style scoped>
.loading-lock {
  height: 500px !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>

  