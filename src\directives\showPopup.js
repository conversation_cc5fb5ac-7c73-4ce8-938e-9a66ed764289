export default {
  showPopup: {
    bind(el, binding, vnode) {
      // 监听 mouseover 事件
      const leaveHandler = (e) => {
        let relatedTarget = e.relatedTarget;
        if (
          relatedTarget &&
          (relatedTarget === el || el.contains(relatedTarget))
        ) {
          return;
        }

        vnode.context[binding.expression](e);
      };

      // 绑定事件
      el.addEventListener("mouseleave", leaveHandler);

      // 将 handler 函数存储在元素的自定义属性中
      el._vueMouseLeave_ = leaveHandler;
    },

    unbind(el) {
      // 移除绑定的事件监听
      el.removeEventListener("mouseleave", el._vueMouseLeave_);
      delete el._vueMouseLeave_;
    },
  },
};
