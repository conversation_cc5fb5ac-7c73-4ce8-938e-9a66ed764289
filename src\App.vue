<template>
  <div id="app" v-show="delayShow">
    <div :id="getId" style="height:100%;">
      <Header v-if="!$route.meta.hiddenHeader"></Header>
      <fix-header v-if="!$route.meta.hiddenHeader"></fix-header>
      <router-view class="container"></router-view>
      <Footer v-if="$route.meta.showFooter"></Footer>
      <FixToolbar></FixToolbar>
    </div>
  </div>
</template>

<script>
import Header from '@/components/Header/index.vue'
import FixHeader from '@/components/FixHeader/index.vue'
import Footer from '@/components/Footer/index.vue'
import FixToolbar from '@/components/FixToolbar/index.vue'
import { mapState } from 'vuex'
export default {
  name: 'App',
  components: { FixToolbar },
  data() {
    return {
      delayShow: false,
      showHeight: '20px'
    }
  },
  components: {
    FixHeader,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
  },
  computed: {
    ...mapState({
      standard: state => state.Home.standard
    }),
    getId() {
      return !this.standard ? 'college-wrap' : ''
    }
  },
  mounted() {
    setTimeout(() => {
      this.delayShow = true
    }, 200)
  }
}
</script>
<style lang="less">
@import "@/style/variables.less";

#app {
  position: relative;
  background-color: #89858504 !important;
  height: 100%;
  .container {
    position: relative;
    min-height: calc(100% - 328px);
    background-color: #fff;
  }
}
</style>