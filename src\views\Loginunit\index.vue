<template>
  <div class="footer">
    <div class="top-box">
      <img src="@/assets/login/logo.png" alt="" @click="jumpHome" />
    </div>
    <div class="content-box">
      <div class="box">
        <div class="box-left">
          <img src="@/assets/login/Mask_group.png" alt="" />
        </div>
        <div class="box-rt">
          <div class="form-box">
            <span class="form-text">用户登录</span>
            <t-form ref="loginForm" :data="loginForm" :rules="rules" @submit="onSubmit">

              <div class="form-com">
                <img src="@/assets/login/Frame@3x(2).png" alt="" />
                <span>手机号:</span>
              </div>
              <!-- 用户名/手机号输入框 -->
              <t-form-item name="mobile">
                <t-input v-model.trim="loginForm.mobile" autocomplete="off" :maxlength="13" placeholder='手机号' />
              </t-form-item>

              <div class="form-com">
                <img src="@/assets/login/Frame@3x(3).png" alt="" />
                <span>{{ isPasswordLogin ? "密码:" : "验证码:" }}</span>
              </div>

              <t-form-item name="verifyCode" class="verifyCode">
                <t-input v-if="isPasswordLogin" type="password" v-model="loginForm.password" autocomplete="off" :maxlength="60"></t-input>
                <t-input v-if="!isPasswordLogin" v-model.trim="loginForm.verifyCode" placeholder="" :maxlength="6"></t-input>
                <t-button v-if="!isPasswordLogin" class="send-verify-code" @click="sendVerificationCode"
                  :disabled="sendDisabled" theme="primary">{{ sendText }}</t-button>
              </t-form-item>

              <div class="login-type-switch">
                <t-link theme="primary" hover="color" @click="toggleLoginType">
                  {{ isPasswordLogin ? '验证码登录' : '密码登录' }}
                </t-link>
              </div>

              <t-form-item>
                <t-button block type="submit" theme="primary" size="large" :loading="loading">
                  登录
                </t-button>
              </t-form-item>
            </t-form>
            <div>
              <t-button style="margin:30px auto 40px;width:100%;" variant="outline" theme="warning"
                @click="jumpFindPass">首次登录或忘记密码</t-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="lower-box">
      <span>Copyright@2024 武汉大学工会版权所有</span>
      <span>技术支持:湖北金一禾技术服务有限公司</span>
      <span>技术咨询热线：18302720011 工作日：09:00-17:30</span>
    </div>
  </div>
</template>

<script>
import { encryptSm2 } from '@/utils/util';
import * as authApi from '@/views/Login/api'

const INITIAL_DATA = {
  mobile: "",
  password: "",
  userNo: "",
  verifyCode: ""
};
export default {
  components: {},
  data() {
    return {
      isPasswordLogin: true,
      loading: false,
      initSeconds: 61,
      smsSeconds: 61,
      loginForm: {
        mobile: '',
        password: '',
        verifyCode: '',
      },
      rules: {
        mobile: [
          { required: true, message: '请输入手机号', type: 'error' },
        ],
        password: [
          { required: true, message: '请输入密码', type: 'error' },
        ],
        verifyCode: [
          { required: false, message: '请输入验证码', type: 'error' },
        ],
      },
    }
  },
  computed: {
    sendDisabled() {
      return this.smsSeconds < this.initSeconds
    },
    placeholderText() {
      return this.isPasswordLogin ? "密码:" : "验证码:";
    },
    sendText() {
      if (this.smsSeconds === this.initSeconds) {
        return '发送验证码'
      }
      return `剩余${this.smsSeconds}秒`
    }
  },
  methods: {
    validateMobile(mobile) {
      return /^1[3-9]\d{9}$/.test(mobile)
    },
    toggleLoginType() {
      this.isPasswordLogin = !this.isPasswordLogin
      if (this.isPasswordLogin) {
        this.rules.password[0].required = true
        this.rules.verifyCode[0].required = false
      } else {
        this.rules.password[0].required = false
        this.rules.verifyCode[0].required = true
      }
    },
    sendVerificationCode() {
      if (!this.loginForm.mobile) {
        this.$message.warning('手机号格式不能为空');
        return
      }
      if (!this.validateMobile(this.loginForm.mobile)) {
        this.$message.warning('手机号格式不正确');
        return
      }
      // 发送短信验证码...
      let params = {
        mobile: this.loginForm.mobile,
        scene: 1
      }
      authApi.sendSmsCode(params).then(res => {
        if (res.code === 0) {
          this.$message.info('短信验证码发送成功');

          let func = () => {
            if (this.smsSeconds === 0) {
              this.smsSeconds = this.initSeconds
              return
            }
            this.smsSeconds--
            setTimeout(func, 1000)
          }
          this.smsSeconds--
          setTimeout(func, 1000)
        }
      }).catch(e => {
        this.smsSeconds = this.initSeconds
      })
    },
    onSubmit({ validateResult, firstError }) {
      if (validateResult === true) {
        this.userLogin();
      } else {
        console.log("Errors: ", validateResult);
        this.$message.warning(firstError);
      }
    },
    async userLogin() {
      if (!this.loginForm.mobile) {
        this.$message.info("需要填写手机号");
        return
      }
      if (!/^1[3-9]\d{9}$/.test(this.loginForm.mobile)) {
        this.$message.info("手机号格式不正确");
        return
      }
      let params = {
        mobile: this.loginForm.mobile,
      }
      sessionStorage.setItem('loginWay', 'account')
      if (this.isPasswordLogin) {
        if (!this.loginForm.password) {
          this.$message.info("需要填写密码");
          return
        }
        params.crypto = 'sm2'
        params.password = encryptSm2(this.loginForm.password);
        this.$store.dispatch('userLogin', params).then(() => {
          let toPath = this.$route.query.redirect || "/home";
          this.$router.push(toPath);
        })
      } else {
        if (!this.loginForm.verifyCode) {
          this.$message.info("需要填写验证码");
          return
        }
        params.code = this.loginForm.verifyCode;
        this.$store.dispatch('smsLogin', params).then(() => {
          let toPath = this.$route.query.redirect || "/home";
          this.$router.push(toPath);
        })
      }
    },
    jumpFindPass() {
      this.$router.push('/find-pass');
    },
    jumpHome() {
      this.$router.push('/home');
    }
  },
  created() { },
  mounted() { },
};
</script>
<style lang="less" scoped>
@import "@/style/variables.less";

.footer {
  .top-box {
    display: flex;
    align-items: center;
    width: 1200px;
    height: 90px;
    line-height: 90px;
    margin: 0 auto;

    img {
      line-height: 90px;
    }
  }

  .form-com {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
    margin-top: 20px;

    img {
      width: 20px;
      height: 20px;
    }

    span {
      margin-left: 5px;
    }
  }

  .form-text {
    margin: 20px 0px;
    display: block;
    width: 100%;
    text-align: center;
    font-weight: bold;
    font-size: 24px;
    color: #f22e00;
  }

  .login-type-switch {
    color: #f22e00;
    margin-bottom: 15px;
    font-size: 14px;
    text-align: left;
    width: auto;
    display: inline-flex;
    align-items: center;
    background: none;
    border: none;
    cursor: pointer;
    transition: none;
  }

  .login-type-switch:hover {
    background: none;
    color: #f22e00;
    border: none;
  }

  .content-box {
    height: 710px;
    background-image: url("@/assets/login/Group_641.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;

    .box {
      display: flex;
      width: 1200px;
      margin: 0 auto;

      .box-left {
        display: flex;
        align-items: center;
        height: 710px;
        line-height: 710px;
      }

      .box-rt {
        display: flex;
        align-items: center;
        margin-left: auto;
        height: 710px;

        .form-box {
          width: 400px;
          background-color: #fff;
          border-radius: 10px;
          padding: 20px;
        }
      }
    }
  }

  .lower-box {
    width: 1200px;
    text-align: center;
    margin: 0 auto;
    margin-top: 40px;

    span {
      display: block;
      margin-bottom: 5px;
    }
  }

  .full-width-adornment {
    width: 100%;
    display: flex;
    align-items: center;

    .t-input {
      flex: 1;
    }

    .t-button {
      margin-left: 5px;
      /* 根据需要调整间距 */
    }
  }

  .verifyCodeBtn {
    flex: 1;
    width: 100%;
    margin-left: 0;
  }

  // .verifyCode {
  //   margin-bottom: 15px;
  // }

  .send-verify-code {
    margin-left: 12px;
  }
}
</style>