import request from '@/base/service'
import qs from 'qs'

const requestMember = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_MEMBER_API
    }
  })
}

const requestMall = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_MALL_API
    }
  })
}

const requestSystem = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_SYSTEM_API
    }
  })
}

const requestProduct = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_PRODUCT_API
    }
  })
}

// 使用手机 + 密码登录
export function authLogin(data) {
  return requestMember({
    url: '/auth/login',
    method: 'post',
    data
  })
}

// 查询顶级大类
export function getRootCategoryList(params) {
  return requestProduct({
    url: '/category/getRootList',
    method: 'get',
    params
  })
}

// 查询分类名称路径
export function getCategoryNamePath(params) {
  return requestProduct({
    url: '/category/getNamePath',
    method: 'get',
    params,
    paramsSerializer: params => qs.stringify(params, { arrayFormat: "repeat" })
  })
}

// 查询父类下所有级别的分类
export function getChildCategoryTreeList(params) {
  return requestProduct({
    url: '/category/getChildTreeList',
    method: 'get',
    params
  })
}

// 查询分类列表
export function queryCategoryList(params) {
  return requestSystem({
    url: '/cms/category/list',
    method: 'get',
    params,
    paramsSerializer: params => qs.stringify(params, { arrayFormat: "repeat" })
  })
}

// 查询内容列表
export function queryContentList(params) {
  return requestSystem({
    url: '/cms/content/list',
    method: 'get',
    params,
    paramsSerializer: params => qs.stringify(params, { arrayFormat: "repeat" })
  })
}

// 查询内容详情
export function getContentByCode(code) {
  return requestSystem({
    url: '/cms/content/info-by-code/' + code,
    method: 'get'
  })
}

// 查询内容详情
export function getContentById(id) {
  return requestSystem({
    url: '/cms/content/info-by-id/' + id,
    method: 'get'
  })
}

// 获取配置信息
export function getConfig(params) {
  return requestMall({
    url: '/basis-config/get',
    method: 'get',
    params
  })
}

// 获取广告位
export function getAdvPositionList(params) {
  return requestMall({
    url: '/adv-position/list',
    method: 'get',
    params
  })
}

// 获取首页配置
export function getHomeConfig(params) {
  return requestMall({
    url: '/home-config/v2/get',
    method: 'get',
    params
  })
}

// 获取供应商
export function getTopList(params) {
  return requestMall({
    url: '/supplier/get-top-list',
    method: 'get',
    params
  })
}

// 获取供应商详情
export function getSupplierDetail(params) {
  return requestMall({
    url: '/supplier/get-detail',
    method: 'get',
    params
  })
}

// 获取运营区域列表
export function getSeoCardList(params) {
  return requestProduct({
    url: '/seo-card/list',
    method: 'get',
    params
  })
}