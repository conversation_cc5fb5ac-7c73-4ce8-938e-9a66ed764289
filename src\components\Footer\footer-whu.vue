<template>
  <div class="footer">
    <div class="footer-container">
      <div class="footerList">
        <div class="footerItem" v-if="false">
          <div class="main-title">平台规则</div>
          <div class="footerItemCon">
            <div @click="jumpTo(1, 'gyjyc')" class="link-item">关于我们</div>
            <div @click="jumpTo(0, 'jcyyszc')" class="link-item">协议规则</div>
            <div @click="jumpTo(1, 'jygz')" class="link-item">交易规则</div>
          </div>
        </div>
        <div class="footerItem" v-if="false">
          <div class="main-title">采购人指南</div>
          <div class="footerItemCon">
            <div @click="jumpTo(1, 'yxds')" class="link-item">优选电商</div>
            <div @click="jumpTo(1, 'wscs')" class="link-item">网上超市</div>
            <div @click="jumpTo(1, 'jjcg')" class="link-item">竞价采购</div>
          </div>
        </div>
        <div class="footerItem" v-if="false">
          <div class="main-title">供应商指南</div>
          <div class="footerItemCon">
            <div @click="jumpTo(1, 'spfb')" class="link-item">商品发布</div>
            <div @click="jumpTo(1, 'ddcl')" class="link-item">订单处理</div>
            <div @click="jumpTo(1, 'jjcy')" class="link-item">竞价参与</div>
          </div>
        </div>
        <div class="footerItem">
          <div class="main-title">友情链接</div>
          <div class="footerItemCon">
            <div @click="jumpTo(91)" class="link-item">武汉大学</div>
            <div @click="jumpTo(92)" class="link-item">武汉大学招标网</div>
            <div @click="jumpTo(93)" class="link-item">武汉大学一体化直采平台</div>
          </div>
        </div>
      </div>
      <div class="right" style="display: flex;align-items: center;justify-content: center;flex-direction: column;padding: 10px;">
        <div> Copyright@2024 武汉大学版权所有 </div>
        <div> 策划：武汉大学采购与招投标管理中心 &nbsp;&nbsp; 主持：武汉大学工会委员会  </div>
        <div> 工作日 9:00-17:30 &nbsp;&nbsp; 平台技术支持热线：18302720011 18302720022 </div>
        <!-- <div> 服务邮箱：<EMAIL> </div>  -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      imgSrc: require("./images/symbol.png"),
    };
  },
  computed: {
    title() {
      return this.$store.state.Home.configData.title || '武汉大学一体化直采平台'
    }
  },
  methods: {
    jumpTo(type1, type2) {
      if (type1 === 91) {
        window.open('https://www.whu.edu.cn/', '_blank')
        return
      }
      if (type1 === 92) {
        window.open('https://cgztb.whu.edu.cn/', '_blank')
        return
      }
      if (type1 === 93) {
        window.open('https://zc2.whu.edu.cn/', '_blank')
        return
      }
      if (type1 === 10) {
        this.$router.push('/home')
        window.scrollTo({
          top: 0,
          behavior: "smooth" // 平滑滚动效果
        });
        return
      }
      if(type1 === 0) {
        this.$router.push({
          path: '/static/agreement',
          query: {
            ccode: type2
          }
        })
        return
      }
      if(type1 === 1) {
        this.$router.push({
          path: '/static/rule',
          query: {
            ccode: type2
          }
        })
        return
      }
    },
    windowTo() {
      window.open('https://beian.miit.gov.cn/#/Integrated/index', '_blank')
    }
  }
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.footer {
  background-color: var(--td-brand-footer-background-color);
  color: var(--td-brand-footer-color);
  border-top: 1px solid #E9EBF2;

  .footer-container {
    width: 1200px;
    margin: 0 auto;
    padding: 30px 15px 20px;
    display: flex;

    .footerList {
      padding-top: 12px;
      overflow: hidden;
      display: flex;
      width: 60%;
      border-right: 1px solid #E9EBF2;

      .footerItem {
        flex: 1;

        .main-title {
          font-size: 18px;
          margin-bottom: 14px;
        }

        .footerItemCon {
          .link-item {
            line-height: 18px;
            font-size: 12px;
            margin-top: 8px;
            cursor: pointer;
            &:hover {
              color: @primary-color;
            }
          }
        }
      }
    }
    .right {
      width: 40%;
      padding-left: 90px;
      display: flex;
      line-height: 30px;
      flex-direction: column;
      .right-header {
        font-size: 22px;
      }
      .right-info {
        font-size: 14px;
      }
      .right-filings {
        font-size: 14px;
        display: flex;
        align-items: center;
        cursor: pointer;
        .img {
          width: 20px;
          height: 20px;
        }
        .filings {
          margin-left: 4px;
        }
      }
    }
  }
}
</style>
