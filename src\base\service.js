/* eslint-disable prefer-promise-reject-errors */
import axios from 'axios'
// eslint-disable-next-line
import { setToken, getToken, getRefreshToken } from '@/base/cookie'
import { refreshToken } from '@/views/Login/api/index'
import { MessagePlugin, DialogPlugin  } from 'tdesign-vue'
import store from "@/store"
import { toLogin } from '@/utils/util'

const baseURL = process.env.VUE_APP_BASE_API

// Axios 无感知刷新令牌，参考 https://www.dashingdog.cn/article/11 与 https://segmentfault.com/a/1190000020210980 实现
// 请求队列
let requestList = []
// 是否正在刷新中
let isRefreshToken = false
let mydialog = null

function handleAuthorized() {
  if (mydialog) {
    return false
  }
  mydialog = DialogPlugin.alert({
    header: '提示',
    body: '账号未登录，请前往登录页',
    confirmBtn: {
      content: '确定',
      theme: 'primary',
    },
    closeOnEscKeydown: false,
    closeOnOverlayClick: false,
    closeBtn: false,
    onConfirm: () => {
      mydialog.hide()
      toLogin()
      mydialog = null
    }
  })

  return Promise.reject('无效的会话，或者会话已过期，请重新登录。')
}

const service = axios.create({
  baseURL,
  timeout: 100000 * 50, // 请求超时时间
  withCredentials: false
})

// request拦截,在请求发起前
service.interceptors.request.use(config => {
  if (store.state.Home.configData.tenantId) {
    config.headers['tenant-id'] = store.state.Home.configData.tenantId
    if (getToken()) {
      config.headers['Authorization'] = `Bearer ${getToken()}`
    }
  }
  return config
})

// respone拦截,获取请求数据后，搞事情
service.interceptors.response.use(
  // 正常获取数据
  async response => {
    const list = [
      'x-pkcs7-certificates', 'vnd.ms-excel', 'pem', 'zip', 'application/pdf'
    ]
    const flag = list.some(
      x =>
        response.headers['content-type'] &&
        response.headers['content-type'].indexOf(x) > -1
    )
    // if (
    //   (response.headers['content-type'] != null && flag) ||
    //   response.data instanceof Blob
    // ) {
    //   return response
    // }
    if (response.data && ['0'].includes(String(response.data.code))) {
      return response.data
    } else if (response.data.code === 403) {
      MessagePlugin.error(response.data.msg)
      setTimeout(() => {
        sessionStorage.setItem('sidebarChecked', JSON.stringify({ path: '/home' }))
        location.replace(location.origin)
      }, 1200)
    } else if (response.data.code === 401) {
      // 如果未认证，并且未进行刷新令牌，说明可能是访问令牌过期了
      if (!isRefreshToken) {
        isRefreshToken = true
        // 1. 如果获取不到刷新令牌，则只能执行登出操作
        if (!getRefreshToken()) {
          return handleAuthorized()
        }
        // 2. 进行刷新访问令牌
        try {
          const refreshTokenRes = await refreshToken()
          // 2.1 刷新成功，则回放队列的请求 + 当前请求
          setToken(refreshTokenRes.data)
          requestList.forEach(cb => cb())
          return service(response.config)
        } catch (e) {// 为什么需要 catch 异常呢？刷新失败时，请求因为 Promise.reject 触发异常。
          // 2.2 刷新失败，只回放队列的请求
          requestList.forEach(cb => cb())
          // 提示是否要登出。即不回放当前请求！不然会形成递归
          return handleAuthorized();
        } finally {
          requestList = []
          isRefreshToken = false
        }
      } else {
        // 添加到队列，等待刷新获取到新的令牌
        return new Promise(resolve => {
          requestList.push(() => {
            response.config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
            resolve(service(response.config))
          })
        })
      } 
    } else if (response.data.code !== 503 && !response.request.responseURL.includes('/basis-config/get')){
      let errMsg = response.msg || response.data.msg || '请求失败'
      if(errMsg.indexOf('令牌') < 0) MessagePlugin.error(errMsg)
    }
    return Promise.reject(response)
    // return response?.data?.code || false
  },
  // 请求失败
  error => {
    MessagePlugin.error((error?.response?.data?.msg) || '请求失败')
    return Promise.reject(error?.response?.data)
  }
)

export default service
