import request from '@/base/service'

const requestP = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_PRODUCT_API
    }
  })
}

// 搜索商品-多供应商接口
export function goodsSearchPageListV2(params) {
  return requestP({
    url: '/sku/goodsSearchPageList',
    method: 'get',
    params
  })
}

// 搜索商品-京东VOP接口
export function goodsSearchPageListV1(params) {
  return requestP({
    url: '/vopgoods/goodsSearchPageList',
    method: 'get',
    params
  })
}
