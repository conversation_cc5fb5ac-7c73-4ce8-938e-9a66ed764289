<template>
  <div class="home-wrapper" v-loading="!inited" v-if="inited">
    <div class="block-wrap" v-if="false">
      <!-- 凑单专区 -->
      <div class="block-item">
        <div class="title">
          <img src="@/views/Home/Fuli/image/coudan.png" alt="" />
          <span>凑单专区</span>
        </div>
        <img style="width: 100%;height: 280px;cursor: pointer;" :src="require('@/views/Home/Fuli/image/merge.png')" alt="" @click="toSpecial('凑单专区')" />
      </div>
      <div class="block-item">
        <!-- 精品礼包 -->
        <div class="title">
          <img src="@/views/Home/Fuli/image/gift.png" alt="" />
          <span>精品礼包</span>
          <!-- TODO -->
          <!-- <div>查看更多</div> -->
        </div>
        <img style="width: 100%;cursor: pointer;" :src="require('@/views/Home/Fuli/image/special.png')" alt="" @click="toSpecial('精品礼包')" />
      </div>
    </div>
    <!-- 福利平台 -->
    <!-- <Fuli v-for="item in specialList" :key="item.title" :itemData="item"></Fuli> -->
    <!-- 自定义商品区域-->
    <SeoCard v-if="homeConfig.seoCardSwitch"></SeoCard>

    <GlobalTip></GlobalTip>
  </div>
</template>

<script>
import Fuli from './Fuli'
import GlobalTip from './GlobalTip'
import SeoCard from './SeoCard'
import { mapState } from 'vuex'

export default {
  name: 'Home',
  components: {
    Fuli,
    GlobalTip,
    SeoCard
  },
  data() {
    return {
      inited: false,
      specialList: [
        {
          title: '粮油调味',
          image: require('@/views/Home/Fuli/image/liangyou.png'),
          titleIcon: require('@/views/Home/Fuli/image/liangyou-icon.png'),
          categoryIdName: 'categoryId1',
          categoryId: ''
        },
        {
          title: '居家用品',
          image: require('@/views/Home/Fuli/image/jujia.png'),
          titleIcon: require('@/views/Home/Fuli/image/jujia-icon.png'),
          categoryIdName: 'categoryId1',
          categoryId: ''
        },
        {
          title: '家用电器',
          image: require('@/views/Home/Fuli/image/dianqi.png'),
          titleIcon: require('@/views/Home/Fuli/image/dianqi-icon.png'),
          categoryIdName: 'categoryId1',
          categoryId: ''
        }
      ]
    }
  },
  methods: {
    init() {
      this.specialList.forEach(item => {
        let cate = this.configCategorys.find(configItem => configItem.name === item.title)
        if(cate) {
          item.categoryId = cate.id
        } 
      })
      this.inited = true
    },
    toSpecial(cname, level = 2) {
      let cate = this.configCategorys.find(configItem => configItem.name === cname)
      if(!cate) {
        console.log('未配置商品分类', cname)
        return
      }
      this.$router.push(`/storeSearch?categoryId${level}=${cate.id}`)
    }
  },
  computed: {
    ...mapState({
      homeConfig: state => state.Home.homeConfigData,
      defaultRegions: state => state.defaultAddress.defaultRegions || []
    }),
    configCategorys() {
      return this.homeConfig.bestProductCategorys || []
    }
  },
  created() {
    this.init()
  }
}
</script>

<style lang="less" scoped>
.home-wrapper {
  padding-bottom: 24px;
  padding-top: 10px;
  background-color: #89858517 !important;
  .block-wrap {
    width: 1200px;
    margin: 0 auto;
    padding: 0;
    .block-item {
      width: 100%;
      margin: 20px auto;
      padding: 0;
      background-color: #fff;
      .title {
        padding: 10px 5px;
        font-size: 20px;
        img {
          padding: 0;
          width: 30px;
          height: 30px;
          margin-right: 5px;
        }
      }
      img {
        padding: 5px;
      }
    }
    .title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      display: flex;
      align-items: center;
    }
  }
}
</style>