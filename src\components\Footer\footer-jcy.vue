<template>
  <div class="footer">
    <div class="footer-container">
      <div class="button-like" style="margin-right: 140px">
        <span class="icon"></span>
        <span class="text">
          <span class="text_1">品质保障</span>
          <span class="text_2">电商自营</span>
        </span>
      </div>
      <div class="button-like" style="margin-right: 140px">
        <span class="icon-1"></span>
        <span class="text">
          <span class="text_1">自主选择</span>
          <span class="text_2">所喜好的商品</span>
        </span>
      </div>
      <div class="button-like" style="margin-right: 100px">
        <span class="icon-2"></span>
        <span class="text">
          <span class="text_1">优惠优价</span>
          <span class="text_2">不高于电商官网价</span>
        </span>
      </div>
      <div class="button-like">
        <span class="icon-3"></span>
        <span class="text">
          <span class="text_1">高效便捷</span>
          <span class="text_2">送货到指定地点</span>
        </span>
      </div>
      <!-- <div class="footerList">
        <div class="footerItem">
          <div class="main-title">平台规则</div>
          <div class="footerItemCon">
            <div @click="jumpTo(1, 'gyjyc')" class="link-item">关于我们</div>
            <div @click="jumpTo(0, 'jcyyszc')" class="link-item">协议规则</div>
            <div @click="jumpTo(1, 'jygz')" class="link-item">交易规则</div>
          </div>
        </div>
        <div class="footerItem">
          <div class="main-title">采购人指南</div>
          <div class="footerItemCon">
            <div @click="jumpTo(1, 'yxds')" class="link-item">优选电商</div>
          </div>
        </div>
        <div class="footerItem">
          <div class="main-title">供应商指南</div>
          <div class="footerItemCon">
            <div @click="jumpTo(1, 'spfb')" class="link-item">商品发布</div>
            <div @click="jumpTo(1, 'ddcl')" class="link-item" v-if="false">订单处理</div>
          </div>
        </div>
        <div class="footerItem">
          <div class="main-title">友情链接</div>
          <div class="footerItemCon">
            <div @click="jumpTo(10)" class="link-item">金采通</div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="right-header">{{title}}</div>
        <div class="right-info">由EDUIPL®提供平台技术支持</div>
        <div class="right-filings" v-if="false">
          <t-image class="img" :src="imgSrc"></t-image>
          <div class="filings" @click="windowTo">
            鄂ICP备2023016859号-1
          </div>
        </div>
      </div> -->
    </div>
    <div class="footer-right">
      <div> Copyright@2024 武汉大学工会版权所有 </div>
      <div> 策划：武汉大学采购与招投标管理中心 &nbsp;&nbsp; 主持：武汉大学工会委员会  </div>
      <div> 平台技术支持热线：18302720011 18302720022 工作日 9:00-17:30</div>
      <!-- <div> 服务邮箱：<EMAIL> </div>  -->
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      imgSrc: require("./images/symbol.png"),
    };
  },
  computed: {
    title() {
      return this.$store.state.Home.configData.title || '金采通'
    }
  },
  methods: {
    jumpTo(type1, type2) {
      if (type1 === 91) {
        window.open('https://www.whu.edu.cn/', '_blank')
        return
      }
      if (type1 === 92) {
        window.open('https://cgztb.whu.edu.cn/', '_blank')
        return
      }
      if (type1 === 93) {
        window.open('https://zcfl.whu.edu.cn/', '_blank')
        return
      }
      if (type1 === 10) {
        this.$router.push('/home')
        window.scrollTo({
          top: 0,
          behavior: "smooth" // 平滑滚动效果
        });
        return
      }
      if(type1 === 0) {
        this.$router.push({
          path: '/static/agreement',
          query: {
            ccode: type2
          }
        })
        return
      }
      if(type1 === 1) {
        this.$router.push({
          path: '/static/rule',
          query: {
            ccode: type2
          }
        })
        return
      }
    },
    windowTo() {
      window.open('https://beian.miit.gov.cn/#/Integrated/index', '_blank')
    }
  }
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.footer {
  background-color: var(--td-brand-footer-background-color);
  color: var(--td-brand-footer-color);
  border-top: 1px solid #E9EBF2;

  .footer-container {
    width: 1200px;
    margin: 0 auto;
    padding: 30px 15px 20px;
    display: flex;

    .footerList {
      padding-top: 12px;
      overflow: hidden;
      display: flex;
      width: 60%;
      border-right: 1px solid #E9EBF2;

      .footerItem {
        flex: 1;

        .main-title {
          font-size: 18px;
          margin-bottom: 14px;
        }

        .footerItemCon {
          .link-item {
            line-height: 18px;
            font-size: 12px;
            margin-top: 8px;
            cursor: pointer;
            &:hover {
              color: @primary-color;
            }
          }
        }
      }
    }
    .right {
      display: flex;
      line-height: 30px;
      flex-direction: column;
      .right-header {
        font-size: 22px;
      }
      .right-info {
        font-size: 14px;
      }
      .right-filings {
        font-size: 14px;
        display: flex;
        align-items: center;
        cursor: pointer;
        .img {
          width: 20px;
          height: 20px;
        }
        .filings {
          margin-left: 4px;
        }
      }
    }
    .button-like {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      width: 200px;
    }
    .icon {
      width: 48px; /* 调整图标宽度 */
      height: 48px; /* 调整图标高度 */
      background-image: url("../../assets/Group_56.png");
      background-size: cover;
      margin-right: 8px;
    }
    .icon-1 {
      width: 48px; /* 调整图标宽度 */
      height: 48px; /* 调整图标高度 */
      background-image: url("../../assets/Group_57.png");
      background-size: cover;
      margin-right: 8px;
    }
    .icon-2 {
      width: 48px; /* 调整图标宽度 */
      height: 48px; /* 调整图标高度 */
      background-image: url("../../assets/Group_61.png");
      background-size: cover;
      margin-right: 8px;
    }
    .icon-3 {
      width: 48px; /* 调整图标宽度 */
      height: 48px; /* 调整图标高度 */
      background-image: url("../../assets/Group_58.png");
      background-size: cover;
      margin-right: 8px;
    }
    .text {
      font-size: 14px;
      margin-left: 5px;
    }
    .text_1{
      font-weight: bold;
      font-size: 16px;
      display: block;
      margin-bottom: 5px
    }
  }
  .footer-right {
    display: flex;
    line-height: 30px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 0 20px;
  }
}

</style>
