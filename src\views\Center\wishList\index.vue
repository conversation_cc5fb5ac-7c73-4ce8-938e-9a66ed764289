<template>
  <div class="wishlist-right">
    <div class="wishlist-content" v-loading="loading">
      <div class="title">
        <h3>我的心愿单</h3>
      </div>
      <div style="margin: 10px 0;">
        <t-button @click="createWishList">添加心愿单</t-button>
      </div>
      <t-table v-loading="loading" hover style="flex: 1;" bordered row-key="index" :data="tableData" :columns="columns">
        <template #categoryNames="{ row }">
          <span>{{ row.categoryNames || '--' }}</span>
        </template>
        <template #brand="{ row }">
          <span>{{ row.brand || '--' }}</span>
        </template>
        <template #model="{ row }">
          <span>{{ row.model || '--' }}</span>
        </template>
        <template #productLink="{ row }">
          <t-link v-if="row.productLink" theme="primary" :href="row.productLink" target="_blank">查看链接</t-link>
          <span v-else>--</span>
        </template>
        <template #createTime="{ row }">
          <span v-if="row.orderTime">{{ row.orderTime | formatDate }}</span>
          <span>--</span>
        </template>
        <template #status="{ row }">
          <t-tag shape="round" :theme="row.status === 0 ? 'default' : 'success'" variant="light-outline">
            {{row.status === 0 ? '待回复' : '已回复'}}
          </t-tag>
        </template>
        <template #oper="{ row }">
          <t-link theme="primary" hover="color" @click="jumpToDetail(row)">
            查看详情
          </t-link>
          <t-link style="margin-left: 8px;" v-if="row.status === 0" theme="primary" hover="color" @click="editWishList(row)">
            编辑
          </t-link>
          <t-link style="margin-left: 8px;" v-if="row.status === 0" theme="primary" hover="color" @click="delWish(row)">
            删除
          </t-link>
        </template>
      </t-table>
      <div v-if="tableData.length > 0">
        <t-pagination class="pagination" :total="total" :showPageNumber="true" :showPageSize="false"
          showPreviousAndNextBtn :totalContent="false" @current-change="changePage" />
      </div>
    </div>
    <add-wish-list v-if="visible" @successOpt="successOpt" @closeDialog="closeDialog" :editData="formData"></add-wish-list>
    <detail-wish-list v-if="detailVisible" @closeDialog="closeDialog" :editData="formData"></detail-wish-list>
  </div>
</template>

<script>
import { wishPage, wishDelete } from '@/views/Center/wishList/api'
import addWishList from './add.vue'
import detailWishList from './detail.vue'

export default {
  data() {
    return {
      visible: false,
      detailVisible: false,
      formData: {},
      page: 1,
      tableData: [],
      columns: [
        { colKey: 'categoryNames', width: 200, title: '采购品目' },
        { colKey: 'brand', title: '品牌', ellipsis: true },
        { colKey: 'model', title: '型号', ellipsis: true },
        { colKey: 'quantity', title: '数量' },
        { colKey: 'productLink', title: '产品url' },
        { colKey: 'createTime', title: '发布时间', width: 120 },
        { colKey: 'status', title: '状态' },
        { colKey: 'oper', title: '操作', width: 180 },
      ],
      total: 0,
      loading: false
    }
  },
  components: {
    addWishList,
    detailWishList
  },
  created() {
    this.queryList()
  },
  methods: {
    jumpToDetail(row) {
      this.detailVisible = true
      this.formData = row
    },
    async delWish(row) {
      const res = await wishDelete(row.id)
      if (res.code === 0) {
        this.$message.success('删除成功')
        this.queryList()
      } else {
        this.$message.error('删除失败')
      }
    },
    async queryList() {
      this.loading = true
      const res = await wishPage({
        pageNo: this.page,
        pageSize: 10
      })
      if (res.code === 0 && res.data) {
        this.tableData = res.data.list
        this.total = Number(res.data.total)
      } else {
        this.tableData = []
        this.total = 0
      }
      this.loading = false
    },
    // 保留两位小数
    saveTwoDecimal(val = 0) {
      return (Math.round(val * 100) / 100).toFixed(2)
    },
    changePage(page) {
      this.page = page
      this.queryList()
    },
    closeDialog() {
      this.visible = false
      this.detailVisible = false
    },
    successOpt() {
      this.queryList()
      this.closeDialog()
    },
    createWishList() {
      this.formData = {}
      this.visible = true
    },
    editWishList(row) {
      this.formData = row
      this.visible = true
    }
  }
}
</script>

<style lang="less" scoped>
@import "@/style/variables.less";
//右边
.wishlist-right {
  width: 83.33%;

  //订单部分
  .wishlist-content {
    margin: 0 20px;
    color: #666;

    //标题
    .title {
      margin-bottom: 22px;
      border: 1px solid #ddd;
      h3 {
        padding: 12px 10px;
        font-size: 16px;
        background-color: #f1f1f1;

      }
    }

    .pagination {
      margin: 10px 0;
    }
  }
}
</style>
