<template>
  <div>
    <Header></Header>
    <div class="supplier-container" v-loading="loading">
      <div class="header-top">
        <div class="header-wrap">
          <div class="symbol" @click="jumpToHome">
            <t-image
              fit="contain"
              :src="styleConfigData.logoUrl"
            />
          </div>
          <div class="search">
            <div class="searchInput">
              <t-input
                v-model="query"
                size="large"
                placeholder="请输入商品名称或商品编码"
                @enter="searchItem"
              />
              <div @click="searchItem" class="searchBtn">搜索</div>
            </div>

            <t-badge :count="cartCount">
              <t-button
                @click="jumpToCart"
                class="shoppingcar"
                shape="round"
                variant="outline"
              >
                <CartIcon style="font-size: 18px" slot="icon" />
                购物车
              </t-button>
            </t-badge>
          </div>
        </div>
      </div>

      <!-- supplier -->
      <div class="supplier-block">
        <div class="header-wrap">
          <img :src="supplierDetail.logoUrl" class="supplier-image" v-if="false"/>
          <div class="supplier-name">
            <div class="name">
              <HomeIcon
                style="margin-right: 6px; color: #333; font-weight: normal"
                slot="icon"
              />
              {{ supplierDetail.name }}
              <img
                src="https://mkt-jhun.zhongcy.com/mall-view/img/best-store.png"
                class="image"
              />
            </div>
            <div class="phone">客服电话：{{ supplierDetail.servicePhone }}</div>
          </div>
        </div>
      </div>

      <!-- 好像没用，先隐藏吧 -->
      <div class="nav" v-if="false">
        <t-button class="nav-item" theme="primary">全部商品</t-button>
        <t-button class="nav-item" theme="primary">店铺首页</t-button>
        <t-button class="nav-item" theme="primary">厨具</t-button>
        <t-button class="nav-item" theme="primary">电脑、办公</t-button>
        <t-button class="nav-item" theme="primary">个人护理</t-button>
        <t-button class="nav-item" theme="primary">工业品</t-button>
        <t-button class="nav-item" theme="primary">家居日用</t-button>
        <t-button class="nav-item" theme="primary">家庭清洁/纸品</t-button>
      </div>

      <div class="content-wrap">
        <!--bread-->
        <div class="bread">
          <div>全部结果</div>

          <template v-if="queryData.keyword">
            <div class="segmentation">></div>
            <div class="segmentation">{{ queryData.keyword }}</div>
          </template>
          <template v-if="categoryAggList[0]">
            <div class="segmentation">></div>
            <t-select class="selectCategory" v-model="categoryAggList[0].categoryId" @change="changeCategory(0)" clearable>
              <t-option
                v-for="item in categoryList1"
                :key="item.categoryId"
                :value="item.categoryId"
                :label="item.categoryName"
              />
            </t-select>
          </template>
          <template v-if="categoryAggList[1]">
            <div class="segmentation">></div>
            <t-select class="selectCategory" v-model="categoryAggList[1].categoryId" @change="changeCategory(1)" clearable>
              <t-option
                v-for="item in categoryList2"
                :key="item.categoryId"
                :value="item.categoryId"
                :label="item.categoryName"
              />
            </t-select>
          </template>
          <template v-if="categoryAggList[2]">
            <div class="segmentation">></div>
            <t-select class="selectCategory" v-model="categoryAggList[2].categoryId" @change="changeCategory(2)" clearable>
              <t-option
                v-for="item in categoryList3"
                :key="item.categoryId"
                :value="item.categoryId"
                :label="item.categoryName"
              />
            </t-select>
          </template>
        </div>

        <!--selector-->
        <SearchSelector
          v-if="selectorList.length > 0 || brandAggList.length > 0"
          ref="selector"
          :selectorList="selectorList"
          :brandAggList="brandAggList"
          @checkCategory="checkCategory"
          @checkBrand="checkBrand"
        ></SearchSelector>

        <!-- overview -->
        <div class="overview">
          <div class="left">
            <Address @changeAddress="changeAddress" class="address-comp"></Address>
            <div class="order" :class="{checked: sortType === 1}" @click="sales">销量优先排序</div>
            <div class="order prices" @click="prices">
              <div :class="{ checked: iconType !== '' }">价格排序</div>
              <div class="icon">
                <caret-up-small-icon :class="{ checked: iconType === 'up' }" />
                <caret-down-small-icon
                  :class="{ checked: iconType === 'down' }"
                />
              </div>
            </div>
            <t-range-input
              class="range"
              v-model="ranges"
              @change="changeNum"
              :placeholder="placeholder"
              clearable
            />
            <t-button theme="primary" class="confirm" @click="confirm">
              确定
            </t-button>
          </div>
          <div class="right">
            <div class="text">
              共<span class="red">{{ total }}</span
              >件商品
            </div>
            <t-pagination
              class="pagination"
              :total="total"
              v-model="pageIndex"
              :pageSize="pageSize"
              :showPageNumber="false"
              :showPageSize="false"
              showPreviousAndNextBtn
              :showJumper="false"
              :totalContent="false"
              @current-change="onCurrentChange"
            />
          </div>
        </div>

        <!-- result -->
        <Result
          :defaultRegions="areaIds && areaIds.split(',')"
          :resultData="resultData"
          :loading="loading"
        ></Result>
        <t-pagination
          v-if="resultData.length > 0"
          :total="total"
          v-model="pageIndex"
          :showJumper="false"
          :pageSize="pageSize"
          :totalContent="false"
          page-ellipsis-mode="both-ends"
          @current-change="onCurrentChange"
          :showPageSize="false"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { HomeIcon, CartIcon, CaretDownSmallIcon, CaretUpSmallIcon } from "tdesign-icons-vue";
import { goodsSearchPageListV1, goodsSearchPageListV2 } from '@/views/Supplier/api'
import { getRootCategoryList, getChildCategoryTreeList, getSupplierDetail } from '@/views/Home/api'
import Header from '@/components/Header/index.vue'
import SearchSelector from "./components/SearchSelector";
import Result from "./components/Result";
import { mapState } from "vuex";
export default {
  name: "SupplierSearch",
  components: {
    SearchSelector,
    Header,
    Result,
    HomeIcon,
    CartIcon,
    CaretUpSmallIcon,
    CaretDownSmallIcon,
  },
  data() {
    return {
      logoUrl: require("@/assets/login/logo.png"),
      categoryList1: [],
      categoryList2: [],
      categoryList3: [],
      selectorList: [],
      brandAggList: [],
      iconType: "",
      placeholder: "￥",
      ranges: [],
      pageIndex: 1,
      pageSize: 20,
      total: 0,
      resultData: [],
      categoryAggList: [],
      sortType: 1,
      minPrice: null,
      maxPrice: null,
      areaIds: null,
      queryData: this.$route.query,
      loading: false,
      query: "",
      supplierDetail: {}
    };
  },
  computed: {
    ...mapState({
      configData: state => state.Home.configData,
      cartCount: state => state.User.cartCount,
      styleConfigData: state => state.Home.styleConfigData
    })
  },
  created() {
    this.getSupplierDetail()
  },
  methods: {
    async getSupplierDetail() {
      const res = await getSupplierDetail({
        id: this.$route.query.id,
      });
      if (res.code == 0) {
        this.supplierDetail = res.data;
      }
    },
    changeNum(val) {
      this.ranges = val.map((item) => {
        if (item) {
          item = String(item.replace(/[^\d.]/g, ""));
        }
        return item;
      });
    },
    // 跳转到首页
    jumpToHome() {
      this.$router.push("/home");
    },
    // 搜索商品
    searchItem() {
      this.$router.replace({
        query: {
          ...this.queryData,
          keyword: this.query
        }
      })
    },
    // 跳转到购物车
    jumpToCart() {
      this.$router.push("/shopcart");
    },
    // 查询大类
    async getRootCategoryList() {
      if (this.categoryList1.length > 0) {
        return;
      }
      const res = await getRootCategoryList();
      if (res.code === 0) {
        this.categoryList1 = res.data;
      }
    },
    // 查询二三级类目
    async queryChildCategory(params) {
      if (this.categoryList3.length > 0) {
        return;
      }
      const res = await getChildCategoryTreeList(params);
      if (res.code === 0) {
        this.categoryList2 = res.data;
        if (this.categoryAggList[1]) {
          const temp = res.data.find(x => x.categoryId == this.categoryAggList[1].categoryId) || {}
          this.categoryList3 = temp.childCategoryList || []
        }
      }
    },
    changeAddress(val = []) {
      this.areaIds = val.length > 0 ? val.join(",") : null;
      this.getData();
    },
    prices() {
      if (this.iconType === "down") {
        this.iconType = "up";
        this.sortType = 2;
      } else {
        this.iconType = "down";
        this.sortType = 3;
      }
      this.getData();
    },
    sales() {
      this.iconType = "";
      this.sortType = 1;
      this.getData();
    },
    confirm() {
      if (
        this.ranges[0] &&
        this.ranges[1] &&
        this.ranges[1] - this.ranges[0] <= 0
      ) {
        const temp = this.ranges[0];
        this.$set(this.ranges, 0, this.ranges[1]);
        this.$set(this.ranges, 1, temp);
      }
      this.minPrice = this.ranges[0] || null;
      this.maxPrice = this.ranges[1] || null;
      this.getData();
    },
    checkCategory(item) {
      if (item.cateLevel === 1) {
        this.categoryAggList = [item];
        this.categoryList2 = [];
        this.categoryList3 = [];
        this.$router.replace({
          query: {
            keyword: this.queryData.keyword,
            categoryId1: item.categoryId,
            id: this.$route.query.id,
          },
        });
      } else if (item.cateLevel === 2) {
        this.categoryAggList[1] = item;
        this.categoryAggList[2] = null;
        this.categoryList3 = [];
        this.$router.replace({
          query: {
            keyword: this.queryData.keyword,
            categoryId2: item.categoryId,
            id: this.$route.query.id,
          },
        });
      } else if (item.cateLevel === 3) {
        this.categoryAggList[2] = item;
        this.$router.replace({
          query: {
            keyword: this.queryData.keyword,
            categoryId3: item.categoryId,
            id: this.$route.query.id,
          },
        });
      }
    },
    checkBrand(item) {
      if (item.brandId) {
        this.$router.replace({
          query: {
            ...this.queryData,
            brandId: item.brandId
          }
        })
      } else {
        delete this.queryData.brandId
        const a = { ...this.queryData }
        this.$router.replace({
          query: {
            ...this.queryData,
            otherParams: true // 不加这个$route.query不会变
          }
        })
      }
    },
    onCurrentChange(val) {
      this.pageIndex = val;
      this.getData();
    },
    changeCategory(type) {
      if (!this.categoryAggList[type].categoryId) {
        // 清空，取上一级
        if (type == 0) {
          this.$router.replace({
            query: {
              keyword: this.queryData.keyword
            }
          })
          return
        }
        type = type - 1
      }
      if (type === 0) {
        this.$router.replace({
          query: {
            keyword: this.queryData.keyword,
            categoryId1: this.categoryAggList[0].categoryId,
            id: this.$route.query.id,
          },
        });
      } else if (type === 1) {
        this.$router.replace({
          query: {
            keyword: this.queryData.keyword,
            categoryId2: this.categoryAggList[1].categoryId,
            id: this.$route.query.id,
          },
        });
      } else if (type === 2) {
        this.$router.replace({
          query: {
            keyword: this.queryData.keyword,
            categoryId3: this.categoryAggList[2].categoryId,
            id: this.$route.query.id,
          },
        });
      }
    },
    async getData() {
      this.loading = true
      const { categoryId1, categoryId2, categoryId3, keyword, brandId, tagIds } = this.queryData
      const res = await goodsSearchPageListV2({
        categoryId1,
        categoryId2,
        categoryId3,
        keyword,
        brandId,
        pageIndex: this.pageIndex,
        pageSize: this.pageSize,
        areaIds: this.areaIds,
        minPrice: this.minPrice,
        maxPrice: this.maxPrice,
        sortType: this.sortType,
        supplierId: this.$route.query.id,
        tagIds
      });
      if (res.code === 0) {
        if (res.data.pageResult.total > 0) {
          this.total = Number(res.data.pageResult.total);
          this.resultData = res.data.pageResult.list;
        } else {
          this.total = 0;
          this.resultData = [];
        }
        if (!keyword) {
          this.brandAggList = res.data.brandAggList || []
          // 不是通过关键词查询进的搜索页
          if (!categoryId1 && !categoryId2 && !categoryId3) {
            await this.getRootCategoryList();
            this.selectorList =
              res.data.categoryAggList.filter((x) => x.cateLevel === 1) || [];
            this.loading = false;
            return;
          }
          // 类目搜索
          if (res.data.categoryAggList && res.data.categoryAggList.length > 0) {
            this.categoryAggList = res.data.categoryAggList.sort(
              (a, b) => a.cateLevel - b.cateLevel
            );
          } else {
            this.categoryAggList = [
              {
                categoryId: "",
              },
              {
                categoryId: "",
              },
              {
                categoryId: "",
              },
            ];
          }
          
          let fucn = (level, cateId) => {
            let arr = res.data.categoryAggList.filter(item => item.cateLevel === level) || []
            if(cateId) {
              arr = arr.filter(item => item.categoryId === parseInt(cateId)) || []
            }
            if(arr.length) {
              return arr[0]
            }

            return {categoryId: ''}
          }

          if (categoryId3) {
            this.selectorList = []
            this.categoryAggList = [fucn(1, categoryId1), fucn(2, categoryId2), fucn(3, categoryId3)]
          } else if (categoryId2) {
            this.selectorList = res.data.categoryAggList.filter(x => x.cateLevel == 3) || []
            this.categoryAggList = [fucn(1, categoryId1), fucn(2, categoryId2)]
          } else if (categoryId1) {
            this.selectorList = res.data.categoryAggList.filter(x => x.cateLevel == 2) || []
            this.categoryAggList = [fucn(1, categoryId1)]
          }

          this.getRootCategoryList()
          if (this.categoryAggList[0] && this.categoryAggList[0].categoryId) {
            this.categoryList3 = []
            this.queryChildCategory({
              parentCategoryId: this.categoryAggList[0].categoryId
            })
          }
        } else {
          // 关键字搜索进的搜索页
          let num = 1;
          if (categoryId1) {
            num = 2;
          }
          if (categoryId2) {
            num = 3;
          }
          if (categoryId3) {
            num = 4;
          }
          if (res.data.categoryAggList) {
            // 后台的接口可能返回cateLevel为0的数据
            if (this.categoryList1.length === 0) {
              this.categoryList1 = res.data.categoryAggList
                .filter((x) => x.cateLevel === 1)
                .map((x) => {
                  return {
                    categoryName: x.cateName,
                    categoryId: x.categoryId,
                    cateLevel: x.cateLevel,
                  };
                });
            }
            if (this.categoryList2.length === 0) {
              this.categoryList2 = res.data.categoryAggList
                .filter((x) => x.cateLevel === 2)
                .map((x) => {
                  return {
                    categoryName: x.cateName,
                    categoryId: x.categoryId,
                    cateLevel: x.cateLevel,
                  };
                });
            }
            if (this.categoryList3.length === 0) {
              this.categoryList3 = res.data.categoryAggList
                .filter((x) => x.cateLevel === 3)
                .map((x) => {
                  return {
                    categoryName: x.cateName,
                    categoryId: x.categoryId,
                    cateLevel: x.cateLevel,
                  };
                });
            }
          } else {
            this.categoryList1 = [];
            this.categoryList2 = [];
            this.categoryList3 = [];
            this.categoryAggList = [];
          }
          if (num === 4) {
            this.selectorList = [];
          } else if (res.data.categoryAggList) {
            this.selectorList =
              res.data.categoryAggList.filter((x) => x.cateLevel === num) || [];
          }
          this.brandAggList = res.data.brandAggList || []
        }
      }
      this.loading = false;
    },
  },
  watch: {
    $route: {
      handler(newVal, oldVal) {
        this.queryData = this.$route.query
        delete this.queryData.otherParams
        if (!this.queryData.categoryId3) {
          this.categoryAggList[2] = undefined
          if (!this.queryData.categoryId2) {
            this.categoryAggList[1] = undefined
            if (!this.queryData.categoryId1) {
              this.categoryAggList[0] = undefined
            }
          }
        }
        if (!this.queryData.isSelected) {
          this.selectorList = []
        }
        if (!this.queryData.brandId) {
          this.brandAggList = []
        }
        this.pageIndex = 1
        this.getData()
      },
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.supplier-container {
  position: relative;
  background-color: #fff;
  margin: 20px 0 10px;

  .header-top {
    padding-top: 16px;
    padding-bottom: 16px;
    background: #fff;
    .header-wrap {
      display: flex;
      align-items: center;
    }
    .symbol {
      width: 587px;
      height: 54px;
      line-height: 54px;
      cursor: pointer;
      /deep/.t-image__wrapper {
        background-color: transparent;
      }
    }
    .search {
      width: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      .searchInput {
        width: 360px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid @primary-color;
        border-top-right-radius: 40px;
        border-bottom-right-radius: 40px;
        /deep/.t-input {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }
        .searchBtn {
          width: 60px;
          height: 40px;
          line-height: 40px;
          cursor: pointer;
          padding: 0 12px;
          border-top-right-radius: 18px;
          border-bottom-right-radius: 18px;
          color: #fff;
          background-color: @primary-color;
          letter-spacing: 0;
        }
      }
      .shoppingcar {
        width: 134px;
        height: 40px;
        margin-left: 20px;
        border: 1px solid #f22e00;
        color: #f22e00;
      }
    }
  }

  .supplier-block {
    position: relative;
    width: 100%;
    height: 96px;
    background-color: #f5f6f8;
    text-align: center;
    .supplier-image {
      width: 60px;
      height: 60px;
      object-fit: cover;
      margin-left: 16px;
    }
    .supplier-name {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-left: 16px;
      height: 60px;
      .name {
        display: flex;
        font-size: 18px;
        font-weight: bold;
      }
      .image {
        width: 66px;
        height: 20px;
        margin-left: 6px;
      }
      .phone {
        max-width: 200px;
        color: #999;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: left;
      }
    }
  }

  .nav {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: @primary-color;
    .nav-item {
      padding: 0 40px;
      font-size: 16px;
    }
  }
  .header-wrap {
    width: 1200px;
    height: 100%;
    margin: 0 auto 16px;
    display: flex;
    align-items: center;
  }

  .content-wrap {
    width: 1200px;
    margin: 0 auto 16px;
  }

  .bread {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin: 24px 0 12px;
    .segmentation {
      margin: 0 8px;
    }
    .selectCategory {
      width: 160px;
    }
  }
  .overview {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    height: 60px;
    border: 1px solid #e9ebf2;
    border-radius: 4px;
    .left {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-left: 24px;
      .address-comp {
        width: 300px;
        margin-right: 16px;
      }
      .order {
        width: 85px;
        margin: 0 6px;
        color: #333;
        cursor: pointer;
        &:hover {
          color: #e11b10;
        }
      }
      .prices {
        display: flex;
        align-items: center;
        .icon {
          margin-left: 8px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #666;
          font-size: 12px;
        }
      }
      .range {
        margin-left: 8px;
        width: 200px;
      }
      .confirm {
        border: none !important;
        margin-left: 16px;
      }
    }
    .right {
      width: 260px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .red {
        color: #e11b10;
        margin: 0 4px;
      }
      .text {
        margin-right: 16px;
      }
      .pagination {
        width: 80px;
      }
    }
  }
  .checked {
    color: #e11b10 !important;
  }
}
// 大学的主题
:root[theme-mode='110'] {
  .supplier-container {
    border-bottom: none;
    .nav {
      .nav-item {
        background-color: var(--td-brand-color-deep);
        color: var(--td-bg-color-1);
      }
      .checkTab {
        background-color: var(--td-bg-color-1);
        color: var(--td-brand-color-deep);
      }
    }
    .searchInput {
      border: 1px solid #fff;
      border-top-right-radius: 40px;
      border-bottom-right-radius: 40px;
      /deep/.t-input {
        border-radius: 0;
        border: 0;
        background-color: @primary-color;
        .t-input__inner {
          color: #fff;
          &::placeholder {
            color: #fff;
          }
        }
      }
    }
    .searchBtn {
      color: var(--td-brand-color-7) !important;
      background-color: var(--td-bg-color-1) !important;
    }
  }
}
</style>
<style lang="less">
@import "@/style/variables.less";
.t-badge--circle, .t-badge--round {
  background-color: @secondary-color !important;
}
</style>
