<template>
  <div class="type-nav">
    <div class="type-container">
      <!-- 品类 -->
      <div class="nav-list" v-showPopup="hidePopup">
        <transition name="sort">
          <div class="sort">
            <div class="all-sort-list" @click="goSearch">
              <div
                class="item"
                v-for="(item1, index) in categoryList.slice(0,10)"
                :key="item1.categoryId"
                :class="{ current: currentIndex == index }"
              >
                <div
                  @mouseenter="changeIndex(index, item1.categoryId)"
                  class="category-item"
                  :data-categoryName="item1.categoryName"
                  :data-categoryId1="item1.categoryId"
                >
                  <span class="icon-t" :data-categoryName="item1.categoryName" :data-categoryId1="item1.categoryId">
                    <span style="margin-right: 5px;margin-top: 5px;">
                      <img style="width: 16px; height:16px" :src="item1.icon" alt="" :data-categoryName="item1.categoryName" :data-categoryId1="item1.categoryId">
                    </span>{{ item1.categoryName }}
                  </span>
                  <span style="color: #999999;">></span>
                </div>
                <!-- 浮窗 -->
                <div
                  class="item-group clearfix"
                  v-if="curChildCategoryList.length > 0"
                  :style="{ display: currentIndex == index ? 'flex' : 'none' }"
                >
                  <div
                    v-for="item2 in curChildCategoryList"
                    :key="item2.categoryId"
                    class="category-popper-wrap"
                    :data-categoryName="item2.categoryName"
                    :data-categoryId2="item2.categoryId"
                  >
                    <div 
                      class="category2"
                      :data-categoryName="item2.categoryName"
                      :data-categoryId2="item2.categoryId"
                    >
                      {{ item2.categoryName }}
                    </div>
                    <template v-if="item2.childCategoryList && false">
                      <div
                        v-for="item3 in item2.childCategoryList"
                        :key="item3.categoryId"
                        class="category3"
                      >
                        <div
                          :data-categoryName="item3.categoryName"
                          :data-categoryId3="item3.categoryId"
                        >
                          {{ item3.categoryName }}
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </transition>
      </div>
      <!-- 轮播 -->
      <div class="img-swiper">
        <t-swiper :duration="300" :interval="5000">
          <t-swiper-item v-for="(item, index) in comAdvPosition" :key="index">
            <div @click="handleAdvPosition(item)">
              <t-image fit="cover" class="swiper-img" :src="item.imgUrl" />
            </div>
          </t-swiper-item>
        </t-swiper>
      </div>
      <div class="info">
        <div class="user-info" v-if="!userInfo || !userInfo.nickname">
          <!-- <div class="hello">Hi~ 您好 <template v-if="nickname"> {{ nickname }}</template></div> -->
          <div class="hello"><img src="@/assets/pic_userImag2.png" alt=""></div>
          <div style="margin-top:5px">欢迎登录武汉大学工会会员福利平台！</div>
          <div class="btn-list">
            <t-button shape="round" @click="toLogin">统一身份认证</t-button>
            <t-button shape="round" @click="toLogin(1)">附属单位用户</t-button>
          </div>
        </div>
        <div class="user-info" v-if="userInfo && userInfo.nickname">
          <!-- <div class="hello">Hi~ 您好 <template v-if="nickname"> {{ nickname }}</template></div> -->
          <div class="hello"><img src="@/assets/<EMAIL>" alt="" style="width: 72px; height:72px"></div>
          <div>您好，<span style="color: #f22e00;">{{ nickname }}</span></div>
          <div style="margin-top:5px">欢迎登录武汉大学工会会员福利平台！</div>
          <div style="font-weight: bold;margin-top: 5px;" v-for="account in accountInfoList" :key="account.type">
            {{ account.scoreType == 0 ? "福利": "扶贫" }}积分：<span style="color: #f22e00;font-weight: bold;
            font-size: 24px;">{{ account.availableAmount || 0 | formatMoney(2) }}</span>
          </div>
        </div>
        <div class="notice">
          <div class="notice-header">
            <div class="title">通知公告</div>
            <div class="more" @click="toInformation">查看更多</div>
          </div>
          <div v-for="(item, index) in latestList" @click="toInfo(item)" :key="index" class="notice-box">
            <div class="notice-item">
              <div class="point"></div>
              <div class="notice-content">{{ item.title }}</div>
            </div>
            <div class="notice-date">{{ item.publishTime | formatDate }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getRootCategoryList, getChildCategoryTreeList, getAdvPositionList, queryContentList } from '@/views/Home/api'
import throttle from "lodash/throttle";
import { toLogin } from '@/utils/util'
import { mapState } from 'vuex'
import * as pointsApi from '@/views/Center/points/api'
export default {
  name: "TypeNav",
  data() {
    return {
      currentIndex: -1,
      categoryId: '',
      defaultAdPositionList: [
       { imgUrl: require("./images/swiper1-1.png") },
       { imgUrl: require("./images/swiper2-2.jpg") },
       { imgUrl: require("./images/swiper3-3.png") },
       { imgUrl: require("./images/swiper4.jpg") }
      ],
      advPositionList: [],
      latestList: [],
      categoryList: [],
      categoryChildMap: {},
      curChildCategoryList: [],
      accountInfoList: {}
    };
  },
  created() {
    this.getRootCategoryList()
    this.loadAdvPositionList()
    this.loadLatestList()
    setTimeout(() => {
      this.loadAccountInfo()
    }, 700)
  },
  methods: {
    async loadAccountInfo() {
      if(!this.userInfo.nickname) {
        return;
      }
      let res = await pointsApi.getMyAccount()
      if(res.code === 0) {
        this.accountInfoList = res.data || {}
      }
    },
    jumpExtra(obj) {
      let target = obj.isOutUrl ? '_blank' : '_self'
      window.open(obj.url, target)
    },
    toInfo(item) {
      this.$router.push(`/information/content?cid=${item.id}`);
    },
    toInformation() {
      this.$router.push('/information');
    },
    toLogin(type){
      toLogin(type)
    },
    async loadLatestList() {
      let params = {
        pageSize: 3,
        categoryType: 20
      }
      let res = await queryContentList(params)
      if(res.code === 0) {
        this.latestList = res.data.list || []
      } 
    },
    async loadAdvPositionList() {
      const res = await getAdvPositionList({type: 1})
      if (res.code === 0) {
        this.advPositionList = res.data || []
      } 
    },
    handleAdvPosition(item) {
      if (item.link) {
        window.open(item.link, '_blank')
      }
    },
    async getRootCategoryList() {
      const res = await getRootCategoryList()
      if (res.code === 0) {
        this.categoryList = res.data
      }
    },
    async queryChildCategory() {
      let parentId = this.categoryId
      const res = await getChildCategoryTreeList({
        parentCategoryId: this.categoryId
      })
      if (res.code === 0) {
        this.categoryChildMap[parentId] = res.data || []
        if(this.categoryId === parentId) {
          this.curChildCategoryList = res.data || []
        }
      }
    },
    hidePopup() {
      this.currentIndex = -1;
    },
    changeIndex: throttle(function (index, id) {
      this.currentIndex = index;
      this.categoryId = id
      if(!this.categoryChildMap[id]) {
        this.queryChildCategory()
      } else {
        this.curChildCategoryList = this.categoryChildMap[id]
      }
    }, 50),
    goSearch(e) {
      let { categoryid1, categoryid2, categoryid3 } = e.target.dataset;
      let location = { path: "/storeSearch" };
      let query = {};
      if (categoryid1) {
        query.categoryId1 = categoryid1;
      } else if(categoryid2) {
        let cate2 = this.curChildCategoryList.find(item => item.categoryId === parseInt(categoryid2))
        if(cate2) {
          query.categoryId1 = cate2.parentId;
        }
      }

      if (categoryid2) {
        query.categoryId2 = categoryid2;
      }
      if (categoryid3) {
        query.categoryId3 = categoryid3;
      }
      if (this.$route.params) {
        location.query = query;
        this.$router.push(location);
      }
    },
  },
  computed: {
    ...mapState({
      userInfo: state => state.User.userInfo
    }),
    extraMenuList() {
      let arr = this.$store.state.Home?.homeConfigData?.extraMenuList || []
      arr = arr.filter(x => x.position === 2)
      arr.sort((a,b) => a.sort - b.sort)
      return arr
    },
    title() {
      return this.$store.state.Home.configData.title
    },
    nickname() {
      if (this.userInfo) {
        return this.userInfo.nickname
      }
      return ''
    },
    comAdvPosition() {
      return this.advPositionList || []
    },
    childCategoryList() {
      if(this.categoryChildMap[this.categoryId]) {
        return  this.categoryChildMap[this.categoryId] || []
      }

      return []
    }
  }
};
</script>

<style lang="less" scpoed>
@import "@/style/variables.less";
.type-nav {
  width: 1200px;
  margin: 0 auto;
  background-color: #fafafa;
  position: relative;
  .icon-t{
    display: flex;
    justify-content: space-between; 
    align-items: center; 
  }
  .type-container {
    display: flex;
    .nav-list {
      position: relative;
      width: 190px;
      height: 470px;
      background-color: #fff;
      .sort {
        overflow: hidden;
        padding: 10px 0;
        height: 100%;
        color: #636363;
        overflow-y: auto;
        .all-sort-list {
          .item {
            .category-item {
              cursor: pointer;
              line-height: 33px;
              font-size: 14px;
              font-weight: 400;
              overflow: hidden;
              padding: 0 15px;
              margin: 0;
              color: #333;
              display: flex;
              justify-content: space-between;
              align-items: center;
            }
            &.current {
              background-color: lightblue;
            }
          }
          .item-group {
            display: none;
            position: absolute;
            width: 150px;
            height: 391px;
            padding: 0 16px;
            overflow-y: auto;
            background: #ffffff;
            border: 1px solid #ddd;
            top: 0;
            left: 190px;
            z-index: 9999;
            flex-direction: column;
            align-items: flex-start;
            .category-popper-wrap {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              width: calc(100% - 20px);
              flex-wrap: wrap;
              padding: 11px 0;
              gap: 4px;
              // border-bottom: 1px solid #999999;
              .category2 {
                word-break: keep-all;
                padding: 0 12px;
                font-weight: 300;
                // border-right: 1px solid #999999;
                cursor: pointer;
                &:hover {
                  color: @primary-color;
                }
              }
              .category3 {
                padding: 0 12px;
                border-right: 1px solid #999999;
                color: #999;
                cursor: pointer;
                &:hover {
                  color: @primary-color;
                }
                &:last-child {
                  border-right: none;
                }
              }
            }
          }
        }
      }
    }
    .img-swiper {
      width: 700px;
      height: 470px;
      margin-left: 10px;
      .swiper-img {
        width: 700px;
        height: 470px;
        cursor: pointer;
      }
    }
    .info {
      flex: 1;
      margin-left: 10px;
      background-color: #fff;
      .user-info {
        margin-top: 10px;
        height: 160px;
        position: relative;
        text-align: center;
        .hello {
          font-weight: 700;
          font-size: 16px;
        }
        .btn-list {
          margin: 0px 20px;
          margin-top: 14px;
          display: flex;
          justify-content: space-between;
        }
      }
      .notice {
        height: 286px;
        margin-top: 14px;
        background-color: #fff;
        .notice-header {
          height: 54px;
          padding: 0 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid rgba(153,153,153,0.6);
          .title {
            font-size: 15px;
            font-weight: 700;
            color: #F22E00;
          }
          .more {
            font-size: 14px;
            color: #999;
            cursor: pointer;
          }
        }
        .notice-box {
          padding: 0 20px;
          cursor: pointer;
          &:hover {
            .notice-content {
              color: @primary-color;
            }
          }
          .notice-item {
            margin-top: 10px;
            line-height: 20px;
            display: flex;
            align-items: center;
            .point {
              width: 6px;
              height: 6px;
              background: #F22E00;
              border-radius: 50%;
            }
            .notice-content {
              margin-left: 8px;
              width: 220px;
              font-size: 14px;
              color: #333;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }

  .sort-enter {
    height: 0;
  }

  .sort-enter-to {
    height: 461px;
  }

  .sort-enter-active {
    transition: all 1s linear;
  }
}
</style>
