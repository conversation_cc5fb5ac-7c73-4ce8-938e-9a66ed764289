import request from '@/base/service'
import { getRefreshToken } from '@/base/cookie'

const requestM = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_MEMBER_API
    }
  })
}

// 使用手机 + 密码登录
export function loginUser(data) {
  return requestM({
    url: '/auth/login',
    method: 'post',
    data
  })
}

// 使用手机 + 短信验证码
export function loginSmsCode(data) {
  return requestM({
    url: '/auth/sms-login',
    method: 'post',
    data
  })
}

// 发送短信验证码
export function sendSmsCode(data) {
  return requestM({
    url: '/auth/send-sms-code',
    method: 'post',
    data
  })
}

// 重置密码
export function resetPassword(data) {
  return requestM({
    url: '/auth/reset-password',
    method: 'post',
    data
  })
}

// 刷新访问令牌
export function refreshToken() {
  return requestM({
    url: `/auth/refresh-token?refreshToken=${ getRefreshToken() }`,
    method: 'post'
  })
}
