<template>
  <div>
    <t-dialog
      theme="info"
      header="温馨提示"
      confirmBtn="我知道了"
      :visible.sync="showTip"
      @confirm="confirmed"
      :cancelBtn="null"
      width="500px"
    >
      <div slot="body">
        <div style="padding: 5px;" v-html="configData.globalTipContent"></div>
      </div>
    </t-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: "GlobalTip",
  data() {
    return {
      ckey: 'jcymall-global-tip-ind',
      showTip: false
    };
  },
  computed: {
    ...mapState({
      configData: state => state.Home.configData
    })
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      let val = sessionStorage.getItem(this.ckey)
      this.showTip = !val && this.configData.globalTipSwitch && this.configData.globalTipContent
    },
    confirmed() {
      sessionStorage.setItem(this.ckey, '1')
      this.showTip = false
    },
  }
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";


</style>
