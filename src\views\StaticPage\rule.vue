<template>
  <div class="rule-box">
    <div class="header-wrapper">
      <div class="home-header">
        <div class="symbol" @click="jumpToHome">
          <t-image
            fit="cover"
            style="height: 45px; margin-right: 15px;"
            :src="styleConfigData.logoUrl || imgSrc"
          />
        </div>
        <div class="help">
          <span>帮助中心</span>
        </div>
      </div>
    </div>
    <div class="rule-wrapper">
      <div class="left">
        <t-menu :defaultValue="activeContentCode" :defaultExpanded="comExpanded" style="width: 100%;">
          <t-submenu value="ptgz" title="平台规则">
            <t-menu-item v-for="item in comContentList('ptgz')" :key="item.code" :value="item.code">
              <div @click="showContent(item)">{{ item.title }}</div>
            </t-menu-item>
          </t-submenu>
          <t-submenu value="cgrzn" title="采购人指南">
            <t-menu-item v-for="item in comContentList('cgrzn')" :key="item.code" :value="item.code">
              <div @click="showContent(item)">{{ item.title }}</div>
            </t-menu-item>
          </t-submenu>
          <t-submenu value="gyszn" title="供应商指南">
            <t-menu-item v-for="item in comContentList('gyszn')" :key="item.code" :value="item.code">
              <div @click="showContent(item)">{{ item.title }}</div>
            </t-menu-item>
          </t-submenu>
        </t-menu>
      </div>
      <div class="right" v-if="contentObj.content">
        <div v-html="contentObj.content"></div>
      </div>
    </div>
    <div class="rule-bottom-wrap">
      <span style="margin-right: 16px;">金采通</span>由金采通®提供平台技术支持
    </div>
  </div>
</template>
  
<script>
import { queryCategoryList, queryContentList, getContentByCode } from "@/views/Home/api";
import { mapState } from 'vuex'
export default {
  name: 'StaticRule',
  data() {
    return {
      imgSrc: require("./images/logo-whdx.png"),
      activeContentCode: '',
      categoryCodes: ['ptgz', 'cgrzn', 'gyszn'],
      categoryList: [],
      contentList: [],
      contentObj: {}
    };
  },
  computed: {
    ...mapState({
      styleConfigData: state => state.Home.styleConfigData
    }),
    comExpanded () {
      if (this.contentObj.id) {
        let category = this.categoryList.find(item => item.id === this.contentObj.categoryId)
        if(category) {
          return [category.categoryCode]
        }
      }

      return []
    }
  },
  mounted() {
    let ccode = this.$route.query.ccode
    if (ccode) {
      this.activeContentCode = ccode
      this.showContent({code: ccode})
    }
  },
  methods: {
    async loadCategoryList() {
      let params = {
        categoryCodeList: this.categoryCodes
      }
      let res = await queryCategoryList(params)
      if(res.code !== 0 || !res.data.length) {
        this.$message.error("内容分类编码无效");
        return;
      }
      this.categoryList = res.data
    },
    async loadContentList() {
      let params = {
        categoryCodeList: this.categoryCodes
      }
      let res = await queryContentList(params)
      if(res.code !== 0 || !res.data.total) {
        return;
      }
      this.contentList = res.data.list
    },
    async showContent(obj) {
      let res = await getContentByCode(obj.code)
      if(res.code !== 0 || !res.data) {
        this.$message.error("内容编码不存在");
        return;
      }
      this.contentObj = res.data
    },
    comContentList (categoryCode) {
      if (categoryCode) {
        let category = this.categoryList.find(item => item.categoryCode === categoryCode)
        if(category) {
          return this.contentList.filter(item => item.categoryId === category.id)
        }
      }

      return []
    },
    jumpToHome() {
      this.$router.push("/home");
    }
  },
  created() {
    this.loadCategoryList();
    this.loadContentList();
  }
};
</script>
  
<style lang="less" scoped>
@import "@/style/variables.less";

.header-wrapper {
  width: 100%;
  position: relative;
  border-bottom: 1px solid #eee;
  background-color: var(--td-bg-color-1);
  /deep/.t-image__wrapper {
    background-color: transparent;
  }
}

.home-header {
  width: 1200px;
  height: 106px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  .symbol {
    cursor: pointer;
  }
  .help {
    border-left: 1px solid #eee;
    padding-left: 16px;
    font-size: 20px;
    line-height: 82px;
    margin: 12px 0;
  }
}

.rule-wrapper {
  display: flex;
  position: relative;
  padding: 30px 20px 20px 20px;
  width: 1200px;
  margin: 0 auto;
  .left {
    width: 265px;
    max-height: 500px;
    position: relative;
    overflow-x: hidden;
    background-color: #FFFFFF;
    margin-right: 24px;
    border: 1px solid #eee;
    /deep/.t-menu__item {
      padding-left: 16px!important;
    }
    /deep/.t-menu__item.t-is-active {
      color: @primary-color!important;
    }
    /deep/.t-submenu > .t-menu__item {
      .t-menu__content {
        color: #000;
      }
    }
    .category-item {
      width: 100%;
      height: 60px;
      line-height: 60px;
      border: 1px solid #eee;
      border-top: 0;
      padding-left: 12px;
      cursor: pointer;
      &:hover {
        color: @primary-color;
      }
    }
  }
  .right {
    flex: 1;
    overflow-y: auto;
    padding: 0 16px;
    /deep/img {
      max-width: 800px!important;
      object-fit: fill!important;
    }
  }
}
.rule-box {
  .rule-bottom-wrap {
    bottom: 0px;
    height: 40px;
    width: 100%;
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-top: 1px solid #eee;
  }
}

// 大学的主题
#college-wrap {
  .header-wrapper {
    background-color: var(--td-brand-color-7);
  }
  .home-header {
    .help {
      color: var(--td-bg-color-1);
    }
  }
}
</style>
  