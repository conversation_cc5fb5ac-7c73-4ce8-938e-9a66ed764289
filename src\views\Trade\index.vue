<template>
  <div class="trade-container" v-loading="loading">
    <div class="title">填写并核对订单信息</div>
    <div class="content">
      <div class="receive">
        <div>收件人信息 <span class="address-tip">（如需送货上门，请提供详细的地址）</span></div>
        <div @click="addAddress" class="add-address">新增收货地址</div>
      </div>
      <div
        class="address"
        v-for="address in showAddressInfo"
        :key="address.id"
        @click="changeCheckedAddress(address, addressInfo)"
      >
        <span class="username" :class="{ selected: address.checked }">
          {{ address.name }} {{ address.provinceName }}
        </span>
        <div class="user-content">
          <div class="s1">
            {{ address.provinceName }}{{ address.cityName }}{{ address.countyName }}{{ address.townName }}{{ address.consigneeAddress }}
          </div>
          <div class="s2">
            {{ address.mobile.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2") }}
          </div>
          <div class="s3" v-show="address.defaulted">默认地址</div>
        </div>
      </div>
      <div class="more-address" @click="showMore = !showMore">
        更多地址
        <chevron-down-double-icon v-if="!showMore" />
        <chevron-up-double-icon v-else />
      </div>
      <div class="line"></div>
      <div class="detail">
        <div class="title">
          <div>商品清单</div>
          <div @click="cartBack" class="goback" v-if="!tradeNowParam.skuId">返回修改采购车</div>
        </div>
        <div v-for="shop in orderInfo" :key="shop.supplierId">
          <t-card :bordered="false" :title="shop.supplierName" shadow style="margin-top:20px;">
            <template #footer>
              <div class="s-price">
                <span v-if="shop.shopSkuPrice < getSupplierSaleAmountMin(shop.supplierId)">起售金额：￥{{ getSupplierSaleAmountMin(shop.supplierId) }}</span>
                <span v-if="shop.shopFreight">运费：￥{{ shop.shopFreight || 0 | formatMoney }} (满 ￥{{ getSupplierFreightThreshold(shop.supplierId) }} 包邮)</span>
                <span v-else>运费：包邮</span>
                <span>商品金额：￥{{ shop.shopSkuPrice | formatMoney}}</span>
              </div>
            </template>
            <div class="list" v-for="order in shop.skuItems" :key="order.skuId">
              <t-image v-if="order.picUrl" class="image" :src="order.picUrl"></t-image>
              <div class="list-name">
                <div>{{ order.skuName }}</div>
                <div class="info-text">7天无理由退货</div>
              </div>
              <div class="price">￥{{ order.skuPrice | formatMoney }}</div>
              <div class="sku-num">X{{ order.count }}</div>
              <div class="has-goods" v-if="order.stock && $bus.isStockValid(order.stock.stockStateType)">{{ order.stock.stockStateType | stockStateText }}</div>
              <div class="has-goods" v-else>无货</div>
            </div>
          </t-card>
        </div>
      </div>
      <div class="pay">支付方式</div>
      <div class="bill">
        <t-space>
          <t-radio-group v-model="paymentMethod">
            <t-radio :value="6" v-if="paymentMethodList.includes(6)" :disabled="mixPay">积分支付</t-radio>
            <t-radio :value="7" v-if="paymentMethodList.includes(7) && mixPay">混合支付</t-radio>
            <t-radio :value="5" v-if="paymentMethodList.includes(5)">线上支付</t-radio>
          </t-radio-group>
          <t-alert theme="error" v-if="!paymentMethodList.length"> 支付方式未配置，请联系管理员! </t-alert>
        </t-space>
        
        <div v-for="item in scoreDetail" :key="item.scoreType" style="font-size: 0.9em;color: gray;margin-top:5px;">{{ item.scoreType == 0 ? '福利' : '扶贫' }}可用积分：{{ item.availableScore || 0 | formatMoney }}</div>
      </div>
      <div class="bbs">
        <div class="title">买家留言：</div>
        <t-textarea
          v-model="remarks"
          placeholder="建议留言前先与商家沟通确认"
          :maxlength="100"
          name="remarks"
          class="remarks-cont"
          :autosize="{ minRows: 3, maxRows: 5 }"
        />
      </div>
      <!-- <div class="line"></div>
      <div class="pay">发票信息</div>
      <div class="bill">
        <div>普通发票（电子） 个人 商品明细</div>
      </div>
      <div class="line"></div>
      <div class="pay">使用优惠/抵用</div>
      <div class="coupon"></div> -->
    </div>
    <div class="money">
      <div class="money-info">
        <div class="info1">
          共 <span class="red">{{ detailRespVO.selectedCount }}</span>
          件商品，总商品金额：
        </div>
        <div class="info2 red">¥{{ detailRespVO.payPrice | formatMoney }}</div>
      </div>
      <div class="money-info">
        <div class="info1">运费：</div>
        <div class="info2 red">¥{{ totalFreight || 0 | formatMoney }}</div>
      </div>
    </div>
    <div class="trade" v-if="showTradeDiv">
      <div v-for="item in scoreDetail" :key="item.scoreType" class="price">
        {{item.scoreType == 0 ? '福利' : '扶贫' }}积分抵扣:<span style="margin-left: 6px;">¥{{ item.payScore | formatMoney }}</span>
      </div>
      <div class="price" v-if="totalRemainingAmount > 0">
        应付金额:<span style="margin-left: 6px;">¥{{ totalRemainingAmount | formatMoney }}</span>
      </div>
      <div class="receiveInfo" v-if="lastSelectedAddress.name">
        寄送至:
        <span>{{ lastSelectedAddress.fullAddress }}</span>
        收货人：<span>{{ lastSelectedAddress.name }}</span>
        <span>{{ lastSelectedAddress.mobile && lastSelectedAddress.mobile.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2") }}</span>
      </div>
    </div>
    <div class="sub" v-if="showOrderSubmit">
      <t-button theme="primary" :disabled="submitDisabled" class="subBtn" @click="submitOrder">提交订单</t-button>
    </div>
    <add-address v-if="visible" :closeNotShow="closeNotShow" @successOpt="successOpt" @closeDialog="closeDialog"></add-address>

    <t-dialog theme="info" header="温馨提示" :visible.sync="freightFeeTip" @confirm="freightFeeTip = false" :cancelBtn="null" >
      <div slot="body">
        <div>由于订单未达到包邮金额，若此单商品需要退款产生的运费将无法退还，烦请知悉！</div>
        <br />
        <div v-for="(shop, key) in orderInfo" :key="key">
          <div v-if="shop.shopFreight">{{ shop.supplierName }}：满 {{ getSupplierFreightThreshold(shop.supplierId) }} 元包邮</div>
        </div>
      </div>
    </t-dialog>

    <t-dialog theme="info" header="温馨提示" :visible.sync="saleAmountMinTip" @confirm="saleAmountMinTip = false" :cancelBtn="null" >
      <div slot="body">
        <div>订单金额小于供应商起售金额，请您调整商品后重试！</div>
        <div v-for="(msg, key) in saleAmountMinErrors" :key="key">
          <div>{{msg}}</div>
        </div>
      </div>
    </t-dialog>

  </div>
</template>

<script>
import { checkOrder } from '@/views/ShopCart/api'
import { getList } from '@/views/Center/address/api'
import { createOrder } from '@/views/Trade/api'
import addAddress from '@/views/Center/address/add.vue'
import { ChevronDownDoubleIcon, ChevronUpDoubleIcon} from "tdesign-icons-vue";
import { mapState } from 'vuex'
export default {
  name: "Trade",
  data() {
    return {
      visible: false,
      orderId: "100",
      payType: 0,
      sendType: 0,
      paymentMethod: null,
      remarks: "",
      addressInfo: [],
      totalPayPrice: 0,
      orderInfo: {},
      totalFreight: 0,
      loading: false,
      showMore: false,
      detailRespVO: {},
      freightFeeTip: false,
      scoreDetail: [],
      mixPay: false,
      canPurchase: false,
      totalRemainingAmount: 0,
      saleAmountMinTip: false,
      saleAmountMinErrors: [],
      submitDisabled: true,
      tradeNowParam: {
        skuId: null,
        count: 1,
        supplierId: null
      }
    };
  },
  components: {
    addAddress,
    ChevronDownDoubleIcon,
    ChevronUpDoubleIcon
  },
  created() {
    if(this.$route.params.skuId) {
      this.tradeNowParam.skuId = this.$route.params.skuId
      this.tradeNowParam.count = this.$route.query.num
      this.tradeNowParam.supplierId = this.$route.query.supplierId
    }
    this.getList()
  },
  watch: {
    // 监听可用支付方式变化，自动设置默认支付方式
    availablePaymentMethods: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          // 如果当前选中的支付方式不在可用列表中，清空选择
          if (this.paymentMethod !== null && !newVal.includes(this.paymentMethod)) {
            this.paymentMethod = null
          }
          // 如果没有选中任何支付方式，设置默认值
          if (this.paymentMethod === null) {
            this.setDefaultPaymentMethod()
          }
        } else if (newVal && newVal.length === 0) {
          // 如果没有可用的支付方式，清空当前选择
          this.paymentMethod = null
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapState({
      configData: state => state.Home.configData,
      supplierList: state => state.Home.supplierList
    }),
    paymentMethodList() {
      let payMethods= this.configData.payMethod || ''
      payMethods = payMethods.split(',') || []
      payMethods = payMethods.filter(item => !!item).map(item => parseInt(item))
      return payMethods
    },
    // 计算属性：获取真正可用的支付方式
    availablePaymentMethods() {
      const available = []
      if (this.paymentMethodList.includes(6) && (this.canPurchase && !this.mixPay)) {
        available.push(6) // 积分支付
      }
      if (this.paymentMethodList.includes(7) && this.mixPay && this.canPurchase) {
        available.push(7) // 混合支付
      }
      if (this.paymentMethodList.includes(5)) {
        available.push(5) // 线上支付
      }
      return available
    },
    // 计算属性：是否显示trade div
    showTradeDiv() {
      return this.paymentMethod !== null && this.availablePaymentMethods.length > 0
    },
    showOrderSubmit() {
      return this.configData.orderSwitch || this.configData.orderSwitch === undefined
    },
    lastSelectedAddress() {
      let result = this.addressInfo.find((item) => item.checked)
      if (!result) {
        result = this.addressInfo[0] || {}
      }
      result.fullAddress = result.provinceName + result.cityName + result.countyName + (result.townName || '') + result.consigneeAddress
      return result
    },
    showAddressInfo() {
      if (this.showMore) {
        return this.addressInfo
      }
      return this.addressInfo.slice(0, 1)
    },
  },
  methods: {
    // 设置默认支付方式
    setDefaultPaymentMethod() {
      if (this.availablePaymentMethods.length > 0 && this.paymentMethod === null) {
        this.paymentMethod = this.availablePaymentMethods[0]
      }
    },
    getSupplierFreightThreshold(sid) {
      let supplier = this.supplierList.find(item => item.id === sid)
      if(supplier) {
        return supplier.freightThreshold || 0
      }

      return null
    },
    getSupplierSaleAmountMin(sid) {
      let supplier = this.supplierList.find(item => item.id === sid)
      if(supplier) {
        return supplier.saleAmountMin || 0
      }

      return null
    },
    validateOrderSaleAmountMin() {
      if(this.orderInfo) {
        this.saleAmountMinErrors = []
        this.saleAmountMinTip = false
        this.orderInfo.forEach(shop => {
          let saleAmountMin = this.getSupplierSaleAmountMin(shop.supplierId)
          if(shop.shopSkuPrice < saleAmountMin) {
            this.saleAmountMinErrors.push(`不满足${shop.supplierName}的起售金额￥${saleAmountMin}`)
          }
        }) 
        if(this.saleAmountMinErrors.length) {
          this.saleAmountMinTip = true
          return false
        }
      }

      return true
    },
    successOpt() {
      this.getList()
      this.closeDialog()
    },
    closeDialog() {
      this.visible = false
    },
    addAddress() {
      this.visible = true
    },
    changePayType(type) {
      this.payType = type;
    },
    changeSendType(type) {
      this.sendType = type;
    },
    changeCheckedAddress(address, addressInfo) {
      addressInfo.forEach((item) => (item.checked = false))
      address.checked = true
      this.checkOrder()
    },
    async submitOrder() {
      if(!this.validateOrderSaleAmountMin()) {
        return
      }
      if(!this.paymentMethod) {
        this.$message.warning('请选择支付方式！')
        return
      }
      if([6, 7].includes(this.paymentMethod) && !this.canPurchase) {
        this.$message.warning(this.reason ? this.reason : '无法下单，请联系平台技术支持')
        return
      }
      this.createOrder()
    },
    async getList() {
      const res = await getList()
      if (res.code === 0 && res.data.length > 0) {
        this.addressInfo = res.data.map(x => {
          return {
            ...x,
            checked: x.defaulted
          }
        })
        this.checkOrder()
      }
      if (!res.data || res.data.length === 0) {
        this.addressInfo = []
        this.visible = true
        this.closeNotShow = true
      }
    },
    async checkOrder() {
      if(!this.lastSelectedAddress || !this.lastSelectedAddress.id) {
        this.$message.warning('请选择收货地址！')
        return
      }
      this.loading = true
      try {
        let params = {
          addressId: this.lastSelectedAddress.id
        }
        if(this.tradeNowParam.skuId) {
          params.item = this.tradeNowParam
        }
        const res = await checkOrder(params)
        if (res.code === 0) {
          this.orderInfo = res.data.detailRespVO.shopItems
          this.totalFreight = res.data.totalFreight
          this.totalPayPrice = res.data.totalPayPrice
          this.totalRemainingAmount = res.data.totalRemainingAmount
          this.detailRespVO = res.data.detailRespVO
          this.freightFeeTip = this.totalFreight > 0
          this.scoreDetail = res.data.scoreDetail
          this.canPurchase = res.data.canPurchase
          this.reason = res.data.reason
          this.mixPay = false
          this.scoreDetail.forEach(item => {
            if(item.scoreType === 0 && item.payPrice !== 0) {
              this.mixPay = true
            }
          })

          this.submitDisabled = this.totalPayPrice <= 0
          this.checkOrderInfo(res.data.detailRespVO)
          // 设置默认支付方式
          this.setDefaultPaymentMethod()
          if(this.totalPayPrice <= 0) {
            this.$message.error('请选择商品')
          }
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    },
    checkOrderInfo(detailRespVO) {
      let flag = false
      let self = this
      if(detailRespVO && detailRespVO.shopItems) {
        detailRespVO.shopItems.forEach(shopItem => {
          if(flag) {
            return
          }
          if(shopItem.skuItems) {
            shopItem.skuItems.forEach(skuItem => {
              if(flag) {
                return
              }
              flag = !skuItem.stock || !this.$bus.isStockValid(skuItem.stock.stockStateType)
              if(flag) {
                let msg =`商品${ skuItem.skuName }无货，请您稍后重试或选择其它商品` 
                let dialog = this.$dialog({
                  closeBtn: false,
                  theme: 'danger',
                  header: '错误提示',
                  cancelBtn: null,
                  closeOnEscKeydown: false,
                  closeOnOverlayClick: false,
                  body: msg,
                  onConfirm() {
                    self.$router.push('/shopcart')
                    dialog.hide()
                  }
                })
              }
            })
          }
        })
      }
      this.submitDisabled = flag
    },
    cartBack() {
      this.$router.go(-1)
    },
    async createOrder() {
      this.loading = true
      try {
        const shopList = this.orderInfo.map(item => {
          const items = item.skuItems.map(x => {
            return {
              skuId: x.skuId,
              count: x.count,
              supplierId: item.supplierId
            }
          })
          return {
            supplierId: item.supplierId,
            items
          }
        })
        const res = await createOrder({
          timestamp: new Date().getTime(),
          addressId: this.lastSelectedAddress.id,
          paymentMethod: this.paymentMethod,
          remark: this.remarks,
          fromCart: true,
          shopItems: shopList
        })
        if (res.code === 0) {
          let query = {
            no: res.data.no,
            oType: 0
          }
          if(res.data.subOrders && res.data.subOrders.length > 1) query.oType = 1
          let routePath = '/trade-success'
          if([5,7].includes(this.paymentMethod)) {
            routePath = '/pay'
          }
          this.$router.replace({
            path: routePath,
            query: query
          })
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    }
  },
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.trade-container {
  position: relative;
  width: 1200px;
  margin: 0 auto;
  padding: 16px 0;
  .title {
    font-size: 14px;
    line-height: 21px;
  }
  .address-tip {
    font-size: 0.9em;
    font-weight: 500;
  }
  .content {
    margin: 10px auto 0;
    border: 1px solid rgb(221, 221, 221);
    padding: 0 25px;
    box-sizing: border-box;

    .receive,
    .pay {
      line-height: 48px;
    }

    .receive {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .add-address {
        color: #165dff;
        cursor: pointer;
      }
    }

    .address {
      display: flex;
      padding-left: 20px;
      margin-bottom: 20px;

      .username {
        height: 32px;
        line-height: 32px;
        padding: 0 16px;
        text-align: center;
        border: 1px solid #ddd;
        cursor: pointer;
        position: relative;
      }

      .username::after {
        content: "";
        display: none;
        width: 13px;
        height: 13px;
        position: absolute;
        right: 0;
        bottom: 0;
        background: url(./images/choosed.png) no-repeat;
      }

      .username.selected {
        border-color: @primary-color;
      }

      .username.selected::after {
        display: block;
      }

      .user-content {
        width: 610px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 32px;
        margin-left: 6px;
        padding-left: 6px;
        cursor: pointer;
        &:hover {
          background-color: #ddd;
        }

        .s2 {
          margin: 0 10px;
        }

        .s3 {
          display: block;
          padding: 0 6px;
          background-color: #878787;
          color: #fff;
          text-align: center;
        }
      }
    }

    .more-address {
      color: rgba(0, 0, 0, 0.6);
      font-size: 12px;
      padding-left: 20px;
      cursor: pointer;
      margin-bottom: 12px;
    }

    .line {
      height: 1px;
      background-color: #ddd;
    }

    .way {
      background: #f4f4f4;
      padding: 16px;
      margin: 0 auto;
      .title {
        margin-bottom: 16px;
      }
      .sent-time {
        margin-top: 16px;
      }
    }

    .detail {
      margin: 12px auto;
      .title {
        margin: 16px 16px 4px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .goback {
          color: #165dff;
          cursor: pointer;
        }
      }
      .trade-shop {
        margin-top: 12px;
        margin-bottom: 6px;
        padding: 0 16px;
      }
      .list {
        padding: 16px 16px 12px;
        background-color: #feedef;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .image {
          width: 100px;
          height: 100px;
        }
        .list-name {
          flex: 1;
          margin-left: 24px;
          .info-text {
            color: #584fdf;
          }
        }

        .price {
          font-size: 14px;
          margin: 0 24px;
          color: @primary-color;
        }
        .sku-num {
          margin: 24px;
        }
      }
    }

    .bbs {
      margin-bottom: 16px;
      .title {
        margin-bottom: 16px;
      }
      .remarks-cont {
        width: 100%;
        border-color: #e4e2e2;
        line-height: 1.8;
        outline: none;
        resize: none;
      }
    }
    .bill {
      margin-bottom: 16px;
    }

    .coupon {
    }
  }
  .money {
    margin: 16px auto;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
    .money-info {
      display: flex;
      align-items: center;
      height: 32px;
      .info1 {
        min-width: 160px;
        text-align: right;
      }
      .info2 {
        text-align: right;
      }
      .red {
        color: @primary-color;
      }
    }
  }

  .trade {
    box-sizing: border-box;
    padding: 16px;
    margin: 10px auto;
    text-align: right;
    background-color: #f4f4f4;
    border: 1px solid #ddd;

    div {
      line-height: 30px;
    }

    .price span {
      color: #e12228;
      font-weight: 700;
      font-size: 14px;
    }

    .receiveInfo {
      color: #999;
    }
  }

  .sub {
    margin: 12px auto;
    text-align: right;
    .subBtn {
      width: 200px;
      margin-right: 0;
    }
  }
}
</style>
<style lang="less">

.trade-container {
  .s-price {
    float: right;
    padding: 5px 10px 10px;
    font-size: 0.95em;
    span {
      margin: 0 20px;
    }
  }
  .t-card__header {
    padding: 10px 10px 0;
    .t-card__title {
      font-size: 1em !important;
    }
  }
  .t-card__footer {
    padding: 0;
  }
  .t-card__body {
    padding: 10px 24px;
  }
}

</style>

