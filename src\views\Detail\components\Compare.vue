<template>
  <div class="compare-block">
    <div class="header">
      <div class="tab-title">对比栏</div>
      <div class="toHidden" @click="toHidden">隐藏</div>
    </div>
    <div class="bottom">
      <div class="product-item" v-for="item in 4" @click="toDetail(productList[item - 1])">
        <template v-if="productList[item - 1]">
          <div class="image">
            <img :src="productList[item - 1].imageUrl" width="50" height="50"/>
          </div>
          <div class="content">
            <div class="name">
              {{ productList[item - 1].skuName }}
            </div>
            <div class="salePrice">
              <div class="price">￥{{ productList[item - 1].salePrice }}</div>
              <div class="del" @click.stop="deleteItem(item)">删除</div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="image">
            <div class="image-empty">{{ item }}</div>
          </div>
          <div class="name-empty">
            您还可以继续添加
          </div>
        </template>
      </div>
      <div class="last-operation">
        <a class="compare-active" :class="{'one-goods': productList.length < 2}" @click="toCompareTable">对比</a>
        <a class="del-items" @click="delItems">清空对比栏</a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "detailInfo",
  data() {
    return {
      productList: []
    };
  },
  created() {
    this.loadProductList()
  },
  methods: {
    toHidden() {
      localStorage.setItem('showCompare', false)
      this.$emit('compareHidden', true)
    },
    toCompareTable() {
      if (this.productList.length < 2) {
        this.$message.info('至少有两件商品才能对比哦！')
        return
      }
      let ctxPath = process.env.VUE_APP_PATH_CONTEXT
      window.open(location.origin + ctxPath + '/#/compare', '_blank')
    },
    loadProductList() {
      const arr = JSON.parse(localStorage.getItem('compareProduct')) || []
      this.productList = arr
    },
    delItems() {
      this.productList = []
      localStorage.setItem('compareProduct', '[]')
    },
    addItem(item) {
      this.loadProductList()
      const i = this.productList.findIndex(x => x.skuId === item.skuId)
      if (i === -1) {
        if (this.productList.length === 4) {
          this.$message.error('对比栏已满，您可以删除不需要的栏内商品再继续添加哦！')
          return
        }
        this.productList.push(item)
      } else {
        this.productList.splice(i, 1)
      }
      localStorage.setItem('compareProduct', JSON.stringify(this.productList))
    },
    toDetail(item) {
      if (!item) {
        return
      }
      let ctxPath = process.env.VUE_APP_PATH_CONTEXT
      window.open(location.origin + ctxPath + `/#/sku/${item.skuId}`, '_blank')
    },
    deleteItem(skuId) {
      this.loadProductList()
      let index = this.productList.findIndex(item => item.skuId === skuId)
      this.productList.splice(index - 1, 1)
      localStorage.setItem('compareProduct', JSON.stringify(this.productList))
    }
  },
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.compare-block {
  position: fixed;
  display: block;
  bottom: 0;
  right: 50%;
  margin-right: -606px;
  z-index: 100;
  width: 990px;
  height: 139px;
  background: #fff;
  -moz-box-shadow: 0 0 15px rgba(221, 221, 221, 0.8);
  -webkit-box-shadow: 0 0 15px rgba(221, 221, 221, 0.8);
  box-shadow: 0 0 15px rgba(221, 221, 221, 0.8);
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 38px;
    border: 1px solid #ddd;
    border-bottom: 2px solid #7abd54;
    .tab-title {
      position: relative;
      left: -1px;
      margin-top: 2px;
      background-color: #fff;
      height: 37px;
      line-height: 37px;
      padding: 0 12px;
      text-align: center;
      border: 2px solid #7abd54;
      border-bottom: 0;
    }
    .toHidden {
      color: #005aa0;
      padding: 0 12px;
      cursor: pointer;
    }
  }
  .bottom {
    display: flex;
    height: 101px;
    padding: 6px;
    border: 2px solid #7abd54;
    border-top: none;
    .product-item {
      width: 204px;
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 10px;
      border-right: 1px dotted #7abd54;
      cursor: pointer;
      .image {
        width: 50px;
        margin-right: 4px;
        .image-empty {
          width: 48px;
          height: 48px;
          text-align: center;
          color: #ccc;
          background-color: #f6f6f6;
          border: 1px solid #fff;
          font-size: 36px;
          overflow: hidden;
        }
      }
      .content {
        flex: 1;
        .name {
          width: 100%;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          color: #333;
          font-size: 12px;
        }
        .salePrice {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 16px;
          margin-top: 2px;
          .price {
            color: #e4393c;
            font-size: 14px;
            font-weight: bold;
          }
          .del {
            display: none;
            color: #333;
            font-size: 12px;
            padding: 0 4px;
            line-height: 16px;
            &:hover {
              color: #e4393c;
            }
          }
        }
      }
      .name-empty {
        position: relative;
        top: -14px;
        color: #ccc;
        font-size: 12px;
      }
      &:hover {
        .del {
          display: block!important;
        }
      }
    }
    .last-operation {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      .compare-active {
        display: block;
        width: 59px;
        height: 30px;
        line-height: 30px;
        border: none;
        color: #fff;
        background-color: #E74649;
        cursor: pointer;
        margin-bottom: 11px;
        border-radius: 3px;
        text-decoration: none;
      }
      .one-goods {
        background-color: #f2f3f5;
        color: #afa9a9;
      }
      .del-items {
        color: #005aa0;
        cursor: pointer;
        text-decoration: none;
      }
    }
  }
}
</style>
