<template>
  <div class="information-wrapper">
    <div class="image"></div>
    <div class="title-area">
      <div class="title">平台资讯</div>
      <t-tabs class="tabs" v-model="tabValue" @change="change">
        <t-tab-panel v-for="(item, index) in fixCategorys" :key="index" :value="item.code">
          <template #label>
            <span class="tabs-name">{{item.label}}</span>
          </template>
        </t-tab-panel>
      </t-tabs>
    </div>
    <div class="notice-content-wrapper">
      <filter-form class="form-wrap" :tabValue="tabValue" :categoryList="comCategoryList" @submit="filterSubmit"></filter-form>
      <div class="content-list" v-loading="loading" v-if="resultData.list && resultData.list.length > 0">
        <t-list style="max-height: 800px;" :split="true" size="large">
          <t-list-item v-for="(item,index) in resultData.list" :key="index" @click="toInfoContent(item)" class="list-item">
            <t-tag :theme="getCategoryName(item) | tagStyle">{{getCategoryName(item)}}</t-tag>
            <span style="margin-left: 8px;">{{item.title}}</span>
            <template #action>
              <div>{{item.publishTime | formatDate('yyyy/MM/dd')}}</div>
            </template>
          </t-list-item>
        </t-list>

        <t-pagination
          class="notice-pagination"
          v-if="resultData.list.length > 0 && resultData.pages > 1"
          :total="resultData.total"
          showJumper
          :pageSize="pageParams.pageSize"
          :totalContent="false"
          @current-change="onCurrentChange"
          :showPageSize="false" />
       </div>
       
       <div v-if="!resultData.list || resultData.list.length === 0" class="nothing">
        <t-image fit="cover" class="empty-image" :src="emptySrc"></t-image>
        <div class="nothing-text">暂无资讯</div>
       </div>
    </div>
  </div>
</template>
    
<script>
import { queryCategoryList, queryContentList } from '@/views/Home/api/index'
import FilterForm from './filterForm';

export default {
  name: 'InformationIndex',
  components: {
    FilterForm
  },
  data() {
    return {
      loading: false,
      fixCategorys: [
        { label: '全部公告', code: 'all', type: 20 },
        { label: '系统公告', code: 'xtgg', type: 21 },
        { label: '业务公告', code: 'ywgg', type: 22 }
      ],
      emptySrc: require('@/assets/nothing.png'),
      pageParams: {
        pageNum: 1,
        pageSize: 10
      },
      resultData: {
        total: 0,
        pages: 0,
        list: [] 
      },
      filterForm: {},
      tabValue: 'all',
      categoryList: [],
      banner: require("./images/banner.png"),
    };
  },
  filters: {
    tagStyle (val) {
      let warningKeywords = ['重要', '延期', '投诉', '废标', '变更']
      let ret = 'primary'
      warningKeywords.forEach(item => {
        if(val && val.indexOf(item) >= 0) {
          ret = 'warning'
        }
      })
      return ret
    }
  },
  computed: {
    curCategory1() {
      return this.fixCategorys.find(item => item.code === this.tabValue)
    },
    subCategoryList () {
      let codes = this.fixCategorys.map(item => item.code)
      return this.categoryList.filter(item => !codes.includes( item.categoryCode))
    },
    comCategoryList () {
      if(this.tabValue === 'all') {
        return this.subCategoryList
      }
      if(!this.curCategory1) return []
      let parentCategory = this.categoryList.find(item => item.categoryCode === this.curCategory1.code)
      return this.subCategoryList.filter(item => item.parentId === parentCategory.id)
    }
  },
  created() {
    this.loadCategoryList()
    this.searchContentList()
  },
  methods: {
    async loadCategoryList() {
      let params = {
        type: this.fixCategorys[0].type
      }
      let res = await queryCategoryList(params)
      if(res.code === 0) {
        this.categoryList = res.data || []
      }
    },
    async loadContentList() {
      this.buildParam4page()
      let params = Object.assign({}, this.pageParams)
      let res = await queryContentList(params)
      console.log('res===', res)
      if (res.code === 0) {
        this.resultData = res.data
      }
    },
    getCategoryName(item) {
      if(!this.categoryList || !this.categoryList.length) {
        return ''
      }
      let category = this.categoryList.find(ci => ci.id === item.categoryId)
      if(category) {
        return category.categoryName
      }
      return ''
    },
    buildParam4page() {
      let arr = []
      this.pageParams.categoryType = this.curCategory1 ? this.curCategory1.type : 20
      if (this.filterForm.noticeType && this.filterForm.noticeType.length) {
        arr = arr.concat(this.filterForm.noticeType)
      } 
      this.pageParams.title = this.filterForm.keyword
      this.pageParams.publishTime = this.filterForm.date

      if(!this.pageParams.title) delete this.pageParams.title
      if(!this.pageParams.publishTime) delete this.pageParams.publishTime

      this.pageParams.categoryCodeList = arr
    },
    onCurrentChange (val) {
      this.pageParams.pageNum = val
      this.loadContentList()
    },
    searchContentList() {
      this.pageParams.pageNum = 1
      this.loadContentList()
    },
    change() {
      this.searchContentList()
    },
    filterSubmit(form) {
      console.log("筛选form", form)
      this.filterForm = form || {}
      this.searchContentList()
    },
    toInfoContent(item) {
      this.$router.push(`/information/content?cid=${item.id}`);
    }
  }
};
</script>
    
<style lang="less" scoped>
@import "@/style/variables.less";

.information-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #F5F6F8;
  .image {
    width: 100%;
    height: 240px;
    background: url(./images/banner.png) no-repeat 50% 50% / cover;
  }
  .title-area {
    position: relative;
    top: -240px;
    z-index: 2;
    width: 1200px;
    height: 240px;
    box-sizing: border-box;
    padding-top: 86px;
    .title {
      font-size: 40px;
      font-weight: bold;
      color: #fff;
    }
    .tabs {
      margin-top: 60px;
      border-top-right-radius: 4px;
      border-top-left-radius: 4px;
      .tabs-name {
        width: 368px;
        font-size: 17px;
        text-align: center;
      }
    }
  }
  .notice-content-wrapper {
    width: 1200px;
    margin-top: -240px;
    padding: 24px 0;
    display: flex;
    .form-wrap {
      width: 287px;
      background: #fff;
      max-height: 630px;
    }
    .content-list {
      width: 897px;
      background: #fff;
      margin-left: 20px;
      min-height: 630px;
      .list-item {
        cursor: pointer;
        &:hover {
          color: @primary-color;
        }
      }
    }
  }
  .notice-pagination {
    margin: 20px 0;
    padding-right: 20px;
  }
  .nothing {
    width: 897px;
    margin-left: 20px;
    .empty-image {
      margin-top: 40px;
      margin-bottom: 24px;
      width: 360px;
    }
    .nothing-text {
      font-size: 20px;
      margin-bottom: 24px;
      color: #80808082;
    }
  }
}
</style>
    