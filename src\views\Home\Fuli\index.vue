<template>
  <div class="fuli-wrap">
    <div class="title">
      <div class="title-text">
        <img :src="itemData.titleIcon" class="title-icon" />
        {{ itemData.title }}
      </div>
      <div class="more" @click="jumpToStore">查看更多 <t-icon name="chevron-right" size="18px"/></div>
    </div>
    <div class="content">
      <div class="left">
        <img :src="itemData.image" class="left-image" />
      </div>
      <div class="right" v-loading="loading">
        <div v-for="(item, index) in productList" :key="index" class="category" @click="jumpDetail(item)">
          <div class="save">省{{ (item.marketPrice - item.salePrice) | formatMoney(0) }}元</div>
          <t-image :src="item.imageUrl" style="width: 234px; height: 192px"></t-image>
          <div class="content">
            <div class="item-title" :title="item.skuName">{{ item.skuName }}</div>
            <div class="price-wrap">
              <div class="special-price">
                <div>福利价</div>
                <div class="price">￥{{ saveTwoDecimal(item.salePrice) }}</div>
              </div>
              <div class="market-price">
                <div>市场价</div>
                <div class="price">￥{{ saveTwoDecimal(item.marketPrice) }}</div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="productList && !productList.length && !loading" class="nothing">
          <t-image fit="cover" class="image" :src="emptySrc"></t-image>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Icon } from 'tdesign-icons-vue';
import { goodsSearchSimpleList } from '@/views/Search/api'
import { mapState } from 'vuex'

export default {
  name: "Like",
  props: {
    config: {
      type: Object,
      default() {
        return {}
      }
    },
    itemData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      productList: [],
      emptySrc: require('@/assets/nothing.png'),
      loading: false
    };
  },
  computed: {
    ...mapState({
      defaultRegions: state => state.defaultAddress.defaultRegions
    })
  },
  components: {
    Icon
  },
  methods: {
    jumpToStore() {
      let paramName = this.itemData.categoryIdName
      let paramVal = this.itemData.categoryId
      if(!paramName || !paramVal) {
        console.log('参数名为空', paramName)
        return
      }

      this.$router.push(`/storeSearch?${paramName}=${paramVal}`);
    },
    // 保留两位小数
    saveTwoDecimal(val = 0) {
      return (Math.round(val * 100) / 100).toFixed(2)
    },
    async getData() {
      this.loading = true
      let param = {
        sortType: 1,
        pageIndex: 1,
        pageSize: 8,
        areaIds: this.defaultRegions.join(',')
      }
      param[this.itemData.categoryIdName] = this.itemData.categoryId
      const res = await goodsSearchSimpleList(param)
      if (res.code === 0) {
        this.productList  = res.data || []
      }
      this.loading = false
    },
    jumpDetail(item) {
      this.$router.push(`/sku/${item.skuId}`)
    }
  },
  created() {
    this.getData()
  }
};
</script>

<style lang="less" scoped>
@import "@/style/variables.less";

.fuli-wrap {
  width: 1200px;
  margin: 30px auto 0;
  height: 606px;
  background: #FFFFFF;

  .title {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 50px;
    align-items: center;
    padding: 0 5px;


    .title-text {
      color: #333;
      font-size: 20px;
      font-weight: 600;
      display: flex;
      align-items: center;
      .title-icon {
        width: 30px;
        height: 30px;
        margin-right: 4px;
      }
    }
    .more {
      color: #999;
      font-size: 14px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .content {
    width: 100%;
    background-color: #fff;
    display: flex;

    .left {
      background-color: #edeae8;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 560px;
      width: 255px;
      .left-image {
        width: 100%;
        height: 100%;

      }
    }

    .right {
      position: relative;
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      .category {
        position: relative;
        width: 236px;
        height: 280px;
        cursor: pointer;
        .save {
          position: absolute;
          background: url("@/views/Home/Fuli/image/rectange.png") no-repeat;
          background-size: 100%;
          top: 10px;
          left: 0;
          width: 86px;
          line-height: 20px;
          text-align: center;
          color: #fff;
          z-index: 10;
          font-size: 0.9em;
        }

        .content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .item-title {
            margin: 10px;
            height: 32px;
            line-height: 16px;
            font-size: 14px;
            color: #333;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            &:hover {
              color: #F22E00;
            }
          }
          .price-wrap {
            width: 100%;
            padding: 0 12px;
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            .special-price {
              color: #F22E00;
            }
            .market-price {
              color: #999999;
              .price {
                text-decoration: line-through;
              }
            }
          }
        }
      }
      .nothing {
        display: flex;
        flex: 1;
        justify-content: center;
        /deep/.t-image__wrapper {
          height: 200px;
          margin-top: 80px;
        }
      }
    }
  }
}</style>
