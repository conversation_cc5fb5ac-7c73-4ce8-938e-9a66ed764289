import request from '@/base/service'

const requestO = (data) => {
  return request({
    ...data,
    ...{
      baseURL: process.env.VUE_APP_TRADE_API
    }
  })
}

// 创建订单
export function createOrder(data) {
  return requestO({
    url: '/order/create',
    method: 'post',
    data
  })
}

// 获得订单交易分页
export function getOrderPage(params) {
  return requestO({
    url: '/order/page',
    method: 'get',
    params
  })
}

// 获得订单状态数量统计
export function getOrderStatusStats(params) {
  return requestO({
    url: '/order/get-order-stats',
    method: 'get',
    params
  })
}

// 获得交易订单
export function getDetailOrder(params) {
  return requestO({
    url: '/order/get-detail',
    method: 'get',
    params
  })
}

// 取消订单
export function cancelOrder(data) {
  return requestO({
    url: '/order/cancel-order',
    method: 'post',
    data
  })
}

// 删除订单
export function deleteOrder(orderNo) {
  return requestO({
    url: `/order/delete-order/${orderNo}`,
    method: 'post'
  })
}

// 支付订单
export function getPayOrder(data) {
  return requestO({
    url: `/order/payOrder`,
    method: 'post',
    data: data
  })
}

// 检查订单支付状态
export function checkOrderPayed(data) {
  return requestO({
    url: `/order/checkPayed`,
    method: 'post',
    data: data
  })
}

